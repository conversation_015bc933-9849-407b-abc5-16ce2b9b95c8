"""
监控数据API接口
提供召回监控数据的查询接口
"""

from flask import Blueprint, jsonify, request
from utils.logger.logger_util import logger

# 创建蓝图
monitor_bp = Blueprint('monitor', __name__)

# 导入监控模块
try:
    from monitor.recall_monitor import get_monitor
    MONITOR_ENABLED = True
except ImportError:
    logger.warning("监控模块未找到")
    MONITOR_ENABLED = False

@monitor_bp.route('/recall-monitor/leaderboard', methods=['GET'])
def get_leaderboard():
    """
    获取召回源排行榜
    
    Query Parameters:
        format: 排行榜格式 (simple, detailed, json), 默认为detailed
    """
    if not MONITOR_ENABLED:
        return jsonify({'error': '监控功能未启用'}), 503
    
    try:
        format_type = request.args.get('format', 'detailed')
        monitor = get_monitor()
        
        if format_type == 'json':
            return jsonify({
                'status': 'success',
                'data': monitor.leaderboard,
                'query_count': monitor.query_counter,
                'start_time': monitor.start_time.strftime('%Y-%m-%d %H:%M:%S')
            })
        else:
            leaderboard_text = monitor.get_leaderboard(format_type)
            return jsonify({
                'status': 'success',
                'data': leaderboard_text,
                'format': format_type
            })
    except Exception as e:
        logger.error(f"获取排行榜失败: {e}")
        return jsonify({'error': str(e)}), 500

@monitor_bp.route('/recall-monitor/statistics', methods=['GET'])
def get_statistics():
    """
    获取详细统计报告
    """
    if not MONITOR_ENABLED:
        return jsonify({'error': '监控功能未启用'}), 503
    
    try:
        monitor = get_monitor()
        report = monitor.get_statistics_report()
        return jsonify({
            'status': 'success',
            'data': report,
            'query_count': monitor.query_counter,
            'start_time': monitor.start_time.strftime('%Y-%m-%d %H:%M:%S')
        })
    except Exception as e:
        logger.error(f"获取统计报告失败: {e}")
        return jsonify({'error': str(e)}), 500

@monitor_bp.route('/recall-monitor/recent', methods=['GET'])
def get_recent_queries():
    """
    获取最近的查询记录
    
    Query Parameters:
        limit: 返回记录数量，默认10，最大100
    """
    if not MONITOR_ENABLED:
        return jsonify({'error': '监控功能未启用'}), 503
    
    try:
        limit = min(int(request.args.get('limit', 10)), 100)
        monitor = get_monitor()
        
        recent_queries = list(monitor.query_history)[-limit:]
        query_data = []
        
        for q in recent_queries:
            query_data.append({
                'query_id': q.query_id,
                'query_text': q.query_text,
                'timestamp': q.timestamp,
                'response_time': q.response_time,
                'rerank_count': q.rerank_count,
                'contributions': {
                    'es': {
                        'contribution_rate': q.es_metrics.contribution_rate,
                        'hit_count': q.es_metrics.hit_count,
                        'top1_count': q.es_metrics.top1_count
                    },
                    'milvus': {
                        'contribution_rate': q.milvus_metrics.contribution_rate,
                        'hit_count': q.milvus_metrics.hit_count,
                        'top1_count': q.milvus_metrics.top1_count
                    },
                    'kg': {
                        'contribution_rate': q.kg_metrics.contribution_rate,
                        'hit_count': q.kg_metrics.hit_count,
                        'top1_count': q.kg_metrics.top1_count
                    }
                }
            })
        
        return jsonify({
            'status': 'success',
            'data': query_data,
            'total_queries': monitor.query_counter
        })
    except Exception as e:
        logger.error(f"获取最近查询记录失败: {e}")
        return jsonify({'error': str(e)}), 500

@monitor_bp.route('/recall-monitor/hourly', methods=['GET'])
def get_hourly_stats():
    """
    获取小时统计数据
    
    Query Parameters:
        hours: 返回最近几小时的数据，默认24小时
    """
    if not MONITOR_ENABLED:
        return jsonify({'error': '监控功能未启用'}), 503
    
    try:
        hours = min(int(request.args.get('hours', 24)), 168)  # 最多7天
        monitor = get_monitor()
        
        # 获取最近N小时的数据
        recent_hours = sorted(monitor.hourly_stats.keys())[-hours:]
        hourly_data = {}
        
        for hour_key in recent_hours:
            stats = monitor.hourly_stats[hour_key]
            hourly_data[hour_key] = {
                'es': {
                    'hit_count': stats['es'].hit_count,
                    'top1_count': stats['es'].top1_count,
                    'total_score': stats['es'].total_score
                },
                'milvus': {
                    'hit_count': stats['milvus'].hit_count,
                    'top1_count': stats['milvus'].top1_count,
                    'total_score': stats['milvus'].total_score
                },
                'kg': {
                    'hit_count': stats['kg'].hit_count,
                    'top1_count': stats['kg'].top1_count,
                    'total_score': stats['kg'].total_score
                }
            }
        
        return jsonify({
            'status': 'success',
            'data': hourly_data,
            'hours_returned': len(hourly_data)
        })
    except Exception as e:
        logger.error(f"获取小时统计失败: {e}")
        return jsonify({'error': str(e)}), 500

@monitor_bp.route('/recall-monitor/reset', methods=['POST'])
def reset_statistics():
    """
    重置统计数据（谨慎使用）
    """
    if not MONITOR_ENABLED:
        return jsonify({'error': '监控功能未启用'}), 503
    
    try:
        # 可以添加权限验证
        # auth_token = request.headers.get('Authorization')
        # if not validate_admin_token(auth_token):
        #     return jsonify({'error': '权限不足'}), 403
        
        monitor = get_monitor()
        old_count = monitor.query_counter
        monitor.reset_statistics()
        
        return jsonify({
            'status': 'success',
            'message': '统计数据已重置',
            'old_query_count': old_count,
            'new_query_count': monitor.query_counter
        })
    except Exception as e:
        logger.error(f"重置统计失败: {e}")
        return jsonify({'error': str(e)}), 500