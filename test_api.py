import requests
import json

# 设置不使用代理
proxies = {
    'http': None,
    'https': None
}

# 设置请求参数
url = 'http://127.0.0.1:10023/zte-ibo-acm-productretrieve/faq'
headers = {
    'X-Emp-No': '0668001399',
    'X-Auth-Value': 'a9429deaab5e7bcd66770610617d41a2',
    'Content-Type': 'application/json'
}
data = {
    "text": "M2000-8S支持哪些接口1111"
}

try:
    print("发送请求...")
    response = requests.post(
        url,
        headers=headers,
        json=data,
        proxies=proxies,
        timeout=30
    )
    
    print(f"状态码: {response.status_code}")
    print(f"响应头: {response.headers}")
    
    if response.status_code == 200:
        print("响应内容:")
        print(response.text)
    else:
        print(f"错误响应: {response.text}")
        
except requests.exceptions.Timeout:
    print("请求超时")
except requests.exceptions.ConnectionError as e:
    print(f"连接错误: {e}")
except Exception as e:
    print(f"未知错误: {e}")