from domain.constants.infoGo_constants import translate_dict, yxxbk_list
from utils.cos_sim import cosine_similarity
from embedding.get_embedding import GetEmbedding
from domain.config.zxtech_config import config

getembedding = GetEmbedding(config)


# Started by AICoder, pid:z8dafa50efma692140f30a225066c01ee1f3e44b
def isSeries(yxxbk_l, body):
    """
    检查body中是否存在product_id为空且series_id不为空的项，并且该系列名称在yxxbk_l中。
    :param yxxbk_l: 系列名称列表
    :param body: 待检查的字典
    :return: 如果存在符合条件的项，返回True；否则，返回False
    """
    for item in body.values():
        if not item['product_id'] and item['series_id']:
            seriesName = item['series_id'].replace('series', '')
            if seriesName in yxxbk_l or seriesName.split(' ')[1] in yxxbk_l:
                return True
    return False

# Ended by AICoder, pid:z8dafa50efma692140f30a225066c01ee1f3e44b


# Started by AICoder, pid:b677cfbbdamb211147b4082650883601bf39cd4b
def inyxxbk_list(yxxbk_l, yxxbk_list):
    """
    检查列表yxxbk_l中的元素是否在列表yxxbk_list中。

    :param yxxbk_l: 待检查的元素列表
    :param yxxbk_list: 目标列表
    :return: 如果存在至少一个元素在yxxbk_list中，返回True；否则返回False
    """
    return any(node in yxxbk_list for node in yxxbk_l)

# Ended by AICoder, pid:b677cfbbdamb211147b4082650883601bf39cd4b


def translate_properties(properties, translation_dict):
    translated_properties = {}
    for key, value in properties.items():
        # 使用反转后的词典查找
        translated_key = translation_dict.get(key, key)
        translated_properties[translated_key] = value
    return translated_properties

def sort_dict(all_dict, question):
    keys = all_dict.keys()
    query_and_res = [question]
    data = []
    for j in keys:
        query_and_res.append(j)
        data.append(j)
    embedding_Res = getembedding.post_url_m3(query_and_res)
    similarities = [cosine_similarity(embedding_Res[0], b) for b in embedding_Res[1:]]
    em_key_dict = {}
    for i in range(len(data)):
        em_key_dict[similarities[i]] = data[i]
    result_arr = []
    sort_similarities = sorted(similarities, reverse=True)
    res_similarities = sort_similarities[0:1]
    es_top_searchText=[]
    for sim in res_similarities:
        key = em_key_dict.get(sim)
        value = all_dict.get(key)
        result_arr.append(value)
        es_top_searchText.append(key)
    return result_arr,es_top_searchText
