"""
本地测试监控功能（不依赖HTTP服务）
直接调用监控模块展示排行榜效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from monitor.recall_monitor import get_monitor
import random
import time

def simulate_queries(num_queries=20):
    """模拟多次查询并记录监控数据"""
    monitor = get_monitor()
    
    print("=" * 80)
    print("                    召回监控本地测试")
    print("=" * 80)
    print(f"\n将模拟 {num_queries} 次查询...")
    print("-" * 40)
    
    queries = [
        "M6000-8S Plus的设备尺寸",
        "ZXR10 M6000如何配置VLAN？",
        "5G基站的功耗参数",
        "产品的最大传输速率是多少？",
        "M6000-8S Plus支持哪些网络协议？",
        "如何进行系统升级操作？",
        "告警信息如何查看和处理？",
        "设备的环境温度要求",
        "M6000系列的端口配置方法",
        "系统日志导出步骤"
    ]
    
    for i in range(num_queries):
        # 随机选择查询
        query = random.choice(queries)
        
        # 模拟ES召回（总是有结果）
        es_results = [
            {'content': f'ES文档{j}_{query[:10]}', 'doc_name': f'doc_es_{j}', 'id': f'es_{i}_{j}'}
            for j in range(random.randint(10, 20))
        ]
        
        # 模拟Milvus召回（已禁用，返回空）
        milvus_results = []
        
        # 模拟KG召回（30%概率有结果）
        kg_results = []
        if random.random() > 0.7:  # 30%概率
            kg_results = [f"KG知识图谱结果：{query[:20]}的详细信息..."]
        
        # 模拟重排序结果
        rerank_results = []
        
        # 如果KG有结果，有50%概率排在前面
        if kg_results and random.random() > 0.5:
            rerank_results.append({
                'content': kg_results[0],
                'doc_name': 'kg_doc',
                'id': f'kg_{i}',
                'score': 0.95
            })
        
        # 添加ES结果
        for idx, es_item in enumerate(es_results[:10]):
            rerank_results.append({
                'content': es_item['content'],
                'doc_name': es_item['doc_name'],
                'id': es_item['id'],
                'score': 1.0 / (idx + 2)
            })
        
        # 如果KG结果没在前面，可能在中间
        if kg_results and len(rerank_results) < 15 and f'kg_{i}' not in [r['id'] for r in rerank_results]:
            insert_pos = random.randint(1, min(5, len(rerank_results)))
            rerank_results.insert(insert_pos, {
                'content': kg_results[0],
                'doc_name': 'kg_doc',
                'id': f'kg_{i}',
                'score': 0.85
            })
        
        # 限制结果数量
        rerank_results = rerank_results[:15]
        
        # 记录到监控
        response_time = random.uniform(0.1, 0.5)
        query_id = monitor.record_query(
            query=query,
            es_results=es_results,
            milvus_results=milvus_results,
            kg_results=kg_results,
            rerank_results=rerank_results,
            response_time=response_time
        )
        
        # 显示进度
        print(f"  [{i+1:3}/{num_queries}] 查询: {query[:30]:30} | ES: {len(es_results):2} | KG: {len(kg_results):1} | 重排: {len(rerank_results):2}")
        
        # 每10次显示一次简单排行榜
        if (i + 1) % 10 == 0:
            print("\n" + "=" * 40)
            print(f"        第 {i+1} 次查询后的排行榜")
            print("=" * 40)
            for idx, item in enumerate(monitor.leaderboard[:3], 1):
                print(f"  [{idx}] {item['source']:6} - 贡献度: {item['contribution_rate']:5.1f}% | 得分: {item['total_score']:6.2f}")
            print("=" * 40 + "\n")
    
    return monitor

def show_final_results(monitor):
    """显示最终结果"""
    print("\n" * 2)
    print("=" * 80)
    print("                    最终统计结果")
    print("=" * 80)
    
    # 显示详细排行榜
    print("\n[1] 召回源贡献度排行榜（详细版）")
    print("-" * 80)
    print(monitor.get_leaderboard('detailed'))
    
    # 显示简单排行榜
    print("\n[2] 召回源贡献度排行榜（简化版）")
    print("-" * 80)
    print(monitor.get_leaderboard('simple'))
    
    # 显示统计报告
    print("\n[3] 详细统计报告")
    print("-" * 80)
    # 只显示报告的前1000个字符，避免太长
    report = monitor.get_statistics_report()
    print(report[:1500] + "\n..." if len(report) > 1500 else report)
    
    # 显示最近查询趋势
    print("\n[4] 最近5次查询的贡献度趋势")
    print("-" * 80)
    recent = list(monitor.query_history)[-5:]
    for q in recent:
        print(f"查询ID: {q.query_id} | 时间: {q.timestamp}")
        print(f"  ES: {q.es_metrics.contribution_rate:5.1f}% | "
              f"Milvus: {q.milvus_metrics.contribution_rate:5.1f}% | "
              f"KG: {q.kg_metrics.contribution_rate:5.1f}%")
    
    # 显示总结
    print("\n" * 2)
    print("=" * 80)
    print("                    监控总结")
    print("=" * 80)
    print(f"总查询次数: {monitor.query_counter}")
    print(f"监控开始时间: {monitor.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n各召回源表现：")
    
    for source, metrics in monitor.global_stats.items():
        if metrics.recall_count > 0:
            print(f"\n{source.upper()}:")
            print(f"  - 平均贡献率: {metrics.contribution_rate:.1f}%")
            print(f"  - 总得分: {metrics.total_score:.2f}")
            print(f"  - Top1命中: {metrics.top1_count} 次")
            print(f"  - 总命中: {metrics.hit_count} 次")
    
    print("\n" + "=" * 80)
    print("                    演示完成")
    print("=" * 80)

if __name__ == "__main__":
    print("\n召回监控本地测试程序")
    print("本程序将在本地模拟查询并展示监控效果")
    print("=" * 40)
    
    # 运行模拟
    monitor = simulate_queries(30)  # 模拟30次查询
    
    # 显示结果
    show_final_results(monitor)
    
    print("\n提示：")
    print("1. 这是本地模拟测试，不需要启动HTTP服务")
    print("2. 实际使用时，监控会自动记录每次FAQ查询")
    print("3. 可以通过API接口查看实时监控数据")