@startuml ZTE产品检索系统4A架构业务流程图
!theme vibrant
skinparam backgroundColor #FAFAFA
skinparam activityBorderThickness 2
skinparam activityBackgroundColor #FFFFFF
skinparam activityBorderColor #333333

title **ZTE产品检索问答系统 - 4A架构业务流程图**\n<i>ZTE Product Retrieval Q&A System - 4A Business Flow</i>\n

|#FFE6E6|**Authentication 认证流程**|
start
:用户发起访问请求\nHTTP/HTTPS;
note right
  请求信息:
  - 用户标识
  - 访问资源
  - 客户端信息
  - 时间戳
end note

:网络安全检查;
if (通过防火墙检查?) then (否)
  :返回网络访问拒绝;
  #FF6B6B:记录安全事件;
  stop
else (是)
endif

:UAC身份认证;
note right
  认证方式:
  - 员工号 + 密码
  - 数字证书
  - OAuth/SAML
  - 多因子认证
end note

if (认证成功?) then (否)
  #FFB6C1:记录认证失败;
  :返回认证失败响应;
  stop
else (是)
  #90EE90:记录认证成功;
endif

:生成访问令牌;
note right
  JWT Token包含:
  - 用户身份信息
  - 权限范围
  - 过期时间
  - 数字签名
end note

|#E6F3FF|**Authorization 授权流程**|

:解析访问令牌;
:获取用户角色信息;
note right
  角色信息包括:
  - 部门角色
  - 功能角色
  - 数据权限
  - 时间限制
end note

:检查API访问权限;
if (有API访问权限?) then (否)
  #FFB6C1:记录授权失败;
  :返回权限不足;
  stop
else (是)
  #90EE90:记录授权成功;
endif

:动态权限计算;
note right
  权限计算依据:
  - 基础角色权限
  - 动态业务规则
  - 上下文环境
  - 风险评估
end note

:数据权限过滤配置;
note right
  数据过滤策略:
  - 产品可见性
  - 文档级别权限
  - 字段级脱敏
  - 地域限制
end note

|#E6FFE6|**Accounting 审计流程**|

:开始业务操作审计;
fork
  :记录用户操作;
  note right
    操作日志包含:
    - 用户身份
    - 操作时间
    - 操作内容
    - 访问资源
    - 操作结果
  end note
fork again
  :实时安全监控;
  note right
    监控内容:
    - 异常访问模式
    - 高频操作检测
    - 敏感数据访问
    - 跨域访问
  end note
fork again
  :性能指标收集;
  note right
    性能指标:
    - 响应时间
    - 并发用户数
    - 资源使用率
    - 错误率统计
  end note
end fork

|#FFFFFF|**业务核心流程**|

:执行产品检索业务;
note right
  业务流程:
  1. 查询预处理
  2. 实体识别
  3. 多路检索
  4. 结果重排序
  5. LLM生成答案
end note

:应用数据权限过滤;
if (符合数据权限?) then (否)
  :过滤敏感内容;
  #FFE4B5:数据脱敏处理;
else (是)
endif

:生成业务响应;
:响应数据加密;

|#E6FFE6|**继续Accounting审计**|

fork
  :记录业务操作结果;
  note right
    结果日志:
    - 查询成功/失败
    - 返回数据量
    - 处理时长
    - 异常信息
  end note
fork again
  :合规性检查;
  if (符合合规要求?) then (否)
    #FF6B6B:触发合规告警;
  else (是)
    #90EE90:合规检查通过;
  endif
fork again
  :资源使用统计;
  note right
    统计信息:
    - CPU使用率
    - 内存消耗
    - 网络流量
    - 存储访问
  end note
end fork

|#FFF0E6|**Administration 管理流程**|

fork
  :更新用户会话状态;
fork again
  :系统健康监控;
  if (系统状态正常?) then (否)
    #FF6B6B:触发系统告警;
    :通知管理员;
  else (是)
  endif
fork again
  :策略动态调整;
  note right
    动态调整策略:
    - 基于风险评估
    - 基于负载情况
    - 基于安全事件
    - 基于业务需求
  end note
end fork

:返回最终响应;

|#E6FFE6|**后续审计处理**|

fork
  :日志持久化存储;
  note right
    存储要求:
    - 不可篡改
    - 长期保存
    - 快速检索
    - 备份恢复
  end note
fork again
  :生成审计报告;
  note right
    报告类型:
    - 实时监控报告
    - 日度统计报告
    - 合规审计报告
    - 安全态势报告
  end note
fork again
  :风险评估更新;
  if (发现安全风险?) then (是)
    #FF6B6B:触发安全响应;
    :更新安全策略;
  else (否)
  endif
end fork

stop

' 添加并行处理说明
note bottom
  **4A架构并行处理说明**:
  
  **Authentication**: 
  - 支持多种认证方式并行验证
  - 实时认证状态同步
  
  **Authorization**: 
  - 并行权限计算和验证
  - 动态权限实时更新
  
  **Accounting**: 
  - 多维度并行审计记录
  - 实时监控和离线分析并行
  
  **Administration**: 
  - 并行系统监控和管理
  - 策略实时调整和应用
end note

' 关键节点说明
floating note left: **安全检查点**\n每个阶段都有安全验证
floating note right: **审计覆盖**\n全流程操作审计记录

@enduml