@startuml
!theme vibrant
skinparam backgroundColor #FFFFFF
skinparam defaultFontSize 12
skinparam ArrowColor #2E86C1
skinparam ArrowThickness 2

' 颜色配置
skinparam ActorBackgroundColor #FFE4B5
skinparam BoundaryBackgroundColor #87CEEB
skinparam ControlBackgroundColor #98FB98
skinparam ParticipantBackgroundColor #FFB6C1
skinparam DatabaseBackgroundColor #E6F2FF

title ZTE AgenticRAG 系统简化架构图

' 定义参与者和系统边界
actor Users
boundary API
control Controller

' 核心处理组件
participant Service
participant Rewrite
participant Retriever
participant Reranker
participant Generator

' 数据存储
database ES
database Milvus
database KG

' 外部服务
participant LLM <<external>>

' 主要流程
autonumber
Users -> API : 发送查询请求
API -> API : UAC Token验证
API -> Controller : 转发请求

Controller -> Service : 调用业务服务
Service -> Service : 实体提取\\n产品型号识别

alt 需要查询重写
    Service -> Rewrite : 重写查询
    Rewrite -> LLM : 调用LLM重写
    LLM --> Rewrite : 返回重写结果
    Rewrite --> Service : 返回优化查询
end

Service -> Retriever : 执行多路检索

group 并行检索
    Retriever -> ES : BM25文本检索
    ES --> Retriever : 文本匹配结果
    Retriever -> Milvus : 向量相似度检索
    Milvus --> Retriever : 向量匹配结果
    Retriever -> KG : 知识图谱检索
    KG --> Retriever : 图谱关联结果
end

Retriever -> Retriever : 结果融合
Retriever --> Service : 返回候选文档

Service -> Reranker : 重排序
Reranker -> Reranker : 多维度相似度计算\\n去重与阈值判断
Reranker --> Service : 返回TopK文档

Service -> Generator : 生成答案
Generator -> LLM : 调用LLM生成
LLM --> Generator : 返回答案
Generator --> Service : 流式输出

Service --> Controller : 返回结果
Controller --> API : 返回响应
API --> Users : 显示答案

' 添加说明
note over Service
    检索策略选择
    - 有产品实体：产品特定检索
    - 无产品实体：全局检索
    - 支持版本感知检索
end note

note over Retriever
    三路召回融合
    - ES: 关键词匹配
    - Milvus: 语义相似
    - KG: 关系推理
end note

note over Reranker
    智能重排序
    - BGE-M3模型评分
    - TF-IDF相似度
    - 内容去重
    - 多阈值控制
end note

@enduml