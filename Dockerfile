FROM public-docker-virtual.artnj.zte.com.cn/python:3.10.10
WORKDIR ./zte-ibo-acm-productretrieve
# 安装时区设置工具
RUN apt-get update && apt-get install -y tzdata
# 设置时区为东八区（北京时间）
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
ENV SW_AGENT_COLLECTOR_BACKEND_SERVICES=esgoap.dt.zte.com.cn:11800
ENV SW_AGENT_NAME=zte-ibo-acm-productretrieve
ADD . .
RUN pip install --no-cache-dir -r requirements.txt --index-url https://artnj.zte.com.cn/artifactory/api/pypi/public-pypi-virtual/simple --trusted-host artnj.zte.com.cn
EXPOSE 5001
CMD ["sw-python", "run", "-p", "python3", "./main.py"]