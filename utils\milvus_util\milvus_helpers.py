from pymilvus import connections, FieldSchema, CollectionSchema, DataType, Collection, utility
import sys
sys.path.append('../..')
from .config import VECTOR_DIMENSION, METRIC_TYPE
from domain.config.zxtech_config import config
from .logs import LOGGER


class MilvusHelper:

    def __init__(self):
        try:
            self.collection = None
            connections.connect(host=config['Milvus']['MILVUS_HOST'], port=config['Milvus']['MILVUS_PORT'])
            LOGGER.info(f"Successfully connect to Milvus with IP:{config['Milvus']['MILVUS_HOST']} and PORT:{config['Milvus']['MILVUS_PORT']}")
        except Exception as e:
            LOGGER.error(f"Failed to connect Milvus: {e}")
            raise Exception(f"Failed to connect Milvus: {e}")


