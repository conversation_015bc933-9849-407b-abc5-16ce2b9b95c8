import json

from flask import make_response, Response
from domain.config.zxtech_config import config

def translate_dict():
    translate_dict = {"额定输入频率": "rated_input_frequency", "主控板": "main_control_board",
                      "业务接入与汇聚单板": "business_access_aggregation_board",
                      "工作电压范围": "working_voltage_range", "告警类别": "alarm_category",
                      "命令格式": "command_format", "总插槽数": "slot_number",
                      "系列产品联系": "series_product_relation", "产品号": "product_numbers",
                      "路由交换板": "routing_switch_board", "描述": "model_desc",
                      "空机箱最大功耗（不含主控交换）": "empty_max_power", "MTBF": "mtbf", "硬件类型": "model_type",
                      "插箱": "plug_box", "词条内容": "attribute", "检测点": "detect_point",
                      "最大风量": "max_air_volume", "交流电源冗余": "ac_power_redundancy", "告警原因": "alarm_reason",
                      "风扇数": "fan_numbers", "系统可靠性": "system_reliabilit", "命令功能": "command_function",
                      "直流电源模块": "dc_power_module", "储存环境海拔": "storage_altitude", "系列号": "serial_number",
                      "最大功耗（不含主控交换）": "maxpower", "额定输入电压": "rated_input_voltage", "词条": "entry",
                      "接收端工作波长范围（nm）": "receiver_working_wavelength_range", "交叉单板": "cross_single_board",
                      "电源冗余": "power_redundancy", "告警恢复": "alarm_recovery",
                      "直流机箱重量（满配置）": "dc_chassis_weight", "光放大单板": "light_amplification_board",
                      "工作环境海拔": "work_altitude", "端口速率": "port_rate",
                      "空机箱典型功耗（不含主控交换）": "empty_typical_power", "风扇单板": "fan_board",
                      "交换板": "exchange_board", "直流额定电压": "dc_rated_voltage", "告警描述": "alarm_desc",
                      "最小消光比（dB）": "minimum_extinction_ratio", "文件键值": "file_key", "适用插箱": "boxes",
                      "大类": "big_category", "文本表联系": "text_table_relation", "机箱重量（满配置）": "chassis_weight",
                      "MTTR": "mttr", "最小平均发送光功率（dBm）": "min_average_transmission_optical_power",
                      "插箱重量": "box_weight", "章节文本联系": "chapter_text_relaion",
                      "最大传输距离": "max_trans_distance", "SPN光模块": "spn_optical_module",
                      "章节表联系": "chapter_table_relaion", "接收灵敏度（dBm）": "receiving_sensitivity",
                      "小类": "small_category", "部件型号": "model", "交换网板": "exchange_network_board",
                      "OXC单板": "oxc_board", "交流电源模块": "ac_power_module",
                      "过载光功率（dBm）": "overload_optical_power", "线路接口板": "line_interface_board",
                      "命令名称": "command_name", "工作环境温度": "work_temperature", "小百科来源": "source",
                      "空插箱重量": "empty_plug_box_weight", "主键": "primary_key", "光模块": "optical_module",
                      "电交叉业务单板": "electric_cross_service_board", "文档表联系": "document_table_relation",
                      "北美ATIS-0600015功耗": "hardware_usamaxpower", "OTN插箱": "otn_plug_box",
                      "OTN光模块": "otn_optical_module", "短期工作环境湿度": "shortterm_work_humidity",
                      "最大平均发送光功率（dBm）": "max_average_transmission_optical_power", "端口类型": "port_type",
                      "告警码": "alarm_code", "表": "table", "风扇冗余": "fan_redundancy", "产品id": "product_id",
                      "集成处理板": "integrated_processing_board", "中心波长（nm）": "center_wavelength",
                      "是否支持": "whether_support", "交流高压直流电源模块": "ac_high_voltage_dc_power_module",
                      "接头类型": "joint_type", "满配功耗": "full_power_consumption", "问题": "question",
                      "内存": "memory", "命令查询": "command_node", "文档id": "document_id", "答案": "answer",
                      "典型热负荷": "heat_load", "合分波单板": "composite_split_wave_board",
                      "主控冗余": "main_control_redundancy", "类型": "type", "常温最大功耗 （25 ℃）": "maxpower_roomtemp",
                      "空机箱典型热负荷": "empty_heat_load", "储存环境温度": "storage_temperature", "表id": "table_id",
                      "适用产品版本": "applicable_product_version", "CPU": "cpu",
                      "光层管理单板": "light_layer_management_board", "名称": "name", "文本": "text",
                      "链接地址": "link_address", "最大输出功率": "max_output_power",
                      "发送功率范围OMA（dBm）": "trans_power_range_OMA", "输入电压范围": "input_voltage_range",
                      "风扇模块数": "fan_modules", "告警查询": "alarm_node",
                      "适用产品型号": "applicable_product_models", "直流电源冗余": "dc_power_redundancy",
                      "最大功耗": "hardware_maxpower", "单板重量": "single_board_weight", "特性查询": "feature_node",
                      "业务插槽数": "business_slots", "长期工作环境湿度": "longterm_work_humidity",
                      "网络处理板": "network_processing_board", "系列文档联系": "series_document_relation",
                      "额定输入电流": "rated_input_current", "词条内容联系": "entry_attribute_relation",
                      "交叉容量": "cross_capacity", "发送波长范围（nm）": "trans_wavelength_range",
                      "是否有越限告警": "limit_exceeding_alarm", "文档文本联系": "document_text_relation",
                      "告警类型": "alarm_type", "告警描述原型": "alarm_desc_prototype", "参数说明": "param_desc",
                      "业务处理板母板": "business_processing_board_mother_card", "告警级别": "alarm_level",
                      "文档章节联系": "document_chapter_relation", "特性名称": "feature_name", "章节": "chapter",
                      "重量": "weight", "文档": "document", "交换容量": "exchange_capacity",
                      "外形尺寸（高×宽×深）": "shape_size", "业务板": "business_board", "告警名称": "alarm_name",
                      "接收波长范围（nm）": "receiving_wavelength_range", "单板尺寸（高×宽×深）": "single_board_size",
                      "保护单板": "protecting_single_board", "机箱": "router", "风扇模块": "fan_module",
                      "告警影响": "alarm_impact", "处理建议": "handle_advice", "监控单板": "monitoring_board",
                      "接收端类型": "receiver_type", "集成业务处理板": "integrated_business_processing_board",
                      "产品系列id": "series_id", "接收灵敏度AVG（dBm）": "receiving_sensitivity_AVG",
                      "来源": "space_link", "交流额定电压": "ac_rated_voltage", "命令模式": "command_mode",
                      "文本id": "text_id", "噪声": "noise",
                      "发送端工作波长范围（nm）": "working_wavelength_transmitting_range",
                      "空机箱北美ATIS-0600015功耗（不含主控交换）": "usaempty_max_power",
                      "发送功率范围AVG（dBm）": "trans_power_range_AVG", "产品文档联系": "product_document_relation",
                      "文本联系": "text_text_relaion", "最大风压": "max_wind_pressure",
                      "高压直流额定电压": "high_voltage", "告警模块": "alarm_module", "子文本id": "sub_text_id",
                      "内容": "content", "SSD": "ssd", "新产品号": "product_number",
                      "业务处理板子卡": "business_processing_board_daughter_card", "储存环境湿度": "storage_humidity",
                      "常温且有风阻下最大功耗（25 ℃）": "maxpower_consumption_roomtemperature_wind",
                      "工作环境湿度": "work_humidity", "封装": "encapsulation", "典型功耗": "typical_power",
                      "交换网冗余": "exchange_redundancy", "产品": "product", "电源单板": "power_supply_board",
                      "光纤类型": "fiber_type", "产品系列": "series", "章节id": "chapter_id",
                      "告警样例": "alarm_example", "交流机箱重量（满配置）": "ac_chassis_weight",
                      "接收灵敏度OMA（dBm）": "receiving_sensitivity_OMA"}
    return translate_dict
def infoGo_product_list():
    infoGo_product_list = ['ZXONE 9700', 'ZXR10 5960-56QU-HF', 'ZXCTN 6120H-B (IPRAN)', 'ZXR10 5960X-54DU-HE',
                           'ZXR10 M6000-2S16', 'ZXR10 5960M-56VX-HX', 'ZXR10 M6000-18S', 'ZXR10 5960X-54DL-HF',
                           'ZXR10 M6000-2S6', 'ZXR10 M6000-3S Plus', 'ZXCTN 6120H-A', 'ZXR10 C89E-12', 'ZXONE 7000 C2D',
                           'ZXCTN 6180H (SPN)', 'ZXCTN 6180H-A', 'ZXR10 5960X-54DL-HG', 'ZXR10 5960M-56QU-HI',
                           'ZXR10 M6000-8S Plus', 'ZXR10 5960X-24U-HF', 'ZXCTN 6190H-A', 'ZXCTN 6120H-S', 'ZXCTN 810A2',
                           'ZXR10 5960X-56QU-HE', 'ZXCTN 6120H-SL', 'ZXR10 5960M-8M-HX', 'ZXCTN 6120H-B (PTN)',
                           'ZXR10 9916X', 'ZXR10 9904X', 'ZXR10 M6000-16SE', 'ZXR10 V6000 vBRAS', 'ZXR10 5960X-54DU-HF',
                           'ZXR10 T8000-18', 'ZXR10 5960X-56QU-HG', 'ZXCTN 6700-24', 'ZXR10 5960-54DU-HC',
                           'ZXR10 5960M-4M-HI', 'ZXR10 C89E-8', 'ZXCTN 9000-8EA DC', 'ZXCTN 6170H', 'ZXCTN 9000-3EA',
                           'ZXR10 C89E-3', 'ZXR10 M6000-8SE', 'ZXCTN 6190H', 'ZXR10 M6000-5S', 'ZXCTN 9000-18EA',
                           'ZXR10 M6000-4SE', 'ZXR10 5960M-128Y-HX', 'ZXONE 7000 C2 C2(NIS) C2C', 'ZXR10 5960M-56QU-HX',
                           'ZXONE 19700', 'ZXCTN 6120H-SC', 'ZXR10 M6000-8S', 'ZXR10 5960X-56QU-HF', 'ZXCTN 9000-2E8A',
                           'ZXCTN 6180H (IPRAN)', 'ZXR10 C89E-4', 'ZXR10 5960M-8M-HI', 'ZXCTN 6700-12',
                           'ZXR10 5960X-54DU-HG', 'ZXCTN 6700-32', 'ZXCTN 6120H-C', 'ZXCTN 6120H-BL', 'ZXMP M721',
                           'ZXCTN 9000-8EA HDC', 'ZXR10 9908X']
    return infoGo_product_list
def yxxbk_list():
    yxxbk_list = ['QPPB', 'DHCP', '私有云', '意图网络', 'SBFD', 'MTU', 'OTN同缆检测', 'Optical Fiber Cable', 'STP',
                  'Telemetry', 'SDN', 'IP网络仿真', '光通信', 'ARP Security', 'SQA', 'SRv6', '路由器',
                  'Network Management System Redundancy and Disaster Recovery', 'IPSec', 'URL过滤', 'MIM', '数据中心',
                  '3A高精度同步', 'SNMP Brute-Force Attack Defense', 'GRE隧道', 'Super VLAN', 'VDC', 'SYN Flood',
                  '光缆', 'FlexE', 'BGP', 'IPv6', 'Virtual Router', 'Switch', 'Flex-Algo', 'CU Separation',
                  'SNMP防暴力攻击', 'FRR', 'IS-IS', '流量监管', 'BFD', 'OTDR', 'NetFlow', 'Netconf', 'IGMP安全', 'NFV',
                  'Root Server', '网管备份容灾', '8021X', 'ND', 'IP属地', 'MAC地址', '微服务', 'IP-Host', 'DDoS攻击',
                  'EVPN', 'VRRP', 'Deterministic IP Network', 'Stacking', 'QinQ', 'Optical Fiber', 'APR-AOSD',
                  '流量整形', 'BRAS', 'Broadband', '交换机', '光纤', 'NETCONF', 'ARP安全', 'PoE', 'BRAS双机热备',
                  '6VPE', 'Static Routing', 'DDoS Attack', 'Trace', 'MPLS', '策略路由', 'NAT', '网络自动化', '容器',
                  '光模块', 'Active-Active Gateways', 'Priority Mapping', 'SynFlood', 'Dampening', '6PE', '混合云',
                  'OSPF', 'VPDN', 'DHCP Snooping', '虚拟路由器', 'SmartGroup', 'URPF', 'RADIUS', 'LACP', 'T-SRv6',
                  'BMP', 'IP Network Simulation', 'BRAS Hot Standby', 'SSH', 'Container', 'VXLAN', 'PTP', '负载分担',
                  'Keychain', 'Microservices', 'PPP', 'ARP', 'Load Balancing', '双活网关', 'Ti-LFA', 'Loopback', 'ACL',
                  'LAN', 'SNMP', 'RDMA', 'OpenFlow', 'CFM', 'AI-Powered Dynamic Energy-Saving', '组播', 'TACACS',
                  '接入网', 'SR', 'EFM', 'DCN', '公有云', 'AI动态节能', 'Ping', 'Router', 'IP城域网', 'APS',
                  '优先级映射', 'IP Host', 'SAMGR', 'Route Preference', '宽带', 'ZESR', '堆叠', 'IPv4', 'LLDP',
                  'MAC Address', 'DNS', 'Public Cloud', 'Network Automation', 'IOAM', 'MCE', 'Hybrid Cloud', 'GR',
                  'IPsec', 'VLAN', 'NSR', '流分类', 'MC-LAG', 'FEC', 'Mirroring', 'UDP', 'PCEP', 'PWE3', 'Damping',
                  'VPN', 'QoS', 'AAA', 'Private Cloud', 'TCP', 'PPPoE', '5G电信云', 'ICMP', '拥塞管理',
                  'Loopback Interface', 'GTSM', 'CU分离', 'Slice', '小颗粒', 'HQoS', 'IPoE', 'IP地址', 'IPRAN',
                  '拥塞避免', 'OpenStack', 'URL Filtering', '确定性网络', 'Data Center', '骨干网', 'RCA', 'BIER',
                  '镜像', '根服务器']
    return yxxbk_list

def isInfoGo(pruduct):
    if 'product' in pruduct:
        pruduct=pruduct.replace('product','')
    list_infoGo_product_long= infoGo_product_list()
    list_infoGo_product_short=[i.split(' ', 1)[1] for i in list_infoGo_product_long]
    if pruduct in list_infoGo_product_long or pruduct in list_infoGo_product_short:
        return True
    return False


def version_error_response(pid,version_list):
    version_str = '、'.join(version_list)
    version_error_resonse = f'未能检索到{pid} {version_str}版本的信息。可能是版本或产品的输入存在偏差，请您确认输入是否准确无误，感谢您的配合。'

    return Response(generate_versionerror_response(version_error_resonse,config['Enviroment']), mimetype='text/event-stream')


def generate_versionerror_response(version_error_resonse,env='dev'):
    dict_content = {
        # "chatUuid": chatUuid,
        "finishReason": "",
        "result": 'start'
    }
    content_start = json.dumps(dict_content, ensure_ascii=False)
    dict_content1 = {
        "finishReason": "stop",
        "result": ""
    }
    if env != 'prod':
        yield ('data:' + str([content_start]) + '\n\n')
    for char in version_error_resonse:
        dict_content = {
            # "chatUuid": chatUuid,
            "finishReason": "",
            "result": char
        }
        content = json.dumps(dict_content, ensure_ascii=False)
        yield ('data: ' + content + '\n\n')

    yield ('data: ' + json.dumps(dict_content1, ensure_ascii=False) + '\n\n')
    yield ('data: [DONE]')
