@startuml ZTE产品检索系统架构图
!include <C4/C4_Container>
!include <C4/C4_Component>

title ZTE产品检索系统 - 全面系统架构图

' 设置样式
skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 12

' 用户层
actor "业务用户" as User #87CEEB
actor "系统管理员" as Admin #FFB6C1

' 外部系统
cloud "外部服务生态" as ExternalEco {
    component "ZTE统一认证中心\n(UAC)" as UACService #FFD700 {
        [身份认证]
        [权限验证]
        [Token管理]
    }
    
    component "Apollo配置中心" as ApolloConfig #FFD700 {
        [配置管理]
        [热更新]
        [环境隔离]
    }
    
    component "AI服务集群" as AIServices #FFD700 {
        [NebulaBiz LLM\nstudio.zte.com.cn]
        [BGE-M3 Embedding\nllm.dev.zte.com.cn]
        [BGE-M3 Reranker\nllm.dev.zte.com.cn]
        [知识图谱API\nkger.zte.com.cn]
    }
}

' 应用层
package "应用服务层" as ApplicationLayer #E6F3FF {
    component "Flask Web应用" as FlaskApp #B0E0E6 {
        [主应用入口\nmain.py]
        [CORS跨域支持]
        [代理配置]
        [蓝图路由管理]
    }
    
    component "控制器层" as ControllerLayer #B0E0E6 {
        [产品问答控制器\nzxtech_controller.py]
        [监控数据控制器\nmonitor_controller.py]
        [参数验证与处理]
        [响应格式化]
    }
}

' 业务逻辑层
package "业务逻辑层" as BusinessLayer #FFF8DC {
    component "核心服务引擎" as CoreEngine #F0E68C {
        [_ZXTECH_核心服务]
        [查询预处理]
        [实体识别与提取]
        [策略路由选择]
        [结果组装与返回]
    }
    
    component "智能处理模块" as IntelligentModule #F0E68C {
        [查询重写服务\nrewrite_model.py]
        [多轮对话管理]
        [意图理解]
        [上下文管理]
        [语言类型判断]
    }
    
    component "监控与分析" as MonitoringModule #F0E68C {
        [召回监控\nrecall_monitor.py]
        [性能统计]
        [数据分析]
        [排行榜生成]
    }
}

' 检索与AI层
package "检索与AI处理层" as RetrievalAILayer #E6FFE6 {
    component "多路召回引擎" as MultiRecallEngine #90EE90 {
        [ES文本检索\nRecallResult]
        [Milvus向量检索\nRecallMilvus]
        [知识图谱检索\nRecallResult_kg]
        [召回策略选择]
    }
    
    component "AI模型服务" as AIModelServices #90EE90 {
        [向量化服务\nGetEmbedding]
        [重排序服务\nBGEM3RerankV2]
        [LLM调用服务\nLLMResult]
        [实体提取\ndp_entity]
    }
    
    component "结果处理引擎" as ResultProcessor #90EE90 {
        [结果合并]
        [去重过滤]
        [相关性排序]
        [阈值判断]
        [流式输出处理]
    }
}

' 数据访问层
package "数据访问层" as DataAccessLayer #FFE4E1 {
    component "数据连接器" as DataConnectors #FFA07A {
        [ES连接器\nes_util]
        [Milvus连接器\nmilvus_util]
        [HTTP客户端\nhttp_util]
        [连接池管理]
    }
    
    component "配置管理器" as ConfigManager #FFA07A {
        [配置加载\nzxtech_config.py]
        [环境变量管理]
        [加密解密\nencryption_util]
        [Apollo客户端]
    }
}

' 基础设施层
package "基础设施层" as InfrastructureLayer #F0F0F0 {
    component "工具支持" as UtilityServices #D3D3D3 {
        [日志系统\nlogger_util]
        [安全认证\ntokenVerify]
        [工具函数\nutils]
        [签名生成]
    }
    
    component "数据处理工具" as DataProcessingTools #D3D3D3 {
        [文本分词]
        [相似度计算\ncos_sim]
        [实体工具\nentity_util]
        [翻译服务\ntranslator]
    }
}

' 数据存储层
package "数据存储层" as DataStorageLayer #E6E6FA {
    database "ElasticSearch集群" as ESCluster #DDA0DD {
        [产品文档索引]
        [全文检索]
        [多索引管理]
        [精确+模糊匹配]
    }
    
    database "Milvus向量数据库" as MilvusDB #DDA0DD {
        [文档向量存储]
        [余弦相似度检索]
        [向量索引管理]
        [高维向量搜索]
    }
    
    database "Nebula知识图谱" as NebulaGraph #DDA0DD {
        [产品关系图谱]
        [实体关系存储]
        [图谱查询]
        [知识推理]
    }
    
    storage "本地存储" as LocalStorage #DDA0DD {
        [日志文件]
        [配置缓存]
        [监控数据]
        [临时文件]
    }
}

' 连接关系 - 用户交互
User --> FlaskApp : HTTP请求\n/zte-ibo-acm-productretrieve/faq
Admin --> FlaskApp : 监控查询\n/monitor/stats

' 连接关系 - 应用层
FlaskApp --> ControllerLayer : 路由分发
ControllerLayer --> CoreEngine : 业务调用
ControllerLayer --> MonitoringModule : 监控记录

' 连接关系 - 业务逻辑
CoreEngine --> IntelligentModule : 智能处理
CoreEngine --> MultiRecallEngine : 检索调用
IntelligentModule --> AIModelServices : AI服务调用

' 连接关系 - 检索与AI
MultiRecallEngine --> ResultProcessor : 结果处理
AIModelServices --> ResultProcessor : 模型输出
ResultProcessor --> CoreEngine : 处理结果

' 连接关系 - 数据访问
MultiRecallEngine --> DataConnectors : 数据查询
AIModelServices --> DataConnectors : 外部API调用
CoreEngine --> ConfigManager : 配置获取

' 连接关系 - 基础设施
CoreEngine --> UtilityServices : 工具支持
IntelligentModule --> DataProcessingTools : 文本处理
MonitoringModule --> LocalStorage : 数据存储

' 连接关系 - 数据存储
DataConnectors --> ESCluster : ES查询
DataConnectors --> MilvusDB : 向量检索
DataConnectors --> NebulaGraph : 图谱查询
UtilityServices --> LocalStorage : 日志写入

' 连接关系 - 外部服务
ControllerLayer --> UACService : 身份认证
ConfigManager --> ApolloConfig : 配置获取
AIModelServices --> AIServices : AI模型调用

' 添加关键流程说明
note right of CoreEngine
    **核心业务流程**
    1. 接收用户查询请求
    2. UAC身份认证验证
    3. 查询预处理与重写
    4. 实体识别与提取
    5. 多路检索策略选择
    6. ES/Milvus/KG并行检索
    7. 结果融合与重排序
    8. LLM生成最终答案
    9. 流式响应返回
    10. 监控数据记录
end note

note bottom of DataStorageLayer
    **数据存储说明**
    - ES: 产品文档全文索引，支持中英文检索
    - Milvus: BGE-M3模型生成的1024维向量
    - Nebula: 产品-文档-实体知识图谱
    - Local: 日志、配置缓存、监控数据
end note

note top of ExternalEco
    **外部依赖服务**
    - UAC: ZTE统一认证，Token验证
    - Apollo: 配置中心，支持热更新
    - AI服务: NebulaBiz LLM + BGE-M3模型
    - KG API: 知识图谱查询服务
end note

@enduml
