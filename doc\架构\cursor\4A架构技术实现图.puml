@startuml ZTE产品检索系统4A架构技术实现图
!theme vibrant
skinparam backgroundColor #F8F9FA
skinparam componentStyle rectangle
skinparam packageStyle rectangle

title **ZTE产品检索问答系统 - 4A架构技术实现图**\n<i>ZTE Product Retrieval Q&A System - 4A Technical Implementation</i>\n

' 定义技术栈样式
skinparam component {
    BackgroundColor<<auth_tech>> #FFE6E6
    BackgroundColor<<authz_tech>> #E6F3FF
    BackgroundColor<<audit_tech>> #E6FFE6
    BackgroundColor<<admin_tech>> #FFF0E6
    BackgroundColor<<middleware>> #F0F0F0
    BackgroundColor<<storage>> #E8E8E8
    BackgroundColor<<external>> #FFEBEE
    BorderThickness 2
    RoundCorner 8
}

' Authentication技术栈
package "**Authentication 认证技术栈**" as AuthTechStack <<auth_tech>> {
    component "UAC认证集成" as UACTech {
        [Flask-Login] as FlaskLogin
        [PyJWT] as PyJWT
        [python-ldap] as PythonLDAP
        [requests-oauthlib] as OAuthLib
    }
    
    component "认证中间件" as AuthMiddleware {
        [TokenValidator类] as TokenValidatorClass
        [AuthenticationDecorator] as AuthDecorator
        [SessionManager] as SessionMgr
        [MultiFactorAuth] as MFAModule
    }
    
    component "认证存储" as AuthStorage {
        [Redis会话存储] as RedisSession
        [用户信息缓存] as UserCache
        [认证状态管理] as AuthState
    }
}

' Authorization技术栈
package "**Authorization 授权技术栈**" as AuthzTechStack <<authz_tech>> {
    component "权限控制框架" as PermissionFramework {
        [Flask-Principal] as FlaskPrincipal
        [casbin-python] as Casbin
        [SQLAlchemy-ACL] as SQLAlchemyACL
        [权限装饰器] as PermissionDecorator
    }
    
    component "权限中间件" as AuthzMiddleware {
        [RoleBasedACL] as RoleACL
        [ResourcePermission] as ResourcePerm
        [DataPermissionFilter] as DataFilter
        [DynamicPermission] as DynamicPerm
    }
    
    component "权限配置" as PermissionConfig {
        [权限矩阵配置] as PermissionMatrix
        [角色定义] as RoleDefinition
        [策略规则引擎] as PolicyEngine
    }
}

' Accounting审计技术栈
package "**Accounting 审计技术栈**" as AuditTechStack <<audit_tech>> {
    component "日志记录框架" as LoggingFramework {
        [Python logging] as PythonLogging
        [structlog] as StructLog
        [loguru] as Loguru
        [ELK Stack集成] as ELKIntegration
    }
    
    component "监控工具" as MonitoringTools {
        [Prometheus] as Prometheus
        [Grafana] as Grafana
        [Jaeger链路追踪] as Jaeger
        [APM监控] as APMMonitoring
    }
    
    component "审计中间件" as AuditMiddleware {
        [OperationAuditor] as OpAuditor
        [PerformanceMonitor] as PerfMonitor
        [SecurityEventLogger] as SecEventLogger
        [ComplianceChecker] as ComplianceCheck
    }
}

' Administration管理技术栈
package "**Administration 管理技术栈**" as AdminTechStack <<admin_tech>> {
    component "配置管理" as ConfigManagement {
        [Apollo Client] as ApolloClient
        [python-dotenv] as PythonDotenv
        [ConfigParser] as ConfigParser
        [动态配置加载] as DynamicConfig
    }
    
    component "系统管理工具" as SystemManagementTools {
        [Docker容器管理] as DockerMgmt
        [Kubernetes部署] as K8sDeploy
        [Nginx负载均衡] as NginxLB
        [健康检查] as HealthCheck
    }
    
    component "运维监控" as OperationsMonitoring {
        [系统资源监控] as ResourceMonitor
        [应用性能监控] as AppPerfMonitor
        [告警通知系统] as AlertSystem
        [自动化运维] as AutoOps
    }
}

' 中间件和基础设施
package "**中间件与基础设施**" as MiddlewareInfra <<middleware>> {
    component "Web服务框架" as WebFramework {
        [Flask 2.x] as Flask
        [Gunicorn WSGI] as Gunicorn
        [Nginx反向代理] as Nginx
        [Flask-CORS] as FlaskCORS
    }
    
    component "消息队列" as MessageQueue {
        [Redis Pub/Sub] as RedisPubSub
        [Celery任务队列] as Celery
        [RabbitMQ] as RabbitMQ
    }
    
    component "缓存系统" as CacheSystem {
        [Redis缓存] as RedisCache
        [Memcached] as Memcached
        [应用级缓存] as AppCache
    }
}

' 数据存储技术栈
package "**数据存储技术栈**" as StorageTechStack <<storage>> {
    database "关系型数据库" as RelationalDB {
        [PostgreSQL] as PostgreSQL
        [MySQL] as MySQL
        [SQLAlchemy ORM] as SQLAlchemy
    }
    
    database "NoSQL数据库" as NoSQLDB {
        [ElasticSearch] as ElasticSearch
        [MongoDB] as MongoDB
        [Redis] as Redis
    }
    
    database "向量数据库" as VectorDB {
        [Milvus] as Milvus
        [BGE-M3向量模型] as BGEM3
        [向量索引] as VectorIndex
    }
    
    database "图数据库" as GraphDB {
        [Nebula Graph] as NebulaGraph
        [知识图谱] as KnowledgeGraph
        [图查询引擎] as GraphQuery
    }
}

' 外部系统集成
package "**外部系统集成**" as ExternalSystems <<external>> {
    component "ZTE企业服务" as ZTEServices {
        [ZTE UAC认证] as ZTEUAC
        [ZTE LDAP目录] as ZTELDAP
        [ZTE安全中心] as ZTESecurity
    }
    
    component "AI/ML服务" as AIMLServices {
        [NebulaBiz LLM] as NebulaBizLLM
        [BGE-M3服务] as BGEM3Service
        [意图识别服务] as IntentService
    }
    
    component "基础设施服务" as InfraServices {
        [对象存储服务] as ObjectStorage
        [CDN内容分发] as CDN
        [API网关] as APIGateway
    }
}

' 技术架构连接关系

' Authentication技术集成
UACTech --> AuthMiddleware : 认证集成
AuthMiddleware --> AuthStorage : 状态存储
FlaskLogin --> TokenValidatorClass : 会话管理
PyJWT --> AuthDecorator : 令牌验证

' Authorization技术集成
PermissionFramework --> AuthzMiddleware : 权限控制
AuthzMiddleware --> PermissionConfig : 策略配置
Casbin --> PolicyEngine : 策略引擎
RoleACL --> DataFilter : 数据过滤

' Accounting技术集成
LoggingFramework --> AuditMiddleware : 日志记录
MonitoringTools --> AuditMiddleware : 监控集成
OpAuditor --> ELKIntegration : 日志收集
Prometheus --> Grafana : 监控可视化

' Administration技术集成
ConfigManagement --> SystemManagementTools : 配置管理
SystemManagementTools --> OperationsMonitoring : 运维监控
ApolloClient --> DynamicConfig : 动态配置
DockerMgmt --> K8sDeploy : 容器编排

' 中间件集成
Flask --> AuthMiddleware : 认证集成
Flask --> AuthzMiddleware : 授权集成
Flask --> AuditMiddleware : 审计集成
Gunicorn --> Nginx : 负载均衡
RedisCache --> RedisPubSub : 缓存消息

' 数据存储集成
SQLAlchemy --> PostgreSQL : ORM映射
ElasticSearch --> VectorDB : 混合检索
Milvus --> BGEM3 : 向量化
NebulaGraph --> KnowledgeGraph : 图谱查询

' 外部系统集成
AuthMiddleware --> ZTEUAC : 认证集成
AuthzMiddleware --> ZTELDAP : 目录查询
AuditMiddleware --> ZTESecurity : 安全审计
Flask --> NebulaBizLLM : AI服务调用

' 跨栈技术连接
AuthStorage --> RedisCache : 共享缓存
PermissionConfig --> RelationalDB : 权限存储
AuditMiddleware --> NoSQLDB : 日志存储
ConfigManagement --> RelationalDB : 配置存储

' 技术实现说明
note top of AuthTechStack
    **认证技术实现要点**:
    - JWT无状态认证
    - Redis会话管理
    - LDAP用户目录集成
    - OAuth2.0标准协议
    - 多因子认证支持
end note

note top of AuthzTechStack
    **授权技术实现要点**:
    - RBAC角色权限模型
    - Casbin策略引擎
    - 动态权限计算
    - 细粒度数据权限
    - RESTful API权限控制
end note

note top of AuditTechStack
    **审计技术实现要点**:
    - 结构化日志记录
    - ELK日志分析栈
    - Prometheus监控指标
    - 分布式链路追踪
    - 实时安全事件监控
end note

note top of AdminTechStack
    **管理技术实现要点**:
    - Apollo配置中心
    - Docker容器化部署
    - Kubernetes集群管理
    - 自动化运维流程
    - 多环境配置管理
end note

' 技术架构图例
legend bottom
    |= 技术栈 |= 颜色 |= 主要技术 |
    |<#FFE6E6> | Authentication | Flask-Login, PyJWT, LDAP |
    |<#E6F3FF> | Authorization | Flask-Principal, Casbin |
    |<#E6FFE6> | Accounting | ELK Stack, Prometheus |
    |<#FFF0E6> | Administration | Apollo, Docker, K8s |
    |<#F0F0F0> | Middleware | Flask, Redis, Nginx |
    |<#E8E8E8> | Storage | PostgreSQL, ES, Milvus |
    |<#FFEBEE> | External | ZTE Services, AI/ML |
    
    == 技术选型原则 ==
    **开源优先**: 优选成熟开源技术
    **标准兼容**: 遵循行业标准协议
    **高可用**: 支持集群和容错
    **可扩展**: 支持水平扩展
    **易维护**: 运维友好的技术栈
endlegend

@enduml