from flask import make_response
import numpy as np


# Started by AICoder, pid:a960cw4563ke9d0140ee085b60283f0ebea87e6b
def wrapper_response(code: str, result: str):
    # 创建一个包含code和msg的字典，其中msg的值是传入的result参数
    body = {
        "bo": {},
        "code": {
            "code": code,
            "msg": result,
            "msgId": "RetCode.chatError"
        }
    }

    # 使用flask的make_response函数创建一个响应对象，并将上述字典作为其数据
    res = make_response(body)

    return res

# Ended by AICoder, pid:a960cw4563ke9d0140ee085b60283f0ebea87e6b

# Started by AICoder, pid:5221641fa3vd1f314619093c5032bb12e808e5ec
def cosine_similarity(a, b):
    """
    计算两个向量的余弦相似度

    参数:
        a (numpy array): 第一个向量
        b (numpy array): 第二个向量

    返回:
        similarity (float): 余弦相似度的值，范围在-1到1之间。-1表示完全不相似，1表示完全相似。
    """
    # 计算点积
    dot_product = np.dot(a, b)
    # 计算向量a的范数
    norm_a = np.linalg.norm(a)
    # 计算向量b的范数
    norm_b = np.linalg.norm(b)
    # 计算余弦相似度
    similarity = dot_product / (norm_a * norm_b)
    return similarity

# Ended by AICoder, pid:5221641fa3vd1f314619093c5032bb12e808e5ec


# Started by AICoder, pid:r6534y1103r409f143d60ab5f00e5324fed066e1
import numpy as np

def weight_similarity(similarity, questions, query):
    """
    计算相似度并进行加权处理

    :param similarity: 相似度值
    :param questions: 问题列表
    :param query: 查询词列表
    :return: 加权后的相似度值
    """
    try:
        # 计算x的值
        x = len(questions) / len(query) - 1

        # 使用np.cosh函数计算权重，并返回加权后的相似度
        return similarity / np.cosh(0.8 * x)
    except Exception:
        # 如果出现异常（如除以零等），则返回0
        return 0

# Ended by AICoder, pid:r6534y1103r409f143d60ab5f00e5324fed066e1


def trace_info_merge(base_url, faq_tuple, docu_tuple=None):
    if docu_tuple:
        faq_tuple += docu_tuple
    studio_url_set = set()
    suffix_list = ['.pdf', '.docx', '.doc']
    for item in faq_tuple:
        if item[8] == "manual" and any(item[5].endswith(suffix) for suffix in suffix_list):
        # if item[8] == "manual":

            kbaseId = item[3]
            fileId = item[4]
            fileName = item[5]
            fileKey = item[6]
            url = f"{base_url}/zte-dmt-dmp-zxdtllm/advancedSearch/faq-intent/source_url?kbaseId={kbaseId}&fileName={fileName}&fileKey={fileKey}&fileId={fileId}"
            studio_url_tuple = (fileName, url)
            studio_url_set.add(studio_url_tuple)
        elif item[8] == "icenter":
            fileName = item[5][:-3]
            downloadUrl = item[7]
            studio_url_tuple = (fileName, downloadUrl)
            studio_url_set.add(studio_url_tuple)
        else:
            continue
    studio_url_list = list(studio_url_set)
    return studio_url_list

# Started by AICoder, pid:e3a7ba597am997314cd50b90b0532909f8a6e979
def chunk_deduplication(target):
    # 创建一个空集合用于存储chunk_id
    chunk_id_set = set()
    # 使用列表推导式过滤掉重复的chunk_id，只保留第一个出现的元素
    result = [res for res in target if res['chunkId'] not in chunk_id_set and not chunk_id_set.add(res['chunkId'])]
    return result

# Ended by AICoder, pid:e3a7ba597am997314cd50b90b0532909f8a6e979

def replace_keys(d, key_map):
    key_map={v:k for k,v in key_map.items()}
    return {key_map.get(k, k): v for k, v in d.items()}

def deal_candidates_by_means(candidates,embeddings,threshold):
    if not candidates:
        return []
    n = len(candidates)
    if n==1:
        return candidates
    to_remove=set()
    for i in range(n):
        if i in to_remove:
            continue
        for j in range(i+1,n):
            if  j in to_remove:
                continue
            similarity = cosine_similarity(embeddings[i], embeddings[j])
            if similarity >= threshold:
                if len(candidates[i]) < len(candidates[j]):
                    to_remove.add(i)
                else:
                    to_remove.add(j)
    res_list=[candidates[i] for i in range(n) if i not in to_remove]
    return res_list
