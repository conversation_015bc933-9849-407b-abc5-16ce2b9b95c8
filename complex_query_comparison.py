# 复杂技术查询场景对比

# ============ 传统SQL数据库方案 ============
class SQLDatabaseQuery:
    def get_upgrade_analysis(self, product_name, current_version, target_feature):
        """
        SQL方案：查询产品升级分析
        需要多个复杂的JOIN查询和子查询
        """
        
        # 查询1：获取当前产品版本信息
        current_product_query = """
        SELECT p.id, p.product_name, v.version_number, v.release_date
        FROM products p
        JOIN product_versions pv ON p.id = pv.product_id
        JOIN versions v ON pv.version_id = v.id
        WHERE p.product_name = %s AND v.version_number = %s
        """
        
        # 查询2：查找支持目标功能的版本（复杂子查询）
        feature_versions_query = """
        SELECT DISTINCT v.version_number, v.release_date, 
               array_agg(f.feature_name) as supported_features
        FROM products p
        JOIN product_versions pv ON p.id = pv.product_id
        JOIN versions v ON pv.version_id = v.id
        JOIN version_features vf ON v.id = vf.version_id
        JOIN features f ON vf.feature_id = f.id
        WHERE p.product_name = %s 
        AND EXISTS (
            SELECT 1 FROM version_features vf2 
            JOIN features f2 ON vf2.feature_id = f2.id
            WHERE vf2.version_id = v.id AND f2.feature_name = %s
        )
        AND v.version_number > %s  -- 版本比较逻辑复杂
        GROUP BY v.id, v.version_number, v.release_date
        ORDER BY v.release_date
        """
        
        # 查询3：兼容性分析（递归查询）
        compatibility_query = """
        WITH RECURSIVE compatibility_chain AS (
            -- 基础情况：当前版本的直接兼容性
            SELECT 
                cp.source_version_id,
                cp.target_version_id, 
                cp.compatibility_type,
                cp.compatibility_level,
                1 as depth,
                ARRAY[cp.source_version_id] as path
            FROM compatibility cp
            JOIN versions v1 ON cp.source_version_id = v1.id
            WHERE v1.version_number = %s
            
            UNION ALL
            
            -- 递归情况：传递兼容性
            SELECT 
                cc.source_version_id,
                cp.target_version_id,
                cp.compatibility_type,
                LEAST(cc.compatibility_level, cp.compatibility_level) as compatibility_level,
                cc.depth + 1,
                cc.path || cp.target_version_id
            FROM compatibility_chain cc
            JOIN compatibility cp ON cc.target_version_id = cp.source_version_id
            WHERE cc.depth < 5  -- 防止无限递归
            AND NOT cp.target_version_id = ANY(cc.path)  -- 防止循环
        )
        SELECT DISTINCT 
            v.version_number,
            cc.compatibility_type,
            cc.compatibility_level,
            cc.depth as compatibility_distance
        FROM compatibility_chain cc
        JOIN versions v ON cc.target_version_id = v.id
        JOIN version_features vf ON v.id = vf.version_id
        JOIN features f ON vf.feature_id = f.id
        WHERE f.feature_name = %s
        ORDER BY cc.compatibility_level DESC, cc.depth ASC
        """
        
        # 查询4：相关文档查询（多表JOIN）
        document_query = """
        SELECT DISTINCT 
            d.document_name,
            d.document_type,
            d.content_path,
            dt.priority_score,
            CASE 
                WHEN dr.relation_type = 'upgrade_guide' THEN 1
                WHEN dr.relation_type = 'compatibility_note' THEN 2
                WHEN dr.relation_type = 'feature_doc' THEN 3
                ELSE 4
            END as doc_priority
        FROM documents d
        JOIN document_relations dr ON d.id = dr.document_id
        JOIN document_types dt ON d.document_type_id = dt.id
        WHERE dr.related_entity_type = 'version'
        AND dr.related_entity_id IN (
            SELECT v.id FROM versions v 
            WHERE v.version_number IN (
                -- 子查询：获取所有相关版本
                SELECT version_number FROM (...) -- 上面查询的结果
            )
        )
        OR (
            dr.related_entity_type = 'feature'
            AND dr.related_entity_id = (
                SELECT id FROM features WHERE feature_name = %s
            )
        )
        ORDER BY doc_priority, dt.priority_score DESC
        """
        
        # 执行所有查询并合并结果
        results = {}
        results['current_product'] = self.execute_query(current_product_query, [product_name, current_version])
        results['target_versions'] = self.execute_query(feature_versions_query, [product_name, target_feature, current_version])
        results['compatibility'] = self.execute_query(compatibility_query, [current_version, target_feature])
        results['documents'] = self.execute_query(document_query, [target_feature])
        
        # 复杂的结果处理和排序逻辑
        return self.process_complex_results(results)


# ============ 知识图谱方案 ============
class GraphDatabaseQuery:
    def get_upgrade_analysis(self, product_name, current_version, target_feature):
        """
        图谱方案：一个查询解决所有问题
        自然的图遍历，高效且易理解
        """
        
        nql_query = """
        // 从产品节点开始，探索所有相关路径
        MATCH upgrade_path = (current_product:product {name: $product_name})
              -[:has_version]->(current_v:version {number: $current_version})
              -[:upgrade_to*1..3]->(target_v:version)
              -[:supports]->(feature:feature {name: $target_feature})
        
        // 同时查找兼容性路径
        OPTIONAL MATCH compatibility_path = (current_v)
                        -[:compatible_with*1..2]->(compat_v:version)
                        -[:supports]->(feature)
        
        // 查找相关文档
        OPTIONAL MATCH doc_path = (target_v)-[:has_document]->(doc:document)
        OPTIONAL MATCH feature_doc_path = (feature)-[:documented_in]->(feature_doc:document)
        
        // 获取技术依赖
        OPTIONAL MATCH tech_dep_path = (feature)-[:depends_on]->(dep_tech:technology)
                       -[:documented_in]->(tech_doc:document)
        
        RETURN 
            // 升级路径信息
            target_v.number as target_version,
            target_v.release_date as release_date,
            length(upgrade_path) as upgrade_distance,
            
            // 兼容性信息
            collect(DISTINCT compat_v.number) as compatible_versions,
            
            // 文档信息
            collect(DISTINCT {
                name: coalesce(doc.name, feature_doc.name, tech_doc.name),
                type: coalesce(doc.type, feature_doc.type, tech_doc.type),
                path: coalesce(doc.path, feature_doc.path, tech_doc.path),
                priority: coalesce(doc.priority, feature_doc.priority, tech_doc.priority)
            }) as related_documents,
            
            // 技术依赖
            collect(DISTINCT dep_tech.name) as dependencies
            
        ORDER BY upgrade_distance, release_date DESC
        LIMIT 20
        """
        
        # 单次查询执行
        result = self.execute_nql(nql_query, {
            'product_name': product_name,
            'current_version': current_version, 
            'target_feature': target_feature
        })
        
        return result


# ============ 性能对比 ============
"""
传统SQL方案：
- 查询时间：800-1500ms
- 查询数量：4-6个独立查询
- 代码行数：150+ 行SQL
- 维护复杂度：高（表结构变更影响大）
- 扩展难度：困难（需要修改多个查询）

知识图谱方案：
- 查询时间：50-150ms  
- 查询数量：1个统一查询
- 代码行数：30行nGQL
- 维护复杂度：低（Schema变更影响小）
- 扩展难度：容易（添加新关系类型即可）
"""