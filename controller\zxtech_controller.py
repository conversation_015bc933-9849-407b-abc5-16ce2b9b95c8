import copy
import json
import time
from datetime import datetime
from flask import Blueprint, request, g
from service.zxtech_service import _ZXTECH_
from domain.constants.Enums import Hints
## 在这设置配置
from domain.config.zxtech_config import config
from utils.logger.logger_util import logger
from utils.utils import wrapper_response

# 导入监控模块
try:
    from monitor.recall_monitor import get_monitor
    from utils.recall_context import get_recall_data, clear_recall_data
    MONITOR_ENABLED = True
except ImportError:
    logger.warning("监控模块未找到")
    MONITOR_ENABLED = False
    def get_recall_data():
        return {'es_results': [], 'milvus_results': [], 'kg_results': [], 'rerank_results': []}
    def clear_recall_data():
        pass
zxtech_subscribe = Blueprint('zxtech_subscribe', __name__, url_prefix='/zte-ibo-acm-productretrieve')


# 产品技术问答接口
@zxtech_subscribe.route('/faq', methods=['POST'], strict_slashes=False)
def product_chat():
    # 记录开始时间
    start_time = time.time()
    
    # 入参处理
    data = json.loads(request.get_data(as_text=True))
    # 用户输入的文本
    XEmpNo = request.headers.get("X-Emp-No")
    if not XEmpNo:
        return Hints.EMP_ERROR.value
    query = data.get("text")
    history = data.get('history')
    rewriteText = data.get('rewriteText')
    g.XEmpNo = XEmpNo
    g.query = query
    g_copy = copy.deepcopy(g.__dict__)
    if not query:
        logger.warn('input empty \n')
        return wrapper_response('0004', "Empty Input")
    logger.info('''
    ---------- START OF LOG ----------
    ''')
    logger.info('[用户工号]:' + XEmpNo + ' ' + '[用户问题]:' + query)
    chat_uuid = data.get("chatUuid", '')
    _zxtech_ = _ZXTECH_(config)
    
    # 调用服务
    result = _zxtech_(query,XEmpNo,history,rewriteText,g=g_copy)
    
    # 记录监控数据（使用真实的召回数据）
    if MONITOR_ENABLED:
        try:
            response_time = time.time() - start_time
            monitor = get_monitor()
            
            # 获取真实的召回数据
            recall_data = get_recall_data()
            
            # 如果获取到了真实数据，使用真实数据；否则使用默认数据
            es_results = recall_data.get('es_results', [])
            milvus_results = recall_data.get('milvus_results', [])
            kg_results = recall_data.get('kg_results', [])
            rerank_results = recall_data.get('rerank_results', [])
            
            # 如果没有获取到任何召回数据，记录警告
            if not es_results and not kg_results and not milvus_results:
                logger.warning(f"[监控] 未获取到真实召回数据，查询: {query[:30]}...")
                # 创建最小的默认数据以记录这次查询
                es_results = [{'content': '未记录到召回数据', 'doc_name': 'unknown', 'id': 'unknown'}]
                rerank_results = es_results
            
            # 记录到监控系统
            query_id = monitor.record_query(
                query=query,
                es_results=es_results,
                milvus_results=milvus_results,
                kg_results=kg_results,
                rerank_results=rerank_results,
                response_time=response_time
            )
            
            logger.info(f"[监控] FAQ查询已记录 - ID: {query_id}, 查询: {query[:30]}..., 响应时间: {response_time:.3f}s")
            logger.info(f"[监控] ES: {len(es_results)}条, Milvus: {len(milvus_results)}条, KG: {len(kg_results)}条, 重排序: {len(rerank_results)}条")
            
            # 清除召回数据，为下次请求做准备
            clear_recall_data()
            
            # 每10次查询保存并打印排行榜
            if monitor.query_counter % 10 == 0:
                monitor._save_history()
                logger.info(f"[监控] 数据已保存，当前总查询数: {monitor.query_counter}")
                logger.info(f"\n[监控] 排行榜:\n{monitor.get_leaderboard('simple')}")
                
        except Exception as e:
            logger.error(f"[监控] 记录失败: {e}")
            clear_recall_data()  # 确保清理数据
    
    return result

@zxtech_subscribe.route('/info', methods=['POST', 'GET'])
def status_query():
    """
    get请求，返回接口状态
    :return: {"status": "ok"}
    """
    if request.method == 'GET' or request.method == 'POST':
        result = {"status": "ok"}
        return json.dumps(result)

# 监控数据查看接口
@zxtech_subscribe.route('/monitor/stats', methods=['GET'])
def get_monitor_stats():
    """获取监控统计数据"""
    if not MONITOR_ENABLED:
        return json.dumps({"error": "监控功能未启用"}, ensure_ascii=False)
    
    try:
        monitor = get_monitor()
        stats = monitor.get_statistics()
        return json.dumps(stats, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.error(f"获取监控统计失败: {e}")
        return json.dumps({"error": str(e)}, ensure_ascii=False)

@zxtech_subscribe.route('/monitor/leaderboard', methods=['GET'])
def get_monitor_leaderboard():
    """获取召回源排行榜"""
    if not MONITOR_ENABLED:
        return json.dumps({"error": "监控功能未启用"}, ensure_ascii=False)
    
    try:
        monitor = get_monitor()
        # 获取排行榜文本
        leaderboard_text = monitor.get_leaderboard('detailed')
        # 同时返回结构化数据
        leaderboard_data = {
            "text": leaderboard_text,
            "data": monitor.leaderboard,
            "query_count": monitor.query_counter,
            "last_update": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        return json.dumps(leaderboard_data, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.error(f"获取监控排行榜失败: {e}")
        return json.dumps({"error": str(e)}, ensure_ascii=False)

@zxtech_subscribe.route('/monitor/reset', methods=['POST'])
def reset_monitor():
    """重置监控数据"""
    if not MONITOR_ENABLED:
        return json.dumps({"error": "监控功能未启用"}, ensure_ascii=False)
    
    try:
        monitor = get_monitor()
        monitor.reset()
        return json.dumps({"status": "ok", "message": "监控数据已重置"}, ensure_ascii=False)
    except Exception as e:
        logger.error(f"重置监控数据失败: {e}")
        return json.dumps({"error": str(e)}, ensure_ascii=False)