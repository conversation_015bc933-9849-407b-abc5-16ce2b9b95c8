import hmac
import hashlib
import datetime
import binascii
from utils.logger.logger_util import logger

ENCODING = 'utf-8'

"""
使用 HMAC 签名方法对 content 进行签名

:param content: 被签名的字符串
:param secret: 密钥
:param algorithm: 算法，如 'sha256', 'sha1'
:return: 签名后的字节串
"""
def hmac_encrypt(content, secret, algorithm):
    content = content if content else ''
    secret = secret.encode(ENCODING)
    content = content.encode(ENCODING)
    mac = hmac.new(secret, content, hashlib.__dict__[algorithm])
    return mac.digest()

def encode_hex(byte_array):
    return binascii.hexlify(byte_array).decode('utf-8')

def encrypt(entity, key):
    try:
        a=hmac_encrypt(entity, key, 'sha256')
        return encode_hex(a)
    except Exception as e:
        logger.info(f"Error: {e}")
        return None

#签名生成
def do_signature(access_key, content, access_secret):
    if not access_key or not access_secret:
        raise ValueError("Access key and access secret cannot be empty")

    sf = access_key
    if content:
        sf += ":" + content

    today = datetime.datetime.now().strftime("%Y%m%d")
    sf += ":" + today
    return encrypt(sf, access_secret)



