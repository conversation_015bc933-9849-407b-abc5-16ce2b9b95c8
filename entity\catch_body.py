from domain.config.zxtech_config import config
from utils.logger.logger_util import logger
from retrieve.Recall_data import RecallResult
from utils.entity_util import check_spell, process_strings
import re

es_recall = RecallResult(config)


class dp_entity(object):
    def __init__(self, config):
        self.es_entity_index = config['catch_body']['entity_es_index']

    def es_query_posturl(self, entity_list):
        result_map = {}
        for entity in entity_list:
            jsonObject = {
                "matchMustQueryConditions": {entity: ["product", "series"]},
                "indexName": [self.es_entity_index],
                "needQuery": True,
                "pageNo": 1,
                "pageSize": 5
            }
            resultJsonArray = es_recall.entity_search_es(jsonObject)
            search_map = {}
            for result in resultJsonArray:
                search_entity = result['product']
                product_id = result['id']
                series_id = result['series_id']
                score = result['score']
                search_map[search_entity] = {"product_id": product_id, "series_id": series_id, "score": score}

            result_map[entity] = search_map
        return result_map

    def __call__(self, query: str):
        kg_prodoct_res = self.es_query_posturl([query])
        # 召回为空
        if kg_prodoct_res.values() == {}:
            return [], {}
        pro2id_list = {}
        product_list = []
        series_list = []
        for k, v in list(kg_prodoct_res.values())[0].items():
            del v['score']
            pro2id_list[k] = v
            product_list.append(v["product_id"].replace("product", ''))
            series_list.append(v["series_id"].replace("series", ''))
        short_product_list = []
        short_series_list = []
        for series in series_list:
            short_series_list.append(series.replace(series.split(' ')[0], '').strip())
        for product in product_list:
            name = product.replace(product.split(' ')[0], '').strip()
            short_product_list.append(name)

        all_dict = {}
        for i, pro in enumerate(short_product_list):
            all_dict[pro] = product_list[i]
        for i, ser in enumerate(short_series_list):
            all_dict[ser] = series_list[i]
        # 判断是否为全称产品型号
        for product in product_list:
            pattern = re.compile(re.escape(product))
            if re.search(pattern, query):
                return product, {product: pro2id_list[product]}
        # 判断是否为简称产品型号
        for product in short_product_list:
            pattern = re.compile(re.escape(product))
            if re.search(pattern, query):
                return product, {product: pro2id_list[all_dict[product]]}
            # 匹配后缀
            pattern_last = re.compile(re.escape(product.split()[-1]))
            if re.search(pattern_last, query):
                return product, {product: pro2id_list[all_dict[product]]}
        # 判断是否为全称系列名
        for series in series_list:
            pattern = re.compile(re.escape(series))
            if re.search(pattern, query):
                return series, {series: {"product_id": "", "series_id": "series" + series}}
        # 判断是否为简称系列名
        for series in short_series_list:
            pattern = re.compile(re.escape(series))
            if re.search(pattern, query):
                return series, {series: {"product_id": "", "series_id": "series" + all_dict[series]}}

        # 都不是就返回为空
        return "", {}
