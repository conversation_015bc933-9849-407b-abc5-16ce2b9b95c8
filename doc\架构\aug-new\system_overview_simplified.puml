@startuml ZTE产品检索系统概览图
!theme plain
title ZTE产品检索系统 - 系统概览图 (简化版)

skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 12
skinparam componentStyle rectangle

' 用户层
actor "用户" as User #87CEEB

' 应用层
package "**ZTE产品检索系统**" as MainSystem #E6F3FF {
    
    ' Web接口层
    component "Web API接口" as WebAPI #B0E0E6 {
        [Flask应用]
        [RESTful API]
        [/faq问答接口]
    }
    
    ' 核心业务层
    component "智能问答引擎" as QAEngine #F0E68C {
        [查询理解]
        [实体识别]
        [多轮对话]
        [答案生成]
    }
    
    ' 检索层
    component "多路检索引擎" as RetrievalEngine #90EE90 {
        [文本检索]
        [向量检索]
        [图谱检索]
        [结果融合]
    }
}

' 数据存储层
package "**数据存储**" as DataStorage #DDA0DD {
    database "ElasticSearch\n文档索引" as ES
    database "Milvus\n向量数据库" as Milvus  
    database "Nebula\n知识图谱" as KG
}

' AI服务层
cloud "**AI服务**" as AIServices #FFD700 {
    component "NebulaBiz LLM\n大语言模型" as LLM
    component "BGE-M3\n向量化+重排序" as BGE
}

' 基础服务层
cloud "**基础服务**" as BaseServices #FFA500 {
    component "UAC认证中心" as UAC
    component "Apollo配置中心" as Apollo
}

' 主要交互流程
User --> WebAPI : ①发起查询请求
WebAPI --> UAC : ②身份认证
WebAPI --> QAEngine : ③业务处理

QAEngine --> BGE : ④文本向量化
QAEngine --> RetrievalEngine : ⑤多路检索

RetrievalEngine --> ES : ⑥文本检索
RetrievalEngine --> Milvus : ⑦向量检索  
RetrievalEngine --> KG : ⑧图谱检索

RetrievalEngine --> BGE : ⑨结果重排序
QAEngine --> LLM : ⑩答案生成
QAEngine --> WebAPI : ⑪返回结果
WebAPI --> User : ⑫响应用户

' 配置和监控
Apollo --> QAEngine : 配置管理
QAEngine --> Apollo : 监控数据

' 核心流程说明
note right of QAEngine
    **核心处理流程**
    1. 查询预处理和重写
    2. 实体识别和提取
    3. 多路并行检索
    4. 结果融合和重排序
    5. LLM生成最终答案
    6. 流式响应返回
end note

note bottom of RetrievalEngine
    **三路检索策略**
    • ES: 基于关键词的文本检索
    • Milvus: 基于语义的向量检索  
    • KG: 基于关系的图谱检索
    
    **融合算法**
    • 多源结果合并
    • 去重和过滤
    • 相关性排序
end note

note top of AIServices
    **AI能力支撑**
    • NebulaBiz: 中兴定制大模型
    • BGE-M3: 多语言向量模型
    • 支持中英文处理
    • 流式输出响应
end note

' 技术特点标注
rectangle "**系统特点**" as SystemFeatures #F0F8FF {
    note as FeaturesNote
        **🚀 核心特性**
        
        ✅ **智能问答**: 基于大模型的智能问答系统
        ✅ **多路检索**: ES+Milvus+KG三路并行检索
        ✅ **语义理解**: BGE-M3多语言向量模型
        ✅ **实时响应**: 流式输出，响应时间<2秒
        ✅ **高可用**: 分布式架构，支持集群部署
        
        **📊 性能指标**
        
        • 并发处理: 1000+ QPS
        • 准确率: >85%
        • 可用性: 99.9%
        • 响应时间: <2秒(P95)
        
        **🔧 技术栈**
        
        • 后端: Python Flask
        • AI: NebulaBiz LLM + BGE-M3
        • 存储: ES + Milvus + Nebula
        • 部署: Docker + K8s
    end note
}

' 数据流向
rectangle "**数据流向**" as DataFlow #FFF8DC {
    note as DataFlowNote
        **📈 主要数据流**
        
        **输入数据**
        用户查询文本 → 参数验证 → 预处理
        
        **处理数据**  
        文本分析 → 实体提取 → 向量化 → 检索
        
        **输出数据**
        检索结果 → 重排序 → LLM生成 → 格式化
        
        **监控数据**
        性能指标 → 召回统计 → 质量评估
    end note
}

@enduml
