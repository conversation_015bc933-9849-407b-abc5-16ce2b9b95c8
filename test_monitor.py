"""
召回监控功能测试脚本
模拟查询并展示监控效果
"""

import random
import time
from monitor.recall_monitor import get_monitor

def simulate_es_results(count=20):
    """模拟ES召回结果"""
    return [
        {'content': f'ES文档{i}', 'doc_name': f'doc_es_{i}', 'id': f'es_{i}', 'score': random.random()}
        for i in range(count)
    ]

def simulate_milvus_results(count=15):
    """模拟Milvus召回结果（当前返回空）"""
    return []  # Milvus已被禁用

def simulate_kg_results(has_result=True):
    """模拟KG召回结果"""
    if has_result and random.random() > 0.5:
        return [f"KG知识图谱结果：产品型号ZXR10 M6000的详细技术参数..."]
    return []

def simulate_rerank_results(es_results, milvus_results, kg_results):
    """模拟重排序结果"""
    # 合并所有结果
    all_results = []
    
    # 添加ES结果
    for idx, item in enumerate(es_results[:10]):
        all_results.append({
            'content': item['content'],
            'doc_name': item['doc_name'],
            'id': item['id'],
            'score': 1.0 / (idx + 1)  # 模拟重排序分数
        })
    
    # 如果有KG结果，随机插入到前5位
    if kg_results:
        insert_pos = random.randint(0, min(4, len(all_results)))
        all_results.insert(insert_pos, {
            'content': kg_results[0],
            'doc_name': 'kg_doc',
            'id': 'kg_001',
            'score': 0.95
        })
    
    # 打乱一部分顺序，模拟重排序效果
    if len(all_results) > 5:
        top5 = all_results[:5]
        rest = all_results[5:]
        random.shuffle(rest)
        all_results = top5 + rest
    
    return all_results[:20]  # 返回前20个

def run_simulation(num_queries=50):
    """运行模拟测试"""
    monitor = get_monitor()
    
    print("=" * 80)
    print("开始召回监控模拟测试")
    print("=" * 80)
    
    queries = [
        "ZXR10 M6000-8S Plus如何配置VLAN？",
        "5G基站的功耗是多少？",
        "如何进行系统升级？",
        "产品的最大传输速率？",
        "支持哪些网络协议？",
        "如何配置端口镜像？",
        "系统日志如何查看？",
        "告警信息怎么处理？",
        "备份恢复操作步骤？",
        "性能监控如何配置？"
    ]
    
    for i in range(num_queries):
        # 随机选择一个查询
        query = random.choice(queries)
        
        # 模拟召回
        es_results = simulate_es_results(random.randint(10, 30))
        milvus_results = simulate_milvus_results()
        kg_results = simulate_kg_results(i % 3 == 0)  # 每3个查询有1个触发KG
        
        # 模拟重排序
        rerank_results = simulate_rerank_results(es_results, milvus_results, kg_results)
        
        # 模拟响应时间
        response_time = random.uniform(0.1, 0.5)
        
        # 记录到监控系统
        query_id = monitor.record_query(
            query=query,
            es_results=es_results,
            milvus_results=milvus_results,
            kg_results=kg_results,
            rerank_results=rerank_results,
            response_time=response_time
        )
        
        # 显示进度
        if (i + 1) % 10 == 0:
            print(f"\n已完成 {i + 1}/{num_queries} 次查询")
            print("-" * 80)
            print(monitor.get_leaderboard('simple'))
        
        # 模拟查询间隔
        time.sleep(0.05)
    
    print("\n" * 2)
    print("=" * 80)
    print("                    最终统计结果")
    print("=" * 80)
    
    # 显示详细排行榜
    print(monitor.get_leaderboard('detailed'))
    
    # 显示统计报告
    print("\n" * 2)
    print(monitor.get_statistics_report())
    
    return monitor

def test_monitor_features():
    """测试监控功能的各个特性"""
    print("\n测试监控功能特性")
    print("=" * 80)
    
    monitor = get_monitor()
    
    # 测试1：记录单个查询
    print("\n1. 测试记录单个查询...")
    es_results = [
        {'content': 'ES结果1', 'doc_name': 'doc1', 'id': '1'},
        {'content': 'ES结果2', 'doc_name': 'doc2', 'id': '2'}
    ]
    kg_results = ['KG知识图谱结果']
    rerank_results = [
        {'content': 'KG知识图谱结果', 'doc_name': 'kg', 'id': 'kg1'},
        {'content': 'ES结果1', 'doc_name': 'doc1', 'id': '1'}
    ]
    
    query_id = monitor.record_query(
        query="测试查询",
        es_results=es_results,
        milvus_results=[],
        kg_results=kg_results,
        rerank_results=rerank_results,
        response_time=0.123
    )
    print(f"[OK] 查询已记录，ID: {query_id}")
    
    # 测试2：获取最近查询
    print("\n2. 测试获取最近查询...")
    recent = list(monitor.query_history)[-1]
    print(f"[OK] 最近查询: {recent.query_text[:30]}")
    print(f"   ES贡献率: {recent.es_metrics.contribution_rate:.1f}%")
    print(f"   KG贡献率: {recent.kg_metrics.contribution_rate:.1f}%")
    
    # 测试3：查看排行榜
    print("\n3. 当前排行榜:")
    print(monitor.get_leaderboard('simple'))
    
    print("\n[OK] 所有测试通过！")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == '--test':
        # 运行功能测试
        test_monitor_features()
    else:
        # 运行完整模拟
        print("\n启动召回监控模拟测试")
        print("将模拟50次查询，展示监控效果")
        print("-" * 80)
        
        monitor = run_simulation(50)
        
        print("\n" * 2)
        print("测试完成！")
        print(f"总计处理查询: {monitor.query_counter} 次")
        print(f"监控数据已保存至: {monitor.save_path}")
        
        print("\n提示：")
        print("1. 可以通过API接口查看监控数据:")
        print("   - GET /recall-monitor/leaderboard - 查看排行榜")
        print("   - GET /recall-monitor/statistics - 查看统计报告")
        print("   - GET /recall-monitor/recent - 查看最近查询")
        print("2. 运行 'python test_monitor.py --test' 进行功能测试")