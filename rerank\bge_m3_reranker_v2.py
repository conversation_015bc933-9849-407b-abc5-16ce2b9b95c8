import time
from typing import List,Optional
import requests
from utils.http_util import post_no_proxy
from utils.logger.logger_util import logger
from domain.config.zxtech_config import config
from utils.logger.log_wrapper import wrap_log_content
from sklearn.metrics.pairwise import cosine_similarity as cosine_similarity_tfidfcos
from spacy_pkuseg import pkuseg
from sklearn.feature_extraction.text import TfidfVectorizer
threshold_origin=config['rerank']['Reranker_tfidf']['threshold_origin']
threshold_tfidf= config['rerank']['Reranker_tfidf']['threshold_tfidf']
threshold_tfidf_binary=config['rerank']['Reranker_tfidf']['threshold_tfidf_binary']
threshold_origin_max= config['rerank']['Reranker_tfidf']['threshold_origin_max']
threshold_origin_min= config['rerank']['Reranker_tfidf']['threshold_origin_min']
threshold_origin_advantage= config['rerank']['Reranker_tfidf']['threshold_origin_advantage']
threshold_tfidf_max= config['rerank']['Reranker_tfidf']['threshold_tfidf_max']
threshold_tfidf_binary_max= config['rerank']['Reranker_tfidf']['threshold_tfidf_binary_max']

rerank_url = config['rerank']['url']

def remove_duplicates_by_key(lst, key):
    seen = set()
    result = []
    for d in lst:
        # 检查当前字典的 id 值是否已经见过
        if d[key] not in seen:
            # 如果 id 值没有见过，则将字典加入结果列表，并将 id 添加到 seen 集合中
            result.append(d)
        seen.add(d[key])
    return result

class BGEM3RerankV2(object):
    def __init__(self,config):
        self.url = config['rerank']['url']

    def rerankV2(self,query,candidates) -> List:
        candidates = remove_duplicates_by_key(candidates,"id")
        candidates_content = [i['content'] for i in candidates]
        bo = {
            "target":query,
            "candidates_list":candidates_content
        }
        
        try:
            receive = post_no_proxy(
                json=bo,
                url=self.url,
                verify=True,
            )
            result = receive.json()['bo']
            for i, dictionary in enumerate(candidates):
                dictionary['rerank_score'] = result[i][1]
            result = sorted(candidates, key=lambda x:x['rerank_score'], reverse=True)
        except Exception as e:
            return None
        
        return result


def check_similarity_tf_intersection(query, candidates, time_record_dict=None, g=None, flag = False):
    if g is None:
        g = {}
    bo = {
        "target": query,
        "candidates_list": candidates
    }
    try:
        before = time.time()
        # 计算url传过来的分数
        receive = post_no_proxy(
            json=bo,
            url=rerank_url,
            verify=True,
            timeout=30
        )
        result_origin = receive.json()['bo']

        origin_candidates = candidates

        # 创建 pkuseg 分词器对象"spacy_ontonotes"
        seg = pkuseg(model_name='resource/spacy_ontonotes')
        # 对 query 和 candidates 进行分词
        query = seg.cut(query)
        candidates = [seg.cut(text) for text in candidates]

        # 将 query 和 candidates 联合起来转为字符串
        tokentext = [query] + candidates
        tokentext = [" ".join(tokens) for tokens in tokentext]

        # 将字符串进行向量转化
        vectorizer = TfidfVectorizer(norm='l2', min_df=0.15)
        vectorizer_binary = TfidfVectorizer(norm='l2', min_df=0.15, binary=True)
        vector = vectorizer.fit_transform(tokentext)
        vectorb_binary = vectorizer_binary.fit_transform(tokentext)

        # 计算余弦相似度：分为二元和非二元两种
        cosine_similarities = cosine_similarity_tfidfcos(vector[0:1], vector[1:]).flatten()
        cosine_similarities_binary = cosine_similarity_tfidfcos(vectorb_binary[0:1], vectorb_binary[1:]).flatten()

        after = time.time()
        tc = after - before

        # logger.info(f"溯源文档重排耗时:{tc} s")

        if time_record_dict is not None:
            time_record_dict['source_rerank'] = tc
        result_tfidf = [[candidate, score] for candidate, score in zip(origin_candidates, cosine_similarities)]
        result_tfidf_binary = [[candidate, score] for candidate, score in
                               zip(origin_candidates, cosine_similarities_binary)]
        similarities = []
        scores = []
        scores_binary = []
        scores_origin = []

        has_greater_than_055 = False
        # 对分数进行取交集的操作，且设定确定值：即分数大于对应的分数，也直接取即可
        for res_origin, res_tfidf, res_tfidf_binary in zip(result_origin, result_tfidf, result_tfidf_binary):
            scores.append(res_tfidf[1])
            scores_binary.append(res_tfidf_binary[1])
            scores_origin.append((res_origin[1]))
            similarities.append(judge_threshold(res_origin[1],res_tfidf[1],res_tfidf_binary[1]))

            # 如果有很有优势的答案，那么将会进行冗余答案去除
            if judge_advantage(res_origin[1]):
                has_greater_than_055 = True

        # 如果条件都满足，则将similarities 对应索引的值设为 False
        if has_greater_than_055:
            i = 0
            for value, value_binary, value_origin in zip(scores, scores_binary, scores_origin):
                if judge_weakness(value_origin):
                    similarities[i] = False
                i = i + 1

        # 定义权重
        weights = [0.2, 0.2, 0.6]

        # 计算加权分数
        weighted_scores = [
            s1 * weights[0] + s2 * weights[1] + s3 * weights[2]
            for s1, s2, s3 in zip(scores, scores_binary, scores_origin)
        ]

        # 如果相似性全为false,则将分数最高的设置为ture
        if flag and all(value is False for value in similarities):
            # 找到B中的最大分数及其对应索引
            max_score_index = weighted_scores.index(max(weighted_scores))
            # 将A中对应位置的值改为True
            similarities[max_score_index] = True

    except Exception as e:
        logger.error(wrap_log_content(f"相关性得分计算报错:{e}", g))
        return [], []

    return similarities, weighted_scores


def judge_threshold(res_origin, res_tfidf, res_tfidf_binary):
    # 如果res_origin大于0.9996，或者res_tfidf大于0.65，或者res_tfidf_binary大于0.65，返回True

    if res_origin > threshold_origin_max or (res_tfidf > threshold_tfidf_max and res_origin > threshold_origin_min) or (res_tfidf_binary > threshold_tfidf_binary_max and res_origin > threshold_origin_min):
        return True
    # 否则，如果res_origin大于threshold_origin，并且res_tfidf大于threshold_tfidf或res_tfidf_binary大于threshold_tfidf_binary，返回True
    elif res_origin > threshold_origin and (res_tfidf > threshold_tfidf or res_tfidf_binary > threshold_tfidf_binary):
        return True
    # 否则，返回False
    else:
        return False


def judge_advantage(res_origin):
    # 如果res_tfidf或res_tfidf_binary大于0.55，并且res_origin大于threshold_origin，返回True
    return  res_origin > threshold_origin_advantage


def judge_weakness(res_origin):
    # 如果value、value_binary和它们的和都小于0.72，并且value_binary小于0.42，并且value也小于0.42，并且value_origin小于0.997，或者value_origin小于0.1，返回True
    return  res_origin < threshold_origin_advantage


