# 产品技术问答系统技术架构详细设计

## 1. 系统架构图

### 1.1 方案一：DN Studio统一平台架构

```mermaid
graph TB
    subgraph "数据源层"
        TSM[TSM文档库]
        InfoGo[Info-Go系统]
    end
    
    subgraph "数据处理层"
        DNStudio[DN Studio流水线采集]
        AutoSync[自动同步服务]
    end
    
    subgraph "存储层"
        KnowledgeDB[(DN Studio知识库<br/>关键字+向量索引)]
        KnowledgeGraph[(知识图谱<br/>产品关系)]
    end
    
    subgraph "检索层"
        MultiRecall[多路召回]
        HybridSearch[混合检索]
        GraphSearch[图关系检索]
    end
    
    subgraph "重排层"
        Rerank[结果重排]
        BGE[BGE-M3]
        TFIDF[TF-IDF]
        Fusion[融合打分]
    end
    
    subgraph "生成层"
        AnswerGen[答案生成]
        LLM[大语言模型]
        FactVerify[事实性验证]
    end
    
    subgraph "应用层"
        WebUI[Web界面]
        API[REST API]
        ChatBot[聊天机器人]
    end
    
    TSM --> DNStudio
    InfoGo --> AutoSync
    DNStudio --> KnowledgeDB
    AutoSync --> KnowledgeGraph
    
    KnowledgeDB --> HybridSearch
    KnowledgeGraph --> GraphSearch
    HybridSearch --> MultiRecall
    GraphSearch --> MultiRecall
    
    MultiRecall --> Rerank
    BGE --> Fusion
    TFIDF --> Fusion
    Rerank --> Fusion
    Fusion --> AnswerGen
    
    LLM --> AnswerGen
    FactVerify --> AnswerGen
    AnswerGen --> WebUI
    AnswerGen --> API
    AnswerGen --> ChatBot
```

### 1.2 方案二：混合架构优化方案

```mermaid
graph TB
    subgraph "数据源层"
        TSM2[TSM文档库]
        InfoGo2[Info-Go系统]
    end
    
    subgraph "数据处理层"
        DNStudio2[DN Studio采集]
        AutoSync2[自动同步服务]
        DataCoordinator[数据同步协调器]
    end
    
    subgraph "存储层"
        ES[(Elasticsearch<br/>关键字索引)]
        VectorDB[(向量数据库<br/>嵌入表示)]
        KG[(知识图谱<br/>产品关系)]
    end
    
    subgraph "检索层"
        MultiRecall2[多路召回]
        KeywordSearch[关键字检索]
        VectorSearch[向量检索]
        GraphSearch2[图关系检索]
    end
    
    subgraph "重排层"
        AdvancedRerank[高级重排]
        BGE2[BGE-M3]
        TFIDF2[TF-IDF]
        CustomFusion[自定义融合算法]
    end
    
    subgraph "生成层"
        EnhancedGen[增强生成]
        MultiLLM[多模型路由]
        WebSearch[联网查询]
        ContextMgr[上下文管理]
    end
    
    subgraph "应用层"
        WebUI2[Web界面]
        API2[REST API]
        ChatBot2[聊天机器人]
    end
    
    TSM2 --> DNStudio2
    InfoGo2 --> AutoSync2
    DNStudio2 --> DataCoordinator
    AutoSync2 --> DataCoordinator
    
    DataCoordinator --> ES
    DataCoordinator --> VectorDB
    DataCoordinator --> KG
    
    ES --> KeywordSearch
    VectorDB --> VectorSearch
    KG --> GraphSearch2
    
    KeywordSearch --> MultiRecall2
    VectorSearch --> MultiRecall2
    GraphSearch2 --> MultiRecall2
    
    MultiRecall2 --> AdvancedRerank
    BGE2 --> CustomFusion
    TFIDF2 --> CustomFusion
    AdvancedRerank --> CustomFusion
    CustomFusion --> EnhancedGen
    
    MultiLLM --> EnhancedGen
    WebSearch --> EnhancedGen
    ContextMgr --> EnhancedGen
    
    EnhancedGen --> WebUI2
    EnhancedGen --> API2
    EnhancedGen --> ChatBot2
```

## 2. 核心模块设计

### 2.1 数据采集模块

#### 2.1.1 TSM数据采集器
```java
@Component
public class TSMDataCollector {
    
    // 支持的文档类型
    private static final Set<String> SUPPORTED_FORMATS = 
        Set.of("doc", "docx", "pdf", "xls", "xlsx", "ppt", "pptx", "jpg", "png");
    
    // 多模态文档解析器
    @Autowired
    private Map<String, DocumentParser> parsers;
    
    public List<ProcessedDocument> collectDocuments(CollectionConfig config) {
        List<TSMDocument> rawDocs = fetchFromTSM(config);
        return rawDocs.parallelStream()
            .filter(this::isValidDocument)
            .map(this::parseDocument)
            .map(this::preprocessDocument)
            .collect(Collectors.toList());
    }
    
    private ProcessedDocument parseDocument(TSMDocument doc) {
        DocumentParser parser = parsers.get(doc.getFileType());
        if (parser == null) {
            throw new UnsupportedFormatException("不支持的文档格式: " + doc.getFileType());
        }
        return parser.parse(doc);
    }
}
```

#### 2.1.2 文档预处理器
```java
@Service
public class DocumentPreprocessor {
    
    public ProcessedDocument preprocess(RawDocument rawDoc) {
        // 1. 去除噪音数据
        String cleanedContent = removeNoise(rawDoc.getContent());
        
        // 2. 结构化信息提取
        DocumentStructure structure = extractStructure(cleanedContent);
        
        // 3. 元数据生成
        DocumentMetadata metadata = generateMetadata(rawDoc, structure);
        
        // 4. 文档切分
        List<DocumentChunk> chunks = chunkDocument(cleanedContent, structure);
        
        return ProcessedDocument.builder()
            .content(cleanedContent)
            .structure(structure)
            .metadata(metadata)
            .chunks(chunks)
            .build();
    }
    
    private String removeNoise(String content) {
        return content
            .replaceAll("页眉.*?\\n", "")
            .replaceAll("页脚.*?\\n", "")
            .replaceAll("\\s+", " ")
            .trim();
    }
}
```

### 2.2 检索模块

#### 2.2.1 混合检索引擎
```java
@Service
public class HybridRetrievalEngine {
    
    @Autowired
    private KeywordSearchService keywordService;
    
    @Autowired
    private SemanticSearchService semanticService;
    
    @Autowired
    private GraphSearchService graphService;
    
    public RetrievalResult search(SearchRequest request) {
        // 1. 查询预处理
        ProcessedQuery processedQuery = preprocessQuery(request.getQuery());
        
        // 2. 多路并行召回
        List<CompletableFuture<List<Document>>> futures = Arrays.asList(
            CompletableFuture.supplyAsync(() -> 
                keywordService.search(processedQuery, request.getKeywordConfig())),
            CompletableFuture.supplyAsync(() -> 
                semanticService.search(processedQuery, request.getSemanticConfig())),
            CompletableFuture.supplyAsync(() -> 
                graphService.search(processedQuery, request.getGraphConfig()))
        );
        
        // 3. 等待所有召回完成
        List<List<Document>> allResults = futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toList());
        
        // 4. 结果融合
        List<Document> mergedResults = mergeResults(allResults);
        
        // 5. 重排序
        List<Document> rerankedResults = rerank(mergedResults, processedQuery);
        
        return RetrievalResult.builder()
            .documents(rerankedResults)
            .totalCount(rerankedResults.size())
            .searchTime(System.currentTimeMillis() - request.getStartTime())
            .build();
    }
}
```

#### 2.2.2 重排序算法
```java
@Component
public class AdvancedReranker {
    
    @Autowired
    private BGEModel bgeModel;
    
    @Autowired
    private TFIDFCalculator tfidfCalculator;
    
    public List<Document> rerank(List<Document> documents, ProcessedQuery query) {
        return documents.parallelStream()
            .map(doc -> calculateRelevanceScore(doc, query))
            .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
            .map(ScoredDocument::getDocument)
            .collect(Collectors.toList());
    }
    
    private ScoredDocument calculateRelevanceScore(Document doc, ProcessedQuery query) {
        // 1. BGE语义相似度
        double semanticScore = bgeModel.calculateSimilarity(doc.getContent(), query.getText());
        
        // 2. TF-IDF关键词匹配度
        double keywordScore = tfidfCalculator.calculate(doc.getContent(), query.getKeywords());
        
        // 3. 图关系相关度
        double graphScore = calculateGraphRelevance(doc, query);
        
        // 4. 融合打分
        double finalScore = 0.5 * semanticScore + 0.3 * keywordScore + 0.2 * graphScore;
        
        return new ScoredDocument(doc, finalScore);
    }
}
```

### 2.3 生成模块

#### 2.3.1 答案生成器
```java
@Service
public class AnswerGenerator {
    
    @Autowired
    private LLMService llmService;
    
    @Autowired
    private PromptTemplate promptTemplate;
    
    @Autowired
    private FactVerifier factVerifier;
    
    public GeneratedAnswer generate(GenerationRequest request) {
        // 1. 构建提示词
        String prompt = promptTemplate.build(
            request.getQuery(),
            request.getContexts(),
            request.getConversationHistory()
        );
        
        // 2. 调用大模型生成
        LLMResponse response = llmService.generate(LLMRequest.builder()
            .prompt(prompt)
            .maxTokens(1000)
            .temperature(0.7)
            .build());
        
        // 3. 事实性验证
        VerificationResult verification = factVerifier.verify(
            response.getText(),
            request.getQuery(),
            request.getContexts()
        );
        
        // 4. 答案后处理
        ProcessedAnswer processedAnswer = postProcess(response.getText(), request.getContexts());
        
        return GeneratedAnswer.builder()
            .answer(processedAnswer.getText())
            .references(processedAnswer.getReferences())
            .confidence(verification.getConfidence())
            .verificationStatus(verification.getStatus())
            .build();
    }
}
```

#### 2.3.2 多轮对话管理
```java
@Service
public class ConversationManager {
    
    @Autowired
    private ConversationRepository conversationRepo;
    
    @Autowired
    private ContextExtractor contextExtractor;
    
    public ConversationContext getContext(String sessionId) {
        Conversation conversation = conversationRepo.findBySessionId(sessionId);
        if (conversation == null) {
            return ConversationContext.empty();
        }
        
        // 提取对话上下文
        List<String> recentQueries = conversation.getRecentQueries(5);
        List<String> recentAnswers = conversation.getRecentAnswers(5);
        Map<String, Object> entities = contextExtractor.extractEntities(recentQueries);
        
        return ConversationContext.builder()
            .recentQueries(recentQueries)
            .recentAnswers(recentAnswers)
            .entities(entities)
            .build();
    }
    
    public void updateContext(String sessionId, String query, String answer) {
        Conversation conversation = conversationRepo.findBySessionId(sessionId);
        if (conversation == null) {
            conversation = new Conversation(sessionId);
        }
        
        conversation.addTurn(query, answer);
        conversationRepo.save(conversation);
    }
}
```

## 3. 数据流设计

### 3.1 数据同步流程
```
1. TSM数据源变更检测
2. 增量数据提取
3. 多模态文档解析
4. 数据预处理和清洗
5. 向量化处理
6. 存储系统同步
7. 索引更新
8. 同步状态记录
```

### 3.2 查询处理流程
```
1. 用户查询接收
2. 查询预处理和增强
3. 多路并行召回
4. 结果融合和去重
5. 重排序算法
6. 权限过滤
7. 答案生成
8. 事实性验证
9. 格式化输出
10. 对话历史更新
```

## 4. 接口设计

### 4.1 REST API接口
```yaml
# 问答接口
POST /api/v1/qa/ask
Request:
  query: string          # 用户问题
  sessionId: string      # 会话ID
  context: object        # 上下文信息
  config: object         # 检索配置

Response:
  answer: string         # 生成的答案
  references: array      # 参考文档
  confidence: number     # 置信度
  sessionId: string      # 会话ID

# 文档上传接口
POST /api/v1/documents/upload
Request:
  file: multipart        # 文档文件
  metadata: object       # 元数据

Response:
  documentId: string     # 文档ID
  status: string         # 处理状态

# 检索接口
POST /api/v1/search
Request:
  query: string          # 检索查询
  filters: object        # 过滤条件
  limit: number          # 返回数量

Response:
  documents: array       # 检索结果
  totalCount: number     # 总数量
  searchTime: number     # 检索耗时
```

### 4.2 WebSocket接口
```javascript
// 实时问答接口
ws://domain/ws/qa

// 消息格式
{
  "type": "question",
  "sessionId": "session-123",
  "query": "用户问题",
  "timestamp": 1234567890
}

{
  "type": "answer",
  "sessionId": "session-123",
  "answer": "生成的答案",
  "references": [...],
  "confidence": 0.85,
  "timestamp": 1234567890
}
```

## 5. 部署架构

### 5.1 微服务架构
```
- 网关服务 (API Gateway)
- 用户服务 (User Service)
- 文档服务 (Document Service)
- 检索服务 (Retrieval Service)
- 生成服务 (Generation Service)
- 对话服务 (Conversation Service)
- 监控服务 (Monitoring Service)
```

### 5.2 容器化部署
```dockerfile
# 应用服务镜像
FROM openjdk:11-jre-slim
COPY target/qa-service.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

```yaml
# Kubernetes部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: qa-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: qa-service
  template:
    metadata:
      labels:
        app: qa-service
    spec:
      containers:
      - name: qa-service
        image: qa-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
```

## 6. 总结

本技术架构设计文档详细描述了两种方案的技术实现细节，包括核心模块设计、数据流设计、接口设计和部署架构。通过模块化设计和微服务架构，确保系统的可扩展性、可维护性和高可用性。
