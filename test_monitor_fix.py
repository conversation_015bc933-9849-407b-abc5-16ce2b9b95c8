"""
测试监控修复效果
验证 /faq 接口是否正确记录了真实的召回数据
"""

import requests
import json
import time
from datetime import datetime

# API配置
BASE_URL = "http://localhost:5001"  # 根据实际情况修改
FAQ_ENDPOINT = "/zte-ibo-acm-productretrieve/faq"

def test_faq_api(query, emp_no="10317843"):
    """
    测试 FAQ 接口
    """
    url = BASE_URL + FAQ_ENDPOINT
    
    headers = {
        "Content-Type": "application/json",
        "X-Emp-No": emp_no
    }
    
    data = {
        "text": query,
        "history": [],
        "rewriteText": "",
        "chatUuid": f"test_{datetime.now().strftime('%Y%m%d%H%M%S')}"
    }
    
    try:
        print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 发送查询: {query[:50]}...")
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            print(f"✓ 请求成功，响应时间: {response.elapsed.total_seconds():.2f}s")
            return True
        else:
            print(f"✗ 请求失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ 请求异常: {e}")
        return False

def run_multiple_tests():
    """
    运行多个测试查询
    """
    test_queries = [
        "ZXR10 M6000-8S Plus的电源功耗是多少？",
        "如何配置OSPF协议？",
        "BGP路由协议的工作原理",
        "交换机VLAN配置方法",
        "光模块的型号有哪些？",
        "设备告警如何处理？",
        "端口镜像怎么配置？",
        "SNMP协议配置步骤",
        "设备升级流程是什么？",
        "如何查看CPU使用率？",
        "内存使用率监控方法",
        "日志文件在哪里查看？",
        "如何重启设备？",
        "密码忘记了怎么办？",
        "网络不通如何排查？"
    ]
    
    print("=" * 60)
    print("开始测试监控修复效果")
    print("=" * 60)
    
    success_count = 0
    total_count = len(test_queries)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n[{i}/{total_count}] 测试查询 {i}")
        if test_faq_api(query):
            success_count += 1
        
        # 避免请求过快
        if i < total_count:
            time.sleep(2)
    
    print("\n" + "=" * 60)
    print("测试完成")
    print(f"成功: {success_count}/{total_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    print("=" * 60)
    
    print("\n提示：")
    print("1. 请检查 monitor_data 目录下的监控报告")
    print("2. 查看日志文件确认是否记录了真实的召回数据")
    print("3. 检查监控报告中的召回源贡献度是否真实")

def check_monitor_report():
    """
    检查监控报告文件
    """
    import os
    from pathlib import Path
    
    monitor_dir = Path("./monitor_data")
    
    if not monitor_dir.exists():
        print("监控目录不存在")
        return
    
    # 查找今天的监控报告
    today = datetime.now().strftime("%Y%m%d")
    report_file = monitor_dir / f"monitor_report_{today}.md"
    
    if report_file.exists():
        print(f"\n找到监控报告: {report_file}")
        
        # 读取并显示部分内容
        with open(report_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        print("\n监控报告内容预览:")
        print("-" * 40)
        for line in lines[:30]:  # 显示前30行
            print(line.rstrip())
        
        # 检查是否有真实数据
        content = ''.join(lines)
        if "ES结果" in content or "未记录到召回数据" in content:
            print("\n⚠️ 警告：监控报告可能包含模拟数据！")
        else:
            print("\n✓ 监控报告看起来包含真实数据")
    else:
        print(f"未找到今天的监控报告: {report_file}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--check":
        # 只检查监控报告
        check_monitor_report()
    else:
        # 运行测试
        run_multiple_tests()
        
        # 等待一下让监控数据保存
        print("\n等待监控数据保存...")
        time.sleep(3)
        
        # 检查监控报告
        check_monitor_report()
        
    print("\n测试脚本执行完成！")