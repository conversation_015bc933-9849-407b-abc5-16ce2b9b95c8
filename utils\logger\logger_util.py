import logging
import os

from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from colorlog import ColoredFormatter
from config import project_root
from domain.config.zxtech_config import config
import traceback
from datetime import datetime
from kafka import KafkaProducer
current_date = datetime.now()
class KafkaLoggingHandler(logging.Handler):
    def __init__(self, brokers, topic):
        logging.Handler.__init__(self)
        self.producer = KafkaProducer(
            bootstrap_servers=brokers
        )
        self.topic = topic

    def emit(self, record):
        try:
            formatter_message = self.formatter.format(record)
            res = self.producer.send(self.topic, value=formatter_message.encode('utf-8'))
            self.producer.flush()
        except (KeyboardInterrupt, SystemExit):
            raise
        except Exception as e:
            self.handleError(record)

BROKER_SERVERS=config['kafka']['broker']
TOPIC=config['kafka']['topic']
class CustomLogger(logging.Logger):
    def error(self, msg, *args, **kwargs):
        # 获取堆栈信息
        stack_info = ''.join(traceback.format_exc())
        # 修改消息以包含堆栈信息
        modified_msg = f"{msg}\nCall stack:\n{stack_info}"
        # 调用父类的error方法来记录日志，使用修改后的消息
        super().error(modified_msg, *args, **kwargs)
class MyLogAdapter(logging.LoggerAdapter):
    def error(self, msg, *args, **kwargs):
        # 获取堆栈信息
        stack_info = ''.join(traceback.format_exc())
        # 修改消息以包含堆栈信息
        modified_msg = f"{msg}\nCall stack:\n{stack_info}".replace('\n','\\n')
        # 调用父类的error方法来记录日志，使用修改后的消息
        super().error(modified_msg, *args, **kwargs)
    def info(self, msg, *args, **kwargs):
        if 'extra' in kwargs:
            for key, value in kwargs['extra'].items():
                self.extra[key] = value
        # 调用父类的info方法来记录日志，使用修改后的消息和extra参数
        super().info(msg, *args, **kwargs)


config_level = config['log']['level']

logger = CustomLogger(name='custom_logger')
# 设置控制台打印日志级别
logger.setLevel(logging._nameToLevel[config_level])

# 创建一个 ColoredFormatter 实例，设置不同日志级别对应的颜色
formatter = ColoredFormatter(
    "%(log_color)s%(asctime)s %(levelname)s: %(message)s",
    datefmt='%Y-%m-%d %H:%M:%S',
    reset=True,
    log_colors={
        'DEBUG': 'black',
        'INFO': 'green',
        'WARNING': 'yellow',
        'ERROR': 'red',
        'CRITICAL': 'white,bg_red',
    },
    secondary_log_colors={},
    style='%'
)


# 创建一个 StreamHandler，并设置 ColoredFormatter
colorHandler = logging.StreamHandler()
colorHandler.setFormatter(formatter)
logger.addHandler(colorHandler)
date_prefix = current_date.strftime("%Y-%m-%d")
log_dir = project_root.joinpath(config['log']['dir'])
rewrite_log_dir = log_dir.joinpath('rewrite_log')
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
log_path = project_root.joinpath(config['log']['dir'], f"log_{date_prefix}.log")
rewrite_log_path = rewrite_log_dir.joinpath(f"rewrite_log_{date_prefix}.log")
log_file_handler = TimedRotatingFileHandler(
    filename=log_path,
    # 每天创建一个新的日志文件
    when='midnight',
    # 每天滚动
    interval=1,
    # 保留最近的三个备份文件
    backupCount=5,
    encoding='utf-8'
)

rewrite_log_file_handler = TimedRotatingFileHandler(
    filename=rewrite_log_path,
    # 每天创建一个新的日志文件
    when='midnight',
    # 每天滚动
    interval=1,
    backupCount=30,
    encoding='utf-8'
)

# 设置日志文件处理器级别为 INFO
log_file_handler.setLevel(logging.INFO)

# 创建格式化器
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
extra_value = {'syslogtag': config['MicroserviceName'][1:], 'ztedpgssouser': '-', 'portalssouser': '-',
               'http_referer': config['BaseUrl'],
               'remote_addr': '-', 'http_x_forwarded_for': '-', 'http_user_agent': '-', 'http_host': '-', 'uri': '-',
               'params': '-', 'server_addr': '-', 'request_method': '-', 'time_local': '%(asctime)s',
               'time_zone': '+0800',
               'status': 'success', 'request_time': '-',
               'request_length': '-', 'body_bytes_sent': '-', 'ssl_protocol': '-', 'server_protocol': '-',
               'ssl_cipher': '-','app_name':'-',
               'upstream_addr': '-',
               'upstream_status': '-', 'upstream_response_time': '-', 'request_body': '-', 'source_syscode': '-',
               'target_syscode': '100000467861',
               'business_type': 'query', 'client_time_local_2_service_code': '-\t' * 11,'msgId':'-','answer_field':'-',
               'extend_5_extend_32': '-\t' * 28}
formatter1 = logging.Formatter('%(syslogtag)s\t%(levelname)s\t%(ztedpgssouser)s\t%(portalssouser)s\t'
                               '%(http_referer)s\t%(remote_addr)s\t%(http_x_forwarded_for)s\t%(http_user_agent)s\t%(http_host)s\t'
                               '%(uri)s\t%(params)s\t%(server_addr)s\t%(request_method)s\t%(asctime)s\t%(time_zone)s\t%(status)s\t%(request_time)s\t'
                               '%(request_length)s\t%(body_bytes_sent)s\t%(ssl_protocol)s\t%(server_protocol)s\t%(ssl_cipher)s\t%(upstream_addr)s\t%(upstream_status)s\t'
                               '%(upstream_response_time)s\t%(request_body)s\t%(source_syscode)s\t%(target_syscode)s\t%(business_type)s\t'
                               '%(client_time_local_2_service_code)s%(message)s\t%(msgId)s\t%(answer_field)s\t%(app_name)s\t%(extend_5_extend_32)s',
                               datefmt='%Y-%m-%d %H:%M:%S')

# 将格式化器添加到处理器
try:
    kafka_handler = KafkaLoggingHandler(BROKER_SERVERS, TOPIC)
    kafka_handler.setLevel(logging.INFO)
    kafka_handler.setFormatter(formatter1)
    logger.addHandler(kafka_handler)
except Exception as e:
    logger.info(e)
log_file_handler.setFormatter(formatter1)
rewrite_log_file_handler.setFormatter(formatter)
rewrite_logger = logging.getLogger('rewrite_log')
rewrite_logger.addHandler(rewrite_log_file_handler)
# 将处理器添加到记录器
logger.addHandler(log_file_handler)
logger = MyLogAdapter(logger, extra_value)
logger.info(f'{project_root.joinpath(config["log"]["path"])}')