@startuml ZTE产品检索业务流程架构图






title ZTE产品检索问答系统 - 业务流程架构图55556666

start

:用户发起请求\nPOST /zte-ibo-acm-productretrieve/faq;
note right
  请求参数:
  - text: 用户问题
  - history: 对话历史
  - rewriteText: 重写文本
  - X-Emp-No: 员工号
end note

:Flask接收请求;

:安全认证检查;
note right
  - UAC Token验证
  - 员工号验证
  - 路径权限检查
end note

if (认证通过?) then (否)
  :返回认证失败;
  stop
else (是)
endif

:参数验证与预处理;
if (参数有效?) then (否)
  :返回参数错误;
  stop
else (是)
endif

:进入核心业务层\n_ZXTECH_服务;

:文本预处理;
note right
  - 清理特殊字符
  - 格式标准化
  - 下划线替换空格
end note

if (存在对话历史?) then (是)
  :多轮对话处理;
  :调用意图识别模型;
  :文本重写优化;
else (否)
  :单轮对话处理;
endif

:语言类型检测;
note right
  判断中文/英文
  影响后续处理策略
end note

:BGE-M3向量化;
note right
  生成1024维向量表示
  用于语义相似度计算
end note

:实体提取(dp_entity);
note right
  从ES索引kg_product_dn20240327
  提取产品型号和系列信息
end note

if (提取到产品实体?) then (否)
  :无实体路径;
  :全局ES检索;
  :全局Milvus检索;
  :全局KG检索;
else (是)
  :有实体路径;
  
  :版本号检测;
  note right
    正则匹配: [vV]\d+(?:\.\d+)*
    过滤预定义版本列表
  end note
  
  if (检测到版本号?) then (是)
    :版本特定检索;
    :根据产品ID+版本号查询文档;
    if (找到版本文档?) then (否)
      :返回版本错误提示;
      stop
    else (是)
    endif
  else (否)
    :产品全文档检索;
    :根据产品ID查询关联文档;
    if (找到产品文档?) then (否)
      :根据系列ID查询文档;
      if (找到系列文档?) then (否)
        :降级到全局检索;
      else (是)
      endif
    else (是)
    endif
  endif
  
  :文档范围内ES检索;
  :文档范围内Milvus检索;
  :文档范围内KG检索;
endif

:三路召回结果融合;
note right
  合并来自三个检索源的结果:
  - ES: 文本匹配结果
  - Milvus: 向量相似度结果  
  - KG: 知识图谱关系结果
end note

:BGE-M3重排序;
note right
  - 计算查询与候选的相关性得分
  - 按相关性重新排序
  - 内容去重处理
end note

if (版本检索模式?) then (是)
  :LLM答案质量判断;
  if (答案质量满足?) then (否)
    :回退到全集文档;
    :重新检索和重排序;
  else (是)
  endif
else (否)
endif

:构建LLM上下文;
note right
  组装Prompt:
  - 用户问题
  - 检索到的相关文档
  - 产品实体信息
  - 语言类型标识
end note

:调用NebulaBiz大语言模型;
note right
  模型配置:
  - temperature: 0
  - top_p: 0.85
  - top_k: 5
  - 流式输出模式
end note

:流式响应输出(SSE);
note right
  Server-Sent Events
  实时返回生成内容
end note

:记录日志和监控;

:返回最终结果;

stop

@enduml
