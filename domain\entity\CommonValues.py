import threading

request_value = threading.local()

class CommonValues:

    @staticmethod
    def set_value(key, value):
        values_map = getattr(request_value, 'values', {})
        values_map[key] = value
        request_value.values = values_map

    @staticmethod
    def get_value(key):
        values_map = getattr(request_value, 'values', {})
        return values_map.get(key, None)

    @staticmethod
    def remove():
        if hasattr(request_value, 'values'):
            del request_value.values
