from domain.constants.Enums import Targets
from .prompt_instance.digiman_en_prompt import digiman_en_prompt
from .prompt_instance.digiman_prompt import digiman_prompt
from .prompt_instance.no_refer_prompt import no_refer_prompt
# from .prompt_instance.zxbk_multi_prompt import zxbk_multi_prompt
from .prompt_instance.zxbk_prompt import zxbk_prompt
from .prompt_instance.zxbk_en_prompt import zxbk_en_prompt
from .prompt_instance.zxbk18_prompt import zxbk18_prompt
from .prompt_instance.coo_prompt import coo_prompt
from .prompt_instance.faq_prompt import faq_prompt
from .prompt_instance.similar_prompt import similar_prompt
from .prompt_instance.eccn_prompt import eccn_prompt
from .prompt_instance.rewrite_prompt import rewrite_prompt
from .prompt_instance.zxbk_multi_prompt import zxbk_multi_prompt
from .prompt_instance.relate_prompt import relate_prompt
from .prompt_instance.zxtech_prompt import zxtech_prompt
from .prompt_instance.zxtech_with_exclusive_prompt import zxtech_with_exclusive_prompt
from .prompt_instance.zxtech_with_exclusive_self_prompt import zxtech_with_exclusive_self_prompt
class PromptLoader():
    def __init__(self, user_content, question, target, history):
        self.user_content = user_content
        self.question = question
        self.target = target
        self.history = history

    def get_function(self):
        prompt_registry = {
            Targets.FAQ: faq_prompt(self.user_content, self.question),
            Targets.NOREFER: no_refer_prompt(self.user_content, self.question),
            Targets.ZXBK: zxbk_prompt(self.user_content, self.question),
            Targets.ZXBK_EN: zxbk_en_prompt(self.user_content, self.question),
            Targets.ZXBK18: zxbk18_prompt(self.user_content, self.question),
            Targets.COO: coo_prompt(self.user_content, self.question),
            Targets.ECCN: eccn_prompt(self.user_content, self.question),
            Targets.SIM: similar_prompt(self.user_content, self.question),
            Targets.REWRITE: rewrite_prompt(self.history, self.question),
            Targets.RELATE: relate_prompt(self.history, self.question),
            Targets.MULTI: zxbk_multi_prompt(self.history, self.user_content, self.question),
            Targets.DIGIMAN: digiman_prompt(self.user_content, self.question),
            Targets.DIGIMAN_EN: digiman_en_prompt(self.user_content, self.question),
            Targets.ZXTECH:zxtech_prompt(self.user_content,self.question),
            Targets.ZXTECH_with_Exclusive:zxtech_with_exclusive_prompt(self.user_content,self.question),
            Targets.ZXTECH_with_Exclusive_Self: zxtech_with_exclusive_self_prompt(self.user_content, self.question)

        }
        if self.target in prompt_registry:
            prompt = prompt_registry[self.target]
        else:
            prompt = prompt_registry[Targets.ZXTECH]
        return prompt

    def get_prompt(self):
        return self.get_function()
