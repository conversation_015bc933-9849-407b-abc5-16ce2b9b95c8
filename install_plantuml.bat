@echo off
echo Installing PlantUML...

REM 创建工具目录
if not exist "tools" mkdir tools
cd tools

REM 下载 PlantUML
echo Downloading PlantUML.jar...
powershell -Command "Invoke-WebRequest -Uri 'https://github.com/plantuml/plantuml/releases/download/v1.2024.7/plantuml-1.2024.7.jar' -OutFile 'plantuml.jar'"

REM 测试 PlantUML
echo Testing PlantUML installation...
java -jar plantuml.jar -version

REM 测试中文支持
echo Testing Chinese support...
echo @startuml > test.puml
echo title 中文测试 >> test.puml
echo Alice -^> Bob: 你好 >> test.puml
echo @enduml >> test.puml

java -Dfile.encoding=UTF-8 -jar plantuml.jar -charset UTF-8 test.puml

echo.
echo Installation complete!
echo PlantUML location: %cd%\plantuml.jar
echo.
echo Please configure your IDE plugin with:
echo   PlantUML jar: %cd%\plantuml.jar
echo   JVM args: -Dfile.encoding=UTF-8
pause