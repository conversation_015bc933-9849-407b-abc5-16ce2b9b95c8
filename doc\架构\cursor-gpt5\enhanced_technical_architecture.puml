@startuml ZTE 产品检索系统增强版技术架构图
!theme vibrant

title ZTE 产品检索系统 - 增强版技术架构图 (4A + 中间件/数据/外部依赖)

skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 12
skinparam componentStyle rectangle
skinparam packageStyle rectangle

skinparam component {
  BorderThickness 2
  RoundCorner 8
}

package "Authentication 认证" as Auth #FFE6E6 {
  component "UAC 集成" as UACAuth {
    [PyJWT]
    [requests]
    [utils/tokenVerify.py]
  }
  component "认证中间件" as AuthMW {
    [Token 校验]
    [Session 验证]
    [异常处理]
  }
  component "会话存储(可选)" as SessionStore {
    [Redis]
  }
}

package "Authorization 授权" as Authz #E6F3FF {
  component "RBAC/策略" as RBAC {
    [角色/资源]
    [策略矩阵]
    [端点授权]
  }
  component "权限拦截器" as AuthzMW {
    [装饰器]
    [资源校验]
  }
}

package "Accounting 审计/可观测" as Audit #E6FFE6 {
  component "日志系统" as Logging {
    [utils/logger]
    [resource/full_link_log_setup.py]
    [结构化日志]
  }
  component "监控指标" as Metrics {
    [召回监控 monitor/recall_monitor.py]
    [性能统计]
  }
  component "链路追踪(可选)" as Tracing {
    [Jaeger/OpenTelemetry]
  }
}

package "Administration 管理" as Admin #FFF0E6 {
  component "配置中心" as Config {
    [apollo/*]
    [infrastructure/config/config_center.py]
    [domain/config/zxtech_config.py]
  }
  component "部署与运维" as Ops {
    [Docker]
    [Kubernetes]
    [Nginx]
  }
  component "特性开关/AB(可选)" as FeatureFlags {
    [环境变量]
    [配置开关]
  }
}

package "中间件与框架" as Middleware #F0F0F0 {
  component "Web框架" as Web {
    [Flask]
    [Flask-CORS]
  }
  component "韧性与治理" as Resilience {
    [限流/重试]
    [熔断/降级]
  }
  component "消息/任务(可选)" as MQ {
    [Celery]
    [RabbitMQ/Kafka]
  }
}

package "数据与AI" as DataAI #E8E8E8 {
  database "ElasticSearch" as ES
  database "Milvus 向量库" as Milvus
  database "Nebula 图数据库" as Nebula
  component "BGE-M3 嵌入" as Embed
  component "BGE-M3 Rerank" as Rerank
  component "NebulaBiz LLM" as LLM
}

package "业务实现(关键代码)" as Biz #FFF8DC {
  component "控制器" as Ctrl {
    [controller/zxtech_controller.py]
    [controller/monitor_controller.py]
  }
  component "核心服务编排" as Core {
    [service/zxtech_service.py]
  }
  component "查询重写" as Rewrite {
    [rewrite/rewrite_model.py]
    [prompt/PromptLoader.py]
  }
  component "多路召回" as Recall {
    [retrieve/milvus_recall.py]
    [retrieve/kg_retrieve/*]
    [utils/es_util]
    [rerank/rrf.py]
  }
  component "重排与质量" as RankQuality {
    [rerank/bge_m3_reranker_v2.py]
    [utils/recall_context.py]
  }
  component "答案生成" as Answer {
    [llm/LLMResult.py]
    [ui/stream_entity.py]
  }
}

' 连接关系
Web --> Ctrl : HTTP 路由
Ctrl --> Core : 业务调用
Core --> Rewrite : 查询重写/提示词
Core --> Recall : 策略召回
Recall --> ES : 文本检索
Recall --> Milvus : 向量检索
Recall --> Nebula : 图谱检索
Recall --> RankQuality : RRF融合
RankQuality --> Answer : TopN 合格文档
Answer --> LLM : 生成调用
Rewrite --> Embed : 嵌入
RankQuality --> Rerank : 重排序

Ctrl --> UACAuth : Token 验证
UACAuth --> AuthMW : 认证集成
AuthMW --> SessionStore : 会话(可选)

Ctrl --> RBAC : 资源授权
RBAC --> AuthzMW : 策略核查

Core --> Logging : 结构化日志
Core --> Metrics : 指标
Core --> Tracing : Trace(可选)

Core --> Config : 配置加载
Web --> Resilience : 入站治理
Core --> MQ : 异步任务(可选)

legend right
  |= 范畴 |= 关键技术/实现 |
  | 认证 | PyJWT, UAC, tokenVerify |
  | 授权 | RBAC/策略拦截 |
  | 审计 | 结构化日志, 指标, Trace |
  | 管理 | Apollo, Docker/K8s, 开关 |
  | 中间件 | Flask, CORS, 限流/熔断 |
  | 数据/AI | ES, Milvus, Nebula, BGE, LLM |
  | 业务实现 | controller/service/retrieve/rerank/llm |
endlegend

@enduml


