from utils.infoGo_util import sort_dict

def check_spell( body):
    for b in body:
        if str(b).startswith('ZX'):
            return True
    return False


def process_strings(bodylist):
    new_list = []

    for string in bodylist:
        # 以空格分割字符串
        parts = string.split()
        processed_parts = []

        for part in parts:
            # 如果分割后的字符串以V开头，则跳过
            if part.startswith('V') or part.startswith('v'):
                continue
            else:
                processed_parts.append(part)

        # 将处理后的部分合并成一个字符串
        processed_string = ' '.join(processed_parts)

        # 添加到新的列表中
        new_list.append(processed_string)

    return new_list
