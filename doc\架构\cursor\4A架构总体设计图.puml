@startuml ZTE产品检索系统4A架构总体设计图
!theme vibrant
skinparam backgroundColor #F8F9FA
skinparam componentStyle rectangle
skinparam packageStyle rectangle

title **ZTE产品检索问答系统 - 4A架构总体设计图**\n<i>ZTE Product Retrieval Q&A System - 4A Architecture Overview</i>\n

' 定义组件样式
skinparam component {
    BackgroundColor<<authentication>> #FFE6E6
    BackgroundColor<<authorization>> #E6F3FF
    BackgroundColor<<accounting>> #E6FFE6
    BackgroundColor<<administration>> #FFF0E6
    BackgroundColor<<business>> #F0F0F0
    BackgroundColor<<data>> #E8E8E8
    BorderThickness 2
    RoundCorner 10
}

' 用户层
actor "内部用户\n(ZTE员工)" as InternalUser #FFE4B5
actor "外部用户\n(合作伙伴)" as ExternalUser #FFE4B5
actor "系统管理员" as Admin #FF6B6B

' 4A架构核心层
package "**4A架构核心层**" as Core4A {
    
    ' Authentication - 认证
    package "**Authentication (认证)**" as AuthenticationLayer <<authentication>> {
        component "UAC认证中心" as UACAuth
        component "Token验证器" as TokenValidator
        component "身份识别器" as IdentityRecognizer
        component "多因子认证" as MFAAuth
        component "单点登录(SSO)" as SSO
    }
    
    ' Authorization - 授权
    package "**Authorization (授权)**" as AuthorizationLayer <<authorization>> {
        component "权限控制器" as PermissionController
        component "角色管理器" as RoleManager
        component "资源访问控制" as ResourceACL
        component "API权限网关" as APIGateway
        component "数据权限过滤" as DataFilter
    }
    
    ' Accounting - 审计/计费
    package "**Accounting (审计)**" as AccountingLayer <<accounting>> {
        component "访问日志记录" as AccessLogger
        component "操作审计" as OperationAudit
        component "性能监控" as PerformanceMonitor
        component "资源使用统计" as ResourceUsage
        component "合规审计" as ComplianceAudit
    }
    
    ' Administration - 管理
    package "**Administration (管理)**" as AdministrationLayer <<administration>> {
        component "系统配置管理" as SystemConfig
        component "用户生命周期" as UserLifecycle
        component "策略管理" as PolicyManager
        component "监控告警" as MonitoringAlert
        component "容量规划" as CapacityPlanning
    }
}

' 业务应用层
package "**业务应用层**" as BusinessLayer <<business>> {
    component "产品检索服务" as ProductRetrievalService
    component "知识问答服务" as QAService
    component "智能推荐服务" as RecommendationService
    component "文档管理服务" as DocumentService
}

' 数据存储层
package "**数据存储层**" as DataLayer <<data>> {
    database "用户身份数据库" as UserDB
    database "权限配置数据库" as PermissionDB
    database "审计日志数据库" as AuditDB
    database "业务数据存储" as BusinessDB {
        [ElasticSearch] as ES
        [Milvus向量库] as Milvus
        [知识图谱] as KG
    }
}

' 外部系统集成
cloud "**外部系统**" as ExternalSystems {
    component "ZTE统一认证平台" as ZTEAuth
    component "企业目录服务" as LDAP
    component "安全运营中心" as SOC
    component "配置管理中心" as CMC
}

' 连接关系 - 用户访问流程
InternalUser --> UACAuth : 1.身份认证
ExternalUser --> UACAuth : 1.身份认证
Admin --> UACAuth : 1.管理员认证

UACAuth --> TokenValidator : 2.令牌验证
TokenValidator --> PermissionController : 3.权限检查
PermissionController --> ProductRetrievalService : 4.服务访问

' Authentication内部连接
UACAuth --> ZTEAuth : 集成认证
UACAuth --> LDAP : 目录查询
IdentityRecognizer --> UserDB : 用户数据
MFAAuth --> TokenValidator : 多因子验证
SSO --> UACAuth : 单点登录

' Authorization内部连接
PermissionController --> RoleManager : 角色查询
RoleManager --> PermissionDB : 权限数据
ResourceACL --> APIGateway : 接口控制
DataFilter --> BusinessDB : 数据过滤

' Accounting内部连接
AccessLogger --> AuditDB : 访问记录
OperationAudit --> AuditDB : 操作记录
PerformanceMonitor --> MonitoringAlert : 性能告警
ResourceUsage --> CapacityPlanning : 容量分析
ComplianceAudit --> SOC : 合规报告

' Administration内部连接
SystemConfig --> CMC : 配置同步
UserLifecycle --> UserDB : 用户管理
PolicyManager --> PermissionDB : 策略配置
MonitoringAlert --> Admin : 告警通知

' 业务层连接
ProductRetrievalService --> ES : 文档检索
QAService --> Milvus : 向量检索
RecommendationService --> KG : 知识推理
DocumentService --> BusinessDB : 数据存储

' 横向监控连接
AccessLogger ..> UACAuth : 认证日志
AccessLogger ..> PermissionController : 授权日志
OperationAudit ..> ProductRetrievalService : 业务审计
PerformanceMonitor ..> BusinessLayer : 性能监控

' 图例
legend right
    |= 颜色 |= 4A组件 |= 说明 |
    |<#FFE6E6> | Authentication | 身份认证与验证 |
    |<#E6F3FF> | Authorization | 权限控制与授权 |
    |<#E6FFE6> | Accounting | 审计日志与计费 |
    |<#FFF0E6> | Administration | 系统管理与运维 |
    |<#F0F0F0> | Business Layer | 业务应用层 |
    |<#E8E8E8> | Data Layer | 数据存储层 |
    
    == 4A架构核心理念 ==
    **Authentication**: 确保用户身份真实可靠
    **Authorization**: 控制用户访问权限边界
    **Accounting**: 记录所有操作审计轨迹
    **Administration**: 提供统一管理运维能力
endlegend

' 添加关键说明
note top of Core4A
    **4A架构设计原则**:
    1. **统一认证**: 基于ZTE UAC的单点登录
    2. **精细授权**: 基于角色的权限控制(RBAC)
    3. **全程审计**: 端到端的操作轨迹记录
    4. **集中管理**: 统一的配置和策略管理
end note

note right of BusinessLayer
    **业务集成说明**:
    - 所有业务服务都需要通过4A验证
    - 支持细粒度的数据权限控制
    - 提供完整的操作审计能力
    - 支持业务级别的监控告警
end note

@enduml