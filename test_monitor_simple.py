"""
简单测试监控功能 - 不依赖HTTP服务
"""

from monitor.recall_monitor import get_monitor

def simple_test():
    """直接测试监控记录功能"""
    
    print("=" * 60)
    print("        简单监控测试")
    print("=" * 60)
    
    # 获取监控实例
    monitor = get_monitor()
    
    print(f"\n当前查询数: {monitor.query_counter}")
    
    # 模拟一次查询
    print("\n模拟记录一次查询...")
    
    # 模拟召回结果
    es_results = [
        {'content': 'ES结果1', 'doc_name': 'doc1', 'id': 'es_1'},
        {'content': 'ES结果2', 'doc_name': 'doc2', 'id': 'es_2'}
    ]
    
    milvus_results = []  # Milvus已禁用
    
    kg_results = ['KG知识图谱结果']
    
    rerank_results = [
        {'content': 'KG知识图谱结果', 'doc_name': 'kg', 'id': 'kg_1'},
        {'content': 'ES结果1', 'doc_name': 'doc1', 'id': 'es_1'}
    ]
    
    # 记录到监控
    query_id = monitor.record_query(
        query="测试查询：M6000设备尺寸",
        es_results=es_results,
        milvus_results=milvus_results,
        kg_results=kg_results,
        rerank_results=rerank_results,
        response_time=0.123
    )
    
    print(f"[OK] 记录成功，查询ID: {query_id}")
    print(f"当前总查询数: {monitor.query_counter}")
    
    # 显示排行榜
    print("\n当前排行榜：")
    print("-" * 40)
    for idx, item in enumerate(monitor.leaderboard[:3], 1):
        print(f"{idx}. {item['source']}: 贡献率{item['contribution_rate']:.1f}%, 得分{item['total_score']:.2f}")
    
    # 保存数据
    print("\n保存监控数据...")
    monitor._save_history()
    print("[OK] 数据已保存到 monitor_data/ 目录")
    
    # 列出生成的文件
    from pathlib import Path
    monitor_dir = Path("monitor_data")
    if monitor_dir.exists():
        files = list(monitor_dir.glob("*20250806*"))
        print(f"\n生成的监控文件（共{len(files)}个）：")
        for f in sorted(files):
            print(f"  - {f.name}")

if __name__ == "__main__":
    simple_test()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n说明：")
    print("1. 监控功能本身是正常的")
    print("2. FAQ接口可能没有正确集成监控")
    print("3. 需要重启服务使装饰器生效")