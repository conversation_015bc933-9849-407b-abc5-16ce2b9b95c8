# 产品技术问答系统开发设计（含4A安全架构）

本文档基于《doc/架构/演进方向/需求方案.md》，在不改变总体业务目标与DOD的前提下，给出面向实施的开发设计方案，覆盖4A安全架构、两套实现路线（方案一、方案二）的技术细节、组件交互与UML设计，供研发、测试、运维与安全合规团队落地实施。

---

## 1. 目标与范围

- 明确产品技术问答系统在“采集-处理-存储-检索-重排-生成”全链路的技术设计。
- 引入4A（Authentication、Authorization、Accounting、Auditing）统一安全与治理框架，满足权限控制、可计量、可追溯的企业级要求。
- 给出两套实现路径：
  - 方案一：复用 DN Studio 的知识与检索能力，最小改造、快速交付。
  - 方案二：自建 ES + 向量库 + 知识图谱，多引擎编排，灵活可扩展。

## 2. 术语与缩写

- 4A：认证（Authentication）、授权（Authorization）、核算/计费（Accounting）、审计（Auditing）。
- IdP：身份提供方（如企业统一认证、AD、CAS、OIDC）。
- PDP/PAP/PIP：策略决策点/策略管理点/属性提供方。
- RAG：Retrieval-Augmented Generation，检索增强生成。
- TSM：TSM 文档库（数据源）。
- Info-Go：Info-Go 系统（元数据/关系数据源）。
- DN Studio：企业AI应用平台的知识与检索能力集合。

---

## 3. 业务能力回顾（来源：需求方案）

- 数据源管理：自动同步，支持多模态文件（Word/PDF/Excel/PPT/图片等），兼容 BN/OTN/FM 语料。
- 用户体验：可达路径简化、联网拓展（通识补充）、对话记忆。
- 问答质量：≤5s 首字、答案准确性（≥90%达2~3分）、权限管控。
- 辅助功能：标准化角标与溯源跳转。

---

## 4. 整体架构（含4A）

系统整体在接入层、应用层、检索/生成层之上，外置统一4A安全域，贯穿请求生命周期。核心目标：

- 对“用户-资源-行为-环境”进行策略化授权与审计。
- 对接口与模型调用进行核算计量与成本归集。
- 以API Gateway为统一入口，确保令牌校验、细粒度授权与审计一致性。

### 4.1 4A组件视图（PlantUML）

```plantuml
@startuml
!theme plain
left to right direction

package "接入层" #AliceBlue {
  [API Gateway] as APIGW
}

package "应用域" #LightGreen {
  component "问答应用\n(QA App)" as QA
  component "检索服务\n(Retriever)" as RET
  component "生成服务\n(LLM Service)" as LLM
}

package "4A安全域" #Moccasin {
  component "认证服务\n(OIDC Client)" as AUTH
  component "授权服务\n(PDP/OPA)" as PDP
  component "策略管理\n(PAP)" as PAP
  component "属性服务\n(PIP/目录)" as PIP
  component "计费/核算\n(Metering)" as ACC
  component "审计\n(Audit)" as AUD
  database  "审计仓库" as AUDDB
  database  "计费仓库" as ACCDB
  component "企业IdP/SSO" as IDP
}

APIGW --> AUTH : Token校验/续期
AUTH --> IDP : OIDC/OAuth2登录
APIGW --> PDP : 授权查询(Allow/Deny)
PDP --> PIP : 拉取用户/资源/环境属性
PAP --> PDP : 策略下发/热更新
QA --> ACC : 用量上报(tokens/调用数)
QA --> AUD : 业务事件审计
AUD --> AUDDB
ACC --> ACCDB

APIGW --> QA
QA --> RET
RET --> LLM : 上下文+提示词

@enduml
```

### 4.2 查询流程时序（含4A）

```plantuml
@startuml
actor User
participant Web as "Web/前端"
participant AUTH as "认证服务"
participant IDP as "企业IdP"
participant APIGW as "API Gateway"
participant PDP as "授权服务(PDP)"
participant PIP as "属性服务(PIP)"
participant QA as "问答应用"
participant ACC as "计费"
participant AUD as "审计"

User -> Web : 访问/app
Web -> AUTH : OIDC授权码流程
AUTH -> IDP : 跳转登录
IDP --> AUTH : 授权码
AUTH --> Web : ID Token + Access Token
Web -> APIGW : /api/query(Access Token)
APIGW -> AUTH : 验证Token
APIGW -> PDP : isAllowed(user, action=Query, resource=Dataset)
PDP -> PIP : 取用户/资源/环境属性
PDP --> APIGW : Permit + 义务(字段脱敏/索引过滤)
APIGW -> QA : 转发请求(附用户上下文)
QA -> ACC : 上报用量
QA -> AUD : 记录审计(指纹/引用)
APIGW --> Web : 响应答案
@enduml
```

### 4.3 4A领域模型（类图）

```plantuml
@startuml
!theme plain

class User {
  +id: UUID
  +username: String
  +email: String
  +orgId: String
  +attributes: Map
}

class Role { +id: UUID +name: String +scope: String }
class Permission { +id: UUID +action: String +resourcePattern: String }
class Policy {
  +id: UUID
  +effect: Enum(Allow,Deny)
  +condition: Expr
  +version: Int
}
class Resource { +id: String +type: String +owner: String +labels: Map }
class Dataset { +id: String +name: String +product: String +visibility: Enum }
class Document { +id: String +title: String +source: String +version: String +checksum: String +acl: Map }
class Chunk { +id: String +docId: String +text: Text +embedding: Vector +offset: Int }
class AuditEvent { +id: UUID +userId: UUID +action: String +resource: String +ts: Instant +result: String +traceId: String }
class AccountingRecord { +id: UUID +userId: UUID +metric: String +value: Double +unit: String +ts: Instant +traceId: String }
class Session { +id: UUID +userId: UUID +accessToken: String +expiresAt: Instant }

User "1" -- "*" Role : has
Role "*" -- "*" Permission : grants
Policy "*" ..> Resource : appliesTo
Document "1" o-- "*" Chunk : contains
AuditEvent "*" --> "1" User : by
AccountingRecord "*" --> "1" User : for
Dataset "1" o-- "*" Document

@enduml
```

---

## 5. 方案一：基于 DN Studio 的实现

### 5.1 技术路线

- 数据源管理：TSM → DN Studio 知识库自动同步；Info-Go → 知识图谱同步。
- 检索：调用 DN Studio 的混合检索接口与图谱查询；在应用侧做多路召回融合与重排。
- 生成：RAG 召回切片 + BGE-M3/TF-IDF 融合重排 → 提示构建 → LLM 生成答案。
- 安全：4A 统一接入；网关做Token校验与OPA授权；应用上报计费与审计。

### 5.2 组件图（方案一 + 4A）

```plantuml
@startuml
!theme plain
left to right direction

package "数据源" #AliceBlue {
  file "TSM文档库" as TSM
  database "Info-Go系统" as INFO
}

package "DN Studio" #Wheat {
  component "知识库\n(结构化+向量)" as KB
  component "混合检索API" as KBAPI
  component "知识图谱服务" as KG
}

package "应用域" #LightGreen {
  component "问答应用" as QA
  component "重排/融合" as RANK
  component "提示构建" as PROMPT
  component "LLM服务" as LLM
}

package "4A安全域" #Moccasin {
  [API Gateway] as APIGW
  [认证] as AUTH
  [授权(OPA)] as PDP
  [计费] as ACC
  [审计] as AUD
}

TSM --> KB : 自动同步
INFO --> KG : 自动同步
QA --> KBAPI : 混合检索
QA --> KG : 图关系检索
KBAPI --> RANK
KG --> RANK
RANK --> PROMPT
PROMPT --> LLM

APIGW --> QA
APIGW --> AUTH
APIGW --> PDP
QA --> ACC
QA --> AUD

@enduml
```

### 5.3 关键实现要点

- 同步
  - 使用平台内置流水线（或定制Adapter）将 TSM 文档、元数据（id、版本、类型）同步至 DN Studio 知识库。
  - Info-Go 同步至知识图谱；保证版本、产品、型号等关系可检索。
- 召回与重排
  - 首选平台“混合检索API”，召回 N 条；并行图谱查询补充关系线索。
  - 重排模型：BGE-M3（多语言）、TF-IDF融合；可以引入Cross-Encoder重排以提高精度。
  - 切片完整性补偿：对重排TopK片段做上下文邻域补充。
- 生成
  - 模板化提示词（产品、版本、场景、答案格式、角标要求）。
  - 事实性验证器（Consistency Checker）比对问答一致性，过滤幻觉。
- 4A集成
  - OIDC对接企业IdP；API网关统一校验与续期。
  - OPA策略：按“产品线/版本/文档标签/切片来源”做 ABAC 授权，支持脱敏义务（字段掩码、引用过滤）。
  - 计费：按查询次数、LLM tokens、重排调用计量；维度：用户、部门、项目。
  - 审计：入参摘要、检索来源、引用指纹、生成摘要、策略决策、延迟指标。

### 5.4 典型时序（检索与生成）

```plantuml
@startuml
actor 用户
participant 前端
participant APIGW as 网关
participant QA as 应用
participant KBAPI as "DN Studio检索API"
participant KG as 图谱
participant R as 重排
participant L as LLM

用户 -> 前端 : 提问
前端 -> 网关 : /api/query
网关 -> QA : 鉴权后转发
QA -> KBAPI : 混合检索(q, topN)
QA -> KG : 关系检索(q)
KBAPI --> QA : 片段候选A
KG --> QA : 关系线索B
QA -> R : A∪B
R --> QA : TopK片段
QA -> L : 构造提示+TopK
L --> QA : 答案+引用
QA -> 网关 : 响应
@enduml
```

### 5.5 接口与数据模型（应用侧）

- 接口
  - GET /api/search?q=...&k=10
  - POST /api/query { query, context, k, policyContext }
  - GET /api/documents/{id}
  - GET /api/audit/logs?userId=...&range=...
  - GET /api/accounting/usage?userId=...&range=...

- 核心模型（应用内）
  - QueryRequest{ query:String, userContext, topK:Int, constraints }
  - RetrievedChunk{ docId, source, version, text, score, span }
  - Answer{ text, citations:[{docId, span, url}], metrics, traceId }
  - AuditEvent/AccountingRecord 同 4A 类图定义

### 5.6 性能与SLO

- 首字时间 ≤ 5s（p95）；端到端平均 ≤ 8s。
- 召回并行度与超时：检索API、图谱查询并行，单路超时 1.5s，整体预算 2.5s。
- 缓存：问题相似度缓存（Redis）、向量召回缓存、用户画像预取。

### 5.7 风险与缓解

- 平台能力受限 → 通过应用侧重排/事实验证提升质量。
- 部署合规 → 4A域侧重审计与策略约束，确保跨域访问可追溯。
- 兼容性 → 通过Adapter保证TSM元数据完整注入知识库。

---

## 6. 方案二：自建多引擎检索实现

### 6.1 技术路线

- 数据源：TSM→DN Studio（或直采）→ 自建 ES、向量库（Milvus/pgvector）、知识图谱（Neo4j/JanusGraph）。
- 检索：关键词（ES）、向量检索（向量库）、图检索（KG）多路召回；应用侧融合与重排。
- 生成：与方案一一致；更灵活地选择Embedding/重排模型与参数。
- 安全：同一4A域；更多资源级策略（索引、集合、图标签）。

### 6.2 组件图（方案二 + 4A）

```plantuml
@startuml
!theme plain
left to right direction

package "采集/处理" #AliceBlue {
  component "TSM Adapter" as TSMAD
  component "DN Studio导入器" as DNIMP
  component "切分/清洗/抽取" as ETL
}

package "存储" #Wheat {
  database "Elasticsearch" as ES
  database "向量库(Milvus/pgvector)" as VDB
  database "知识图谱(Neo4j)" as KG
}

package "应用域" #LightGreen {
  component "多路召回编排" as ORCH
  component "重排/融合" as RANK
  component "提示构建" as PROMPT2
  component "LLM服务" as LLM2
}

package "4A安全域" #Moccasin {
  [API Gateway] as APIGW
  [认证] as AUTH
  [授权(OPA)] as PDP
  [计费] as ACC
  [审计] as AUD
}

TSMAD --> ETL
DNIMP --> ETL
ETL --> ES
ETL --> VDB
ETL --> KG

APIGW --> ORCH
ORCH --> ES : 关键字检索
ORCH --> VDB : 向量检索
ORCH --> KG : 图关系检索
ORCH --> RANK : 候选融合
RANK --> PROMPT2
PROMPT2 --> LLM2

APIGW --> AUTH
APIGW --> PDP
ORCH --> ACC
ORCH --> AUD
@enduml
```

### 6.3 关键实现要点

- 采集与ETL
  - 文档标准化：去页眉页脚、图片OCR、表格结构化；引入版本/产品/地域标签。
  - 切分策略：按标题/语义/规则混合切分；保留段落层级与页码，用于角标与跳转。
  - 向量化：BGE-M3/中文领域模型；持续评估Embedding质量。
- 检索
  - ORCH流水线并发调用 ES/VDB/KG；召回合并去重；打分标准化。
  - 重排：Cross-Encoder（如 bge-reranker-large）+ 规则因子（来源可信度、时间、版本匹配）。
  - 结果补全：邻域窗口、文档摘要拼接。
- 4A与隔离
  - ES/VDB/KG 层按索引/集合/标签进行资源分级，与OPA策略一致。
  - 只返回用户有权访问的片段，引用列表同样按策略过滤。

### 6.4 时序（多路召回与融合）

```plantuml
@startuml
participant ORCH as 编排
participant ES
participant VDB
participant KG
participant R as 重排

编排 -> ES : keyword(q)
编排 -> VDB : vector(q)
编排 -> KG : graph(q)
ES --> 编排 : A
VDB --> 编排 : B
KG --> 编排 : C
编排 -> 重排 : merge(A,B,C)
重排 --> 编排 : TopK
@enduml
```

### 6.5 接口与数据模型（应用侧）

- 接口（拟定，与方案一对齐）
  - GET /api/search?q=...&k=10&type=keyword|vector|graph|mixed
  - POST /api/query { query, constraints:{product,version}, topK, rerank }
  - POST /api/ingest { source, files, metadata }
  - GET /api/audit/logs, GET /api/accounting/usage

- 核心模型
  - 同方案一，ORCH扩展检索类型与打分来源字段。

### 6.6 性能与SLO

- 索引与向量库分片策略，目标召回p95 ≤ 1.5s；融合与重排 ≤ 1.0s；生成预算 2.5~5s。
- 熔断与降级：任一路径超时即降级为可用路径；维持首字时间SLO。

### 6.7 风险与缓解

- 维护开销：三套存储引擎 → 基于Helm/K8s声明式运维与自动化指标告警。
- 一致性：ES/VDB/KG 三者间元数据一致 → 以文档ID/版本为主键，构建入湖审计与对账任务。
- 成本：存储/算力 → 分层存储（热/温/冷）与索引生命周期管理（ILM）。

---

## 7. 两方案对比

| 维度 | 方案一（复用DN Studio） | 方案二（自建多引擎） |
| --- | --- | --- |
| 交付速度 | 快，集成为主 | 中，需自建能力 |
| 灵活性 | 中，受平台接口限制 | 高，可自由组合与调优 |
| 检索上限 | 中高，平台演进可带动 | 高，短期可达更优定制 |
| 4A集成难度 | 低-中，主要在应用与网关 | 中，高度资源级控制 |
| 运维成本 | 低，平台统一维护 | 高，需要统一观测与自动化运维 |
| 风险 | 平台耦合度 | 系统复杂度/一致性 |

---

## 8. 数据治理与质量

- 数据分层：热/温/冷分层；频繁访问的FAQ与高价值文档进入热层。
- 评估框架：自动化测试集（覆盖问题类型/难度）、相关性与精确度评测、用户反馈闭环。
- 事实性验证：第二模型比对问答一致性，过滤幻觉。
- 角标与溯源：统一引用格式，点击跳转至原文位置；引用仅展示有权访问的片段。

---

## 9. 安全、合规与隐私

- 认证/授权：OIDC对接企业IdP，OPA做ABAC；支持项目级、产品级、版本级、国家区分等策略。
- 审计：记录用户、请求、策略决策、数据来源、引用指纹与延迟指标；对接企业SIEM。
- 核算：按用户/部门/项目聚合LLM Token与调用次数，用于成本归集与预算。
- 隐私：最小可见、最小必要；脱敏义务由网关或应用执行。

---

## 10. 部署与运维

- 部署：Kubernetes/容器化；环境分层（dev/test/prod）。
- 配置：集中化配置中心；策略热更新（PAP→PDP）。
- 观测：日志、指标、分布式追踪；关键SLO告警；查询质量看板。
- 灰度：逐步放量与回滚策略；离线A/B验证重排与Embedding升级。

---

## 11. 实施路线图

1. MVP（T0-T2周）
   - 方案一优先：接入DN Studio混合检索；完成4A基础接入；实现问答/溯源/审计。
2. 增强（T3-T6周）
   - 引入Cross-Encoder重排、事实性验证、缓存优化；完善计费归集与看板。
3. 可选并行：方案二骨架搭建（T3-T8周）
   - 自建ES/VDB/KG最小通路；小范围灰度与指标对比。

---

## 12. 验收标准（映射DOD）

- 首字时间 ≤ 5s（p95），≥90%答案达2~3分。
- 权限管控：资源级可见性与脱敏义务生效；未授权内容不可溯源。
- 多模态解析：覆盖Word/PDF/Excel/PPT/图片，角标与定位可点击跳转。
- 审计与核算：全链路可追溯；成本归集可按人/部门/项目导出。

---

## 附录：策略示例（OPA Rego 片段）

```rego
package productqa.authz

default allow = false

allow {
  input.user.roles[_] == "product_reader"
  input.resource.type == "document_chunk"
  input.resource.labels.product in input.user.attributes.products
  not is_restricted_version
}

is_restricted_version {
  input.resource.labels.version in {"internal", "draft"}
  not (input.user.roles[_] == "product_owner")
}
```


