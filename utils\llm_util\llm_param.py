from domain.constants.Enums import Targets
from pydantic import BaseModel
from typing import List, Dict, Any, Tuple

class _LLM_Params_(BaseModel):
    map_dict: Any = None
    body: Any = None
    doc_res: Any = None
    es_entity: Any = None
    es_res_doc: Any = None
    milvus_res_doc: Any = None
    kg_res: Any = None
    rerank_res_doc: Any = None
    query_lang: Any = None
    target: Any = None
    history: Any = None

class _Stream_Generate_Params_(BaseModel):
    kg_dict: Any = None
    chunk_id_map: Any = None
    receive: Any = None
    body: Any = None
    doc_res: Any = None
    es_entity: Any = None
    es_res_doc: Any = None
    milvus_res_doc: Any = None
    kg_res: Any = None
    rerank_res_doc: Any = None
    candidates2: Any = None
    urls_str: Any = None
    env: str = 'dev'

class _Product_Version_Params_(BaseModel):
    body: Any = None
    embed_query: Any = None
    pid: Any = None
    sid: Any = None
    query_lang: Any = None
    version_list: Any = None
