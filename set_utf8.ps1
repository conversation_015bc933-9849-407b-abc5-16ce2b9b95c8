# PowerShell UTF-8 编码设置脚本
# 设置控制台输出编码为 UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 设置 PowerShell 输出编码为 UTF-8
$OutputEncoding = [System.Text.Encoding]::UTF8

# 设置文件默认编码为 UTF-8
$PSDefaultParameterValues['Out-File:Encoding'] = 'utf8'
$PSDefaultParameterValues['*:Encoding'] = 'utf8'

# 设置代码页为 UTF-8 (65001)
chcp 65001 | Out-Null

Write-Host "PowerShell 编码已设置为 UTF-8" -ForegroundColor Green
Write-Host "当前编码页: $(Get-Content env:LC_ALL -ErrorAction SilentlyContinue)" -ForegroundColor Yellow
Write-Host "控制台输出编码: $([Console]::OutputEncoding.EncodingName)" -ForegroundColor Yellow