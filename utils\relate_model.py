import time

import requests
from utils.http_util import post_no_proxy

from domain.constants.Enums import RelatedResult, Rewrite
from utils.logger.log_wrapper import wrap_log_content, get_extra
from utils.logger.logger_util import logger
import copy
from domain.config.zxtech_config import config
url = copy.deepcopy(config['Relate']['simwords_url'])


# Started by AICoder, pid:r166bcd2d6g0b7d14db609cf50bac6195e669f8d
def convert_format(text, data):
    # 初始化结果字符串
    result = ""

    # 遍历列表中的每个字典
    for item in data:
        # 检查字典中是否存在'role'和'content'键，并且'role'为'user'
        if 'role' in item and 'content' in item and item['role'] == 'user':
            # 添加用户的内容，并在前面加上'---'
            result += str(item['content']).replace('\n', ' ').replace(' ', '') + "---"

    # 添加原始文本内容
    result += str(text).replace('\n', ' ').replace(' ', '')

    # 返回结果字符串
    return result

# Ended by AICoder, pid:r166bcd2d6g0b7d14db609cf50bac6195e669f8d
# Started by AICoder, pid:ba4bb90944o0a0b14f590be7709e160c2e1984dc
def extract_content(history):
    result = []
    for entry in history:
        # 如果角色是assistant并且内容中包含"**参考文档**"，则分割内容并去除多余空格
        if entry["role"] == "assistant" and Rewrite.FILTER.value in entry["content"]:
            parts = entry["content"].split(Rewrite.FILTER.value)
            entry["content"] = parts[0].strip()
        result.append(entry)
    return result

# Ended by AICoder, pid:ba4bb90944o0a0b14f590be7709e160c2e1984dc

def is_related(text, history, time_record_dict=None, g=None):
    # 发送POST请求
    if g is None:
        g = {}
    if not history:
        return False
    history = extract_content(history)
    data = convert_format(text, history)
    data = {
        "query": data
    }
    response = ""
    try:
        before = time.time()
        response = post_no_proxy(url, headers={'Content-Type': 'application/json'}, json=data)
        after = time.time()
        tc = after - before
        # logger.info(f"相关性判断耗时:{tc} s")
        if time_record_dict is not None:
            time_record_dict['relate'] = tc
        response = response.json()['bo']
        logger.info(wrap_log_content(f'[相关性判断结果]{response}',g), extra=get_extra({'log_type': 'relate'}))
    except Exception as e:
        logger.error(wrap_log_content(f"相关性判断报错:{e},{data}",g), extra=get_extra({'log_type': 'relate'}))
    # 打印返回的内容
    if response == RelatedResult.MULTI.value:
        return True
    return False
