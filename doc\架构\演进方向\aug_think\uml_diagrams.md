# UML架构图集

## 1. 4A架构类图

```mermaid
classDiagram
    class AuthenticationService {
        +String authenticateUser(credentials)
        +Boolean validateToken(token)
        +String refreshToken(token)
        +void logout(token)
    }
    
    class AuthorizationService {
        +Boolean checkPermission(user, resource, action)
        +List~Permission~ getUserPermissions(user)
        +Boolean hasRole(user, role)
        +void grantPermission(user, permission)
    }
    
    class AccountingService {
        +void recordUsage(user, service, metrics)
        +UsageReport generateReport(user, period)
        +void updateQuota(user, quota)
        +Boolean checkQuota(user, service)
    }
    
    class AuditingService {
        +void logAccess(user, resource, action, result)
        +void logSystemEvent(event, details)
        +List~AuditLog~ queryLogs(criteria)
        +void generateAuditReport(period)
    }
    
    class User {
        +String userId
        +String username
        +String department
        +String level
        +List~Role~ roles
    }
    
    class Role {
        +String roleId
        +String roleName
        +String description
        +List~Permission~ permissions
    }
    
    class Permission {
        +String permissionId
        +String action
        +String resource
        +String scope
    }
    
    class Resource {
        +String resourceId
        +String resourceType
        +String classification
        +String securityLevel
    }
    
    User ||--o{ Role : has
    Role ||--o{ Permission : contains
    Permission ||--o{ Resource : controls
    
    AuthenticationService --> User : validates
    AuthorizationService --> User : authorizes
    AuthorizationService --> Permission : checks
    AccountingService --> User : tracks
    AuditingService --> User : logs
```

## 2. 系统整体架构组件图

```mermaid
graph TB
    subgraph "用户接入层"
        WEB[Web界面]
        API[API接口]
        MOBILE[移动端]
    end
    
    subgraph "网关层"
        GATEWAY[API网关]
        LB[负载均衡]
    end
    
    subgraph "4A安全层"
        AUTH[认证服务]
        AUTHZ[授权服务]
        ACC[计费服务]
        AUDIT[审计服务]
    end
    
    subgraph "业务服务层"
        QA[问答服务]
        SEARCH[检索服务]
        RERANK[重排服务]
        GENERATE[生成服务]
    end
    
    subgraph "数据服务层"
        KB[知识库服务]
        KG[知识图谱服务]
        VECTOR[向量服务]
        CACHE[缓存服务]
    end
    
    subgraph "数据存储层"
        DNSTUDIO[(DN Studio)]
        ES[(Elasticsearch)]
        VECTORDB[(向量数据库)]
        GRAPH[(图数据库)]
        REDIS[(Redis缓存)]
    end
    
    subgraph "基础设施层"
        MONITOR[监控服务]
        LOG[日志服务]
        CONFIG[配置服务]
        MQ[消息队列]
    end
    
    WEB --> GATEWAY
    API --> GATEWAY
    MOBILE --> GATEWAY
    
    GATEWAY --> LB
    LB --> AUTH
    
    AUTH --> AUTHZ
    AUTHZ --> QA
    
    QA --> ACC
    QA --> AUDIT
    QA --> SEARCH
    
    SEARCH --> RERANK
    RERANK --> GENERATE
    
    SEARCH --> KB
    SEARCH --> KG
    SEARCH --> VECTOR
    SEARCH --> CACHE
    
    KB --> DNSTUDIO
    KG --> GRAPH
    VECTOR --> VECTORDB
    CACHE --> REDIS
    
    QA --> MONITOR
    QA --> LOG
    QA --> CONFIG
    QA --> MQ
```

## 3. 数据流序列图

```mermaid
sequenceDiagram
    participant U as 用户
    participant G as API网关
    participant A as 认证服务
    participant Z as 授权服务
    participant Q as 问答服务
    participant S as 检索服务
    participant R as 重排服务
    participant Gen as 生成服务
    participant KB as 知识库
    participant KG as 知识图谱
    participant Acc as 计费服务
    participant Aud as 审计服务
    
    U->>G: 1. 提交问题
    G->>A: 2. 验证身份
    A->>G: 3. 返回用户信息
    G->>Z: 4. 检查权限
    Z->>G: 5. 权限确认
    G->>Q: 6. 转发问题
    
    Q->>S: 7. 检索请求
    S->>KB: 8. 知识库检索
    S->>KG: 9. 图谱检索
    KB->>S: 10. 返回候选结果
    KG->>S: 11. 返回关系结果
    
    S->>R: 12. 重排请求
    R->>S: 13. 重排结果
    S->>Q: 14. 最终检索结果
    
    Q->>Gen: 15. 生成请求
    Gen->>Q: 16. 生成答案
    
    Q->>Acc: 17. 记录使用量
    Q->>Aud: 18. 记录操作日志
    
    Q->>G: 19. 返回答案
    G->>U: 20. 最终答案
```

## 4. 检索系统活动图

```mermaid
flowchart TD
    Start([开始]) --> Input[接收用户问题]
    Input --> Parse[问题解析]
    Parse --> Rewrite[问题重写]
    Rewrite --> Expand[查询扩展]
    
    Expand --> Parallel{并行检索}
    
    Parallel --> KW[关键词检索]
    Parallel --> Vec[向量检索]
    Parallel --> Graph[图谱检索]
    
    KW --> Merge[结果合并]
    Vec --> Merge
    Graph --> Merge
    
    Merge --> Filter[权限过滤]
    Filter --> Rerank[重排序]
    Rerank --> Score[相关性打分]
    
    Score --> Check{质量检查}
    Check -->|通过| Generate[答案生成]
    Check -->|不通过| Fallback[降级处理]
    
    Generate --> Format[格式化输出]
    Fallback --> Format
    
    Format --> End([结束])
```

## 5. 权限控制状态图

```mermaid
stateDiagram-v2
    [*] --> 未认证
    
    未认证 --> 认证中 : 提交凭证
    认证中 --> 已认证 : 认证成功
    认证中 --> 认证失败 : 认证失败
    认证失败 --> 未认证 : 重新认证
    
    已认证 --> 权限检查 : 访问资源
    权限检查 --> 访问授权 : 权限验证通过
    权限检查 --> 访问拒绝 : 权限验证失败
    
    访问授权 --> 资源访问 : 获取资源
    资源访问 --> 使用记录 : 记录使用情况
    使用记录 --> 审计日志 : 记录操作日志
    
    访问拒绝 --> 审计日志 : 记录拒绝日志
    审计日志 --> 已认证 : 继续会话
    
    已认证 --> 会话过期 : 超时
    会话过期 --> 未认证 : 重新登录
    
    已认证 --> 主动登出 : 用户登出
    主动登出 --> 未认证 : 清除会话
```

## 6. 数据处理流程图

```mermaid
flowchart LR
    subgraph "数据源"
        TSM[TSM文档库]
        InfoGo[Info-Go系统]
    end
    
    subgraph "采集层"
        Collector[数据采集器]
        Parser[文档解析器]
    end
    
    subgraph "处理层"
        Clean[数据清洗]
        Extract[信息提取]
        Structure[结构化处理]
    end
    
    subgraph "质量控制"
        Validate[数据验证]
        Score[质量评分]
        Filter[质量过滤]
    end
    
    subgraph "存储分发"
        Route[路由分发]
        Index[索引构建]
        Store[数据存储]
    end
    
    TSM --> Collector
    InfoGo --> Collector
    
    Collector --> Parser
    Parser --> Clean
    
    Clean --> Extract
    Extract --> Structure
    
    Structure --> Validate
    Validate --> Score
    Score --> Filter
    
    Filter --> Route
    Route --> Index
    Index --> Store
```

## 7. 微服务部署图

```mermaid
graph TB
    subgraph "Kubernetes集群"
        subgraph "Namespace: gateway"
            GW[API网关 Pod]
            LB[负载均衡 Pod]
        end
        
        subgraph "Namespace: auth"
            AUTH1[认证服务 Pod-1]
            AUTH2[认证服务 Pod-2]
            AUTHZ1[授权服务 Pod-1]
            AUTHZ2[授权服务 Pod-2]
        end
        
        subgraph "Namespace: business"
            QA1[问答服务 Pod-1]
            QA2[问答服务 Pod-2]
            QA3[问答服务 Pod-3]
            SEARCH1[检索服务 Pod-1]
            SEARCH2[检索服务 Pod-2]
        end
        
        subgraph "Namespace: data"
            KB1[知识库服务 Pod-1]
            KB2[知识库服务 Pod-2]
            KG1[知识图谱服务 Pod-1]
            CACHE1[缓存服务 Pod-1]
        end
        
        subgraph "Namespace: storage"
            ES[Elasticsearch集群]
            VECTOR[向量数据库集群]
            GRAPH[图数据库集群]
            REDIS[Redis集群]
        end
        
        subgraph "Namespace: monitoring"
            PROM[Prometheus]
            GRAF[Grafana]
            ELK[ELK Stack]
        end
    end
    
    subgraph "外部存储"
        NFS[NFS存储]
        S3[对象存储]
    end
    
    LB --> GW
    GW --> AUTH1
    GW --> AUTH2
    AUTH1 --> AUTHZ1
    AUTH2 --> AUTHZ2
    AUTHZ1 --> QA1
    AUTHZ2 --> QA2
    QA1 --> SEARCH1
    QA2 --> SEARCH2
    SEARCH1 --> KB1
    SEARCH2 --> KB2
    KB1 --> ES
    KB2 --> VECTOR
    KG1 --> GRAPH
    CACHE1 --> REDIS
    
    ES --> NFS
    VECTOR --> S3
    
    PROM --> AUTH1
    PROM --> QA1
    PROM --> SEARCH1
    GRAF --> PROM
    ELK --> AUTH1
    ELK --> QA1
```
