from Crypto.Cipher import AES
import base64
import os
import sys
import time
import hashlib
import requests
from utils.http_util import post_no_proxy
from domain.config.zxtech_config import config
from utils.encryption_util import decrypt

SECRET_KEY = decrypt(config['identify']['uac']['secret-key'], config['key-flag'])
APP_ID = config['identify']['uac']['app-id']
ACCESS_KEY = decrypt(config['identify']['uac']['access-key'], config['key-flag'])
SECRET = decrypt(config['identify']['uac']['secret'], config['key-flag'])
ENCODING_UTF8 = config['identify']['uac']['encoding-utf8']
TENANT_ID = config['identify']['uac']['tenant-id']
CLIENTIP = config['identify']['uac']['client-ip']
SYSTEMCODE = config['identify']['uac']['system-code']
VERIFY_URL = config['identify']['uac']['verify-url']
ITP_VALUE = decrypt(config['identify']['uac']['itp-value'], config['key-flag'])


class TokenVerify:
    def __init__(self, accountid, token):
        self.accountid = accountid
        self.token = token

    def sha256_hex(self, code_data):
        sha256_hash = hashlib.sha256()
        sha256_hash.update(code_data.encode('utf-8'))
        return sha256_hash.hexdigest()

    def aes_gcm_encrypt(self):
        # 13位时间戳
        time_millis = int(time.time() * 1000)
        # 明文
        plaintext = SECRET_KEY + "," + APP_ID + "," + ACCESS_KEY + "," + str(time_millis)
        # secret_key = base64.b64decode(secret)
        iv = os.urandom(12)
        aes_cipher = AES.new(SECRET.encode(ENCODING_UTF8), AES.MODE_GCM, iv)
        ciphertext, auth_tag = aes_cipher.encrypt_and_digest(plaintext.encode(ENCODING_UTF8))
        result = iv + ciphertext + auth_tag
        return base64.b64encode(result).decode(ENCODING_UTF8)

    def aes_gcm_decrypt(self, encrypted):
        res_bytes = base64.b64decode(encrypted.encode(ENCODING_UTF8))
        nonce = res_bytes[:12]
        ciphertext = res_bytes[12:-16]
        auth_tag = res_bytes[-16:]
        aes_cipher = AES.new(SECRET_KEY.encode(ENCODING_UTF8), AES.MODE_GCM, nonce)
        return aes_cipher.decrypt_and_verify(ciphertext, auth_tag).decode(ENCODING_UTF8)

    def token_verify(self):
        # 向UAC发起token验证请求
        # 请求header头
        accountid = self.accountid
        token = self.token
        tokenverify = TokenVerify(accountid=accountid, token=token)
        token_uac = tokenverify.aes_gcm_encrypt()
        headers = {
            'X-Auth-Value': token_uac,
            'X-Tenant-Id': TENANT_ID,
            'X-App-Id': APP_ID,
            'X-Itp-Value': ITP_VALUE,
            'Content-Type': 'application/json'
        }
        # 请求body体
        code_data = accountid + token + CLIENTIP + SYSTEMCODE
        verifyCode = tokenverify.sha256_hex(code_data)
        body = {
            'accountId': accountid,
            'clientIp': CLIENTIP,
            'systemCode': SYSTEMCODE,
            'token': token,
            'verifyCode': verifyCode
        }
        # 发送POST请求
        response = post_no_proxy(VERIFY_URL, headers=headers, json=body)
        # 解析响应
        response_data = response.json()
        code = response_data['code']
        if code == '0000':
            return True, response
        else:
            return False, response


