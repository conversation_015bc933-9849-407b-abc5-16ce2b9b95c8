@startuml ZTE产品检索系统4A架构安全模型图
!theme vibrant
skinparam backgroundColor #F5F5F5
skinparam componentStyle rectangle
skinparam packageStyle rectangle

title **ZTE产品检索问答系统 - 4A架构安全模型图**\n<i>ZTE Product Retrieval Q&A System - 4A Security Model</i>\n

' 定义安全层级样式
skinparam component {
    BackgroundColor<<perimeter>> #FFE4E1
    BackgroundColor<<network>> #E1F5FE
    BackgroundColor<<application>> #E8F5E8
    BackgroundColor<<data>> #FFF3E0
    BackgroundColor<<management>> #F3E5F5
    BorderThickness 2
    RoundCorner 8
}

' 安全边界定义
rectangle "**网络安全边界**" as NetworkPerimeter <<perimeter>> {
    component "防火墙" as Firewall
    component "WAF(Web应用防火墙)" as WAF
    component "DDoS防护" as DDoSProtection
    component "网络入侵检测" as NetworkIDS
}

rectangle "**认证安全层 (Authentication Security)**" as AuthSecurity <<network>> {
    component "UAC认证网关" as UACGateway {
        [员工账号认证] as EmployeeAuth
        [合作伙伴认证] as PartnerAuth
        [服务账号认证] as ServiceAuth
    }
    
    component "多因子认证(MFA)" as MFA {
        [密码认证] as PasswordAuth
        [数字证书] as CertAuth
        [OTP动态令牌] as OTPAuth
        [生物识别] as BiometricAuth
    }
    
    component "身份联邦" as IdentityFederation {
        [SAML 2.0] as SAML
        [OAuth 2.0] as OAuth
        [OpenID Connect] as OIDC
    }
    
    component "会话管理" as SessionManagement {
        [JWT令牌] as JWTToken
        [会话超时] as SessionTimeout
        [单点登录] as SSO
        [安全登出] as SecureLogout
    }
}

rectangle "**授权安全层 (Authorization Security)**" as AuthzSecurity <<application>> {
    component "权限控制引擎" as PermissionEngine {
        [RBAC角色控制] as RBAC
        [ABAC属性控制] as ABAC
        [ACL访问控制] as ACL
        [动态权限] as DynamicPermission
    }
    
    component "API安全网关" as APISecurityGateway {
        [接口鉴权] as APIAuth
        [流量控制] as RateLimit
        [接口加密] as APIEncryption
        [签名验证] as SignatureVerification
    }
    
    component "数据权限控制" as DataPermissionControl {
        [字段级权限] as FieldLevelPermission
        [行级权限] as RowLevelPermission
        [数据脱敏] as DataMasking
        [数据分类标签] as DataClassification
    }
}

rectangle "**审计安全层 (Accounting Security)**" as AuditSecurity <<data>> {
    component "安全日志中心" as SecurityLogCenter {
        [认证日志] as AuthLogs
        [授权日志] as AuthzLogs
        [操作日志] as OperationLogs
        [异常日志] as ExceptionLogs
    }
    
    component "实时监控" as RealTimeMonitoring {
        [行为分析] as BehaviorAnalysis
        [异常检测] as AnomalyDetection
        [威胁识别] as ThreatDetection
        [入侵检测] as IntrusionDetection
    }
    
    component "合规审计" as ComplianceAudit {
        [等保合规] as ClassifiedProtection
        [GDPR合规] as GDPRCompliance
        [企业合规] as CorporateCompliance
        [审计报告] as AuditReports
    }
}

rectangle "**管理安全层 (Administration Security)**" as AdminSecurity <<management>> {
    component "安全策略管理" as SecurityPolicyMgmt {
        [密码策略] as PasswordPolicy
        [访问策略] as AccessPolicy
        [加密策略] as EncryptionPolicy
        [备份策略] as BackupPolicy
    }
    
    component "安全运营中心" as SOC {
        [安全事件响应] as IncidentResponse
        [漏洞管理] as VulnerabilityMgmt
        [威胁情报] as ThreatIntelligence
        [安全态势] as SecurityPosture
    }
    
    component "密钥管理" as KeyManagement {
        [证书管理] as CertificateManagement
        [密钥轮换] as KeyRotation
        [硬件安全模块] as HSM
        [密钥托管] as KeyEscrow
    }
}

' 业务应用安全
rectangle "**业务应用安全**" as BusinessSecurity {
    component "产品检索安全" as ProductRetrievalSecurity {
        [查询权限控制] as QueryPermission
        [结果过滤] as ResultFiltering
        [敏感信息保护] as SensitiveInfoProtection
    }
    
    component "AI模型安全" as AIModelSecurity {
        [模型访问控制] as ModelAccessControl
        [输入验证] as InputValidation
        [输出过滤] as OutputFiltering
        [模型防攻击] as ModelAntiAttack
    }
}

' 数据安全层
rectangle "**数据安全层**" as DataSecurity {
    database "加密存储" as EncryptedStorage {
        [静态数据加密] as DataAtRest
        [传输加密] as DataInTransit
        [应用层加密] as ApplicationEncryption
    }
    
    database "数据备份安全" as BackupSecurity {
        [备份加密] as BackupEncryption
        [备份验证] as BackupVerification
        [灾难恢复] as DisasterRecovery
    }
}

' 外部安全集成
cloud "**外部安全服务**" as ExternalSecurityServices {
    [ZTE安全运营中心] as ZTESOCSecurity
    [威胁情报平台] as ThreatIntelligencePlatform
    [安全设备管理] as SecurityDeviceManagement
}

' 安全流程连接
Firewall --> WAF : 流量过滤
WAF --> UACGateway : 安全访问
UACGateway --> MFA : 多因子验证
MFA --> PermissionEngine : 权限验证
PermissionEngine --> APISecurityGateway : API授权

' 横向安全监控
SecurityLogCenter ..> UACGateway : 认证监控
SecurityLogCenter ..> PermissionEngine : 授权监控
SecurityLogCenter ..> APISecurityGateway : API监控
RealTimeMonitoring ..> SecurityLogCenter : 日志分析

' 安全管理流程
SecurityPolicyMgmt --> UACGateway : 认证策略
SecurityPolicyMgmt --> PermissionEngine : 授权策略
SOC --> RealTimeMonitoring : 安全监控
SOC --> ZTESOCSecurity : 威胁联动

' 业务安全集成
APISecurityGateway --> ProductRetrievalSecurity : 业务授权
DataPermissionControl --> ProductRetrievalSecurity : 数据权限
AIModelSecurity --> ProductRetrievalSecurity : AI安全

' 数据安全连接
DataPermissionControl --> EncryptedStorage : 数据保护
SecurityLogCenter --> BackupSecurity : 日志备份
KeyManagement --> EncryptedStorage : 密钥管理

' 安全等级标识
note top of NetworkPerimeter
    **网络安全边界**
    - 防护等级: 三级等保
    - 网络隔离: DMZ部署
    - 流量监控: 7x24实时监控
end note

note top of AuthSecurity
    **认证安全要求**
    - 强身份认证: 双因子认证
    - 联邦认证: 支持企业SSO
    - 会话安全: JWT + 安全cookie
    - 认证审计: 全程日志记录
end note

note top of AuthzSecurity
    **授权安全策略**
    - 最小权限原则
    - 基于角色的访问控制
    - 动态权限调整
    - 细粒度数据权限
end note

note top of AuditSecurity
    **审计安全标准**
    - 不可抵赖性
    - 完整性保护
    - 实时监控告警
    - 合规性检查
end note

' 安全等级图例
legend bottom
    |= 安全层级 |= 颜色 |= 防护重点 |
    |<#FFE4E1> | 网络边界 | 外部威胁防护 |
    |<#E1F5FE> | 认证层 | 身份验证安全 |
    |<#E8F5E8> | 授权层 | 权限控制安全 |
    |<#FFF3E0> | 审计层 | 日志监控安全 |
    |<#F3E5F5> | 管理层 | 策略管理安全 |
    
    == 安全防护体系 ==
    **纵深防御**: 多层安全防护
    **零信任**: 持续验证，永不信任
    **合规要求**: 满足等保三级要求
    **威胁感知**: 主动防御能力
endlegend

@enduml