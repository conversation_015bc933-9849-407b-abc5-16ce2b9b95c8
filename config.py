from domain.constants.ConfigEnum import ConfigEnum
import yaml
from pathlib import Path

# 获取项目的绝对路径
def get_project_root():
    # 获取当前脚本的绝对路径
    current_file = Path(__file__).resolve()

    # 获取当前脚本所在目录的绝对路径
    current_directory = current_file.parent


    return current_directory

project_root = get_project_root()
# 配置使用本地配置文件还Apollo
config_source = ConfigEnum.APOLLO
# 本地配置文件路径
local_config_source = project_root.joinpath('./resource/config/zxtech_config_dev.yaml')
# apollo相关配置

# 配置环境
env = 'dev'


def __get_configs_from_apollo__():
    pass


def __get_configs_from_local__():
    with open(local_config_source, 'r',encoding='utf-8') as f:
        config = yaml.safe_load(f)
        return config

def get_config():
    if config_source == ConfigEnum.LOCAL:
        return __get_configs_from_local__()
    else:
        return __get_configs_from_apollo__()

config = get_config()