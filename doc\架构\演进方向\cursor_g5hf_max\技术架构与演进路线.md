# 产品技术问答系统 技术架构与演进路线（2025Q3-Q4）

版本：v1.0  
日期：2025-08-08  
范围：面向 BN / OTN / FM 多产品线的统一问答平台

---

## 一、总体技术架构

```plantuml
@startuml
!theme plain
left to right direction

skinparam rectangle BackgroundColor White
skinparam rectangle BorderColor Black

package "数据源" #AliceBlue {
  file "TSM 文档库" as TSM
  database "Info-Go" as INFO
}

package "采集与前置处理" #LightGreen {
  component "Ingestor\n(调度/增量)" as ING
  component "Parser/OCR\n(Word/PDF/PPT/Excel/图片)" as PAR
  component "Cleaner\n(去重/歧义/规范化)" as CLN
  component "Summarizer/Structurer\n(摘要提纯/结构化抽取)" as SUM
  component "Tiering\n(热/温/冷)" as TIER
  component "Indexer\n(ES/向量/KG/DB)" as IDX
}

package "存储层" #Moccasin {
  database "Elasticsearch\n(关键字/稀疏语义)" as ES
  database "向量库\n(密集语义/BGE)" as VEC
  database "知识图谱\n(实体/关系)" as KG
  database "元数据/ACL/画像\n(DB)" as META
}

package "检索与生成" #Wheat {
  component "Query Rewrite\n(语义改写/消歧)" as QR
  component "Retriever\n(稀疏+密集+KG 多路召回)" as RET
  component "Reranker(BGE)\n(交叉编码器)" as RR
  component "Fusion\n(融合打分)" as FUS
  component "AnswerGen\n(RAG+事实校验)" as GEN
}

package "缓存与预加载" #Cornsilk {
  component "Semantic Cache\n(语义/检索结果)" as SC
  component "Chunk Cache\n(热点切片)" as CC
  component "Preloader\n(基于画像/历史预测)" as PRE
}

package "评估与运营" #Thistle {
  component "Evaluation\n(召回/重排/生成)" as EVAL
  component "AB 实验/监控\n(性能/体验/SLA)" as EXP
  component "行为分析\n(漏斗/高频场景)" as UBA
  component "FAQ 管理\n(人工补全/推荐)" as FAQ
}

package "接入与安全" #Beige {
  component "API Gateway\n(限流/熔断/路由)" as GW
  component "Auth/ACL\n(SSO/OIDC/ABAC)" as AUTH
}

TSM --> ING
INFO --> ING
ING --> PAR
PAR --> CLN
CLN --> SUM
SUM --> TIER
TIER --> IDX
IDX --> ES
IDX --> VEC
IDX --> KG
IDX --> META

GW --> AUTH
AUTH --> QR
QR --> PRE
PRE --> SC
QR --> RET
SC --> RET
RET --> RR
RR --> FUS
FUS --> GEN
GEN --> GW

EVAL .. RET
EVAL .. RR
EVAL .. GEN
EXP .. GW
UBA .. GW
FAQ .. GEN
SC .. EXP
CC .. RET

note bottom of TIER
  分层策略：
  - 热：近30天、高频文档
  - 温：近6个月、常见文档
  - 冷：长期低频
  存储/索引/缓存策略随层级调整
end note

note bottom of RET
  多路召回：
  1) 关键字/BM25/稀疏语义
  2) 密集语义（BGE 向量）
  3) 知识图谱关系检索
end note

@enduml
```

要点：
- 前置处理将“去重/消歧/摘要提纯/结构化抽取/冷热分层”纳入统一流水线，保证索引质量与查询时延。
- 检索采用“稀疏+密集+KG”多路召回，交叉编码器重排，事实校验兜底，输出可点击引用角标。
- 引入“语义缓存+热点切片缓存+预加载”，在不牺牲新鲜度的前提下降低端到端时延。
- 评估与运营贯穿全链路，提供离线基准与在线 A/B，驱动持续优化。

---

## 二、关键改造项与技术要点

- 数据前置处理：
  - 预处理（AI+人工标注）：识别标题层级、表格/列表抽取、命令/参数结构化；图片/扫描件 OCR；单位/术语标准化。
  - 数据分层（热/温/冷）：结合访问频度与时效性，分层决定索引刷新频率、缓存策略与副本数。
  - 结构化存储：图谱承载“产品-版本-特性-命令-指标-案例”关系，DB 承载元数据、ACL、用户画像。
  - 文档摘要-结构化提纯：为长文生成多粒度摘要与章节关键词，用于 Query Rewrite 与重排特征。

- 数据清洗：
  - 去重：文档级（版本/哈希）、切片级（语义相似度阈值）；
  - 歧义处理：同名产品/指标消歧（上下文+图谱路径+组织视角）。

- 检索增强：
  - 稀疏语义（BM25/SPLADE 可选）+ 密集语义（BGE 向量），加权融合；
  - 智能预加载：基于用户画像/历史对话/热门主题预测查询意图，提前计算召回与重排候选并进入缓存。

- 评估框架：
  - 自动化评测：相关性、精确度、事实性、可读性；
  - 性能指标：P95 首字、端到端延迟、缓存命中率、资源成本；
  - 用户体验：CSAT、点击引用率、追问率。

- 后置处理：
  - 重排优化：领域数据微调；特征引入（章节位置、标题匹配、图谱距离、摘要覆盖率）。
  - 缓存优化：语义哈希一致性、结果有效期分层、冷热切片自动晋升；
  - 人工 FAQ 补全：提供缺口发现与推荐，结合编辑工作台；
  - 用户行为分析：路径漏斗、高频问题簇、失败模式定位，驱动专项精调与高频场景模板化。

---

## 三、关键流程时序（含预加载）

```plantuml
@startuml
actor User
participant GW as "API Gateway"
participant AUTH as "Auth/ACL"
participant PRE as "Preloader"
participant SC as "Semantic Cache"
participant RET as "Retriever"
participant RR as "Reranker"
participant GEN as "AnswerGen"
participant EVAL as "Evaluation"
participant UBA as "Behavior Analytics"

== 会话建立 ==
User -> GW: open_session
GW -> AUTH: verify(token)
AUTH --> GW: allow(acl, profile)
GW -> PRE: prewarm(profile)
PRE -> SC: prime(predicted_queries)

== 用户提问 ==
User -> GW: ask(question)
GW -> SC: lookup(semantic_hash)
SC --> GW: hit/miss
GW -> RET: retrieve(q, acl)  
RET -> RR: rank(candidates)
RR --> RET: ranked
RET -> GEN: generate(topK, citations)
GEN --> GW: stream(answer)
GW -> EVAL: log(q, answer, metrics)
GW -> UBA: log(behavior)

@enduml
```

---

## 四、演进路线与里程碑

### 4.1 里程碑一（2025-09-30）

- 数据前置处理：
  - 预处理 MVP：Parser/OCR、标题/段落抽取、基础结构化；
  - 去重与规范化 v1：文档/切片级哈希与近似去重；
  - 分层策略 v1：热/温/冷判定与索引刷新频率配置。
- 检索处理：
  - 稀疏+密集混合检索 v1（BM25 + BGE 向量），可配置权重；
  - 智能预加载 v1：基于热门查询和简单画像的规则预热；
  - 图谱基础检索路径（产品→版本→命令）。
- 后置处理：
  - 重排模型 baseline（BGE Cross-Encoder），网格调参；
  - 缓存 v1：查询结果缓存、热点切片缓存；
  - FAQ 工具 v1：人工补全与引用一键插入。
- 评估与运营：
  - 自动化评测 v1：相关性/精确度/事实一致性离线评测；
  - 在线看板 v1：P95 首字、命中率、CSAT；
  - A/B 能力接入（灰度路由）。
- 验收指标：
  - P95 首字 ≤ 5s；P99 ≤ 8s；缓存命中率 ≥ 25%；
  - 答案质量 ≥ 90% 达 2~3 分；
  - 关键链路稳定性 ≥ 99.9%。

### 4.2 里程碑二（2025-12-30）

- 数据前置处理：
  - 摘要提纯与章节关键词；命令/参数结构化增强；
  - 去重/歧义 v2：语义近邻 + 领域词库；
  - 分层策略 v2：自动晋升/降级与成本感知存储。
- 检索处理：
  - 稀疏语义（可选 SPLADE）与密集语义的自适应融合；
  - 智能预加载 v2：基于用户画像与序列预测（下一问意图）预热；
  - 图谱检索增强：属性约束与多跳路径评分。
- 后置处理：
  - 重排模型领域精调（少量标注集）；特征扩展（章节/摘要/图谱距离）；
  - 缓存 v2：语义缓存一致性、分层 TTL、近线重算；
  - FAQ 推荐：基于缺口检测与点击行为自动建议。
- 评估与运营：
  - 自动化评测 v2：覆盖用户体验主观题；
  - A/B 扩展：策略/权重/提示词多维实验；
  - 成本与SLA优化：同等SLA下降 15% 资源成本。
- 验收指标：
  - P95 首字 ≤ 4s；缓存命中率 ≥ 40%；
  - 事实性校验通过率 ≥ 95%；
  - 高频场景（前20类）一次命中率 ≥ 85%。

---

## 五、风险与依赖

- 上游依赖：TSM/Info-Go 增量可用性与元数据完整性；
- 模型与算力：BGE 编码/重排/GPU 资源保障，QoS 限流与降级；
- 权限与合规：ABAC 标签一致性；日志审计与脱敏策略闭环；
- 数据质量：OCR 低质页、扫描件、图片表格；需要抽检与回溯机制。

---

## 六、落地清单（摘）

- 规格与接口：检索/重排/生成/评估/FAQ/画像/预热接口定义；
- 配置与权重：召回权重、重排阈值、缓存 TTL、分层规则参数化；
- 可观测性：Tracing、结构化日志、失败样本自动采集。

（本文件与《开发设计.md》配套使用：前者给出架构与路线，后者给出实现与接口细节。）


