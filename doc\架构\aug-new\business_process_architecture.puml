@startuml ZTE产品检索系统业务流程架构图
!theme plain
title ZTE产品检索系统 - 业务流程架构图

skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 11
skinparam activityStyle rectangle

' 用户角色
actor "业务用户" as User
actor "系统管理员" as Admin

' 主要业务流程
package "**核心业务流程**" as CoreBusinessProcess {
    
    ' 用户查询流程
    rectangle "**用户查询处理流程**" as QueryProcess #E3F2FD {
        activity "1. 接收用户查询" as ReceiveQuery #BBDEFB
        activity "2. UAC身份认证" as Authentication #BBDEFB
        activity "3. 查询预处理" as QueryPreprocess #BBDEFB
        activity "4. 实体识别提取" as EntityExtraction #BBDEFB
        activity "5. 策略路由选择" as StrategySelection #BBDEFB
    }
    
    ' 智能处理流程
    rectangle "**智能处理流程**" as IntelligentProcess #FFF3E0 {
        activity "查询重写处理" as QueryRewrite #FFE0B2
        activity "多轮对话管理" as DialogManagement #FFE0B2
        activity "意图理解分析" as IntentAnalysis #FFE0B2
        activity "上下文管理" as ContextManagement #FFE0B2
        activity "语言类型判断" as LanguageDetection #FFE0B2
    }
    
    ' 检索处理流程
    rectangle "**多路检索流程**" as RetrievalProcess #E8F5E8 {
        activity "ES文本检索" as ESRetrieval #C8E6C9
        activity "Milvus向量检索" as MilvusRetrieval #C8E6C9
        activity "KG图谱检索" as KGRetrieval #C8E6C9
        activity "结果合并处理" as ResultMerging #C8E6C9
        activity "去重过滤" as Deduplication #C8E6C9
    }
    
    ' AI模型处理流程
    rectangle "**AI模型处理流程**" as AIModelProcess #F3E5F5 {
        activity "文本向量化" as TextEmbedding #E1BEE7
        activity "相似度计算" as SimilarityCalculation #E1BEE7
        activity "结果重排序" as ResultReranking #E1BEE7
        activity "阈值判断" as ThresholdJudgment #E1BEE7
        activity "LLM答案生成" as AnswerGeneration #E1BEE7
    }
    
    ' 结果处理流程
    rectangle "**结果处理流程**" as ResultProcess #FFF8E1 {
        activity "答案质量评估" as QualityAssessment #FFECB3
        activity "流式响应处理" as StreamProcessing #FFECB3
        activity "响应格式化" as ResponseFormatting #FFECB3
        activity "监控数据记录" as MonitoringRecord #FFECB3
        activity "返回最终结果" as FinalResponse #FFECB3
    }
}

' 监控管理流程
package "**监控管理流程**" as MonitoringProcess {
    rectangle "**性能监控流程**" as PerformanceMonitoring #E0F2F1 {
        activity "召回数据监控" as RecallMonitoring #B2DFDB
        activity "性能指标统计" as PerformanceStats #B2DFDB
        activity "异常检测告警" as AnomalyDetection #B2DFDB
        activity "数据分析报告" as DataAnalysis #B2DFDB
    }
    
    rectangle "**系统管理流程**" as SystemManagement #FCE4EC {
        activity "配置管理" as ConfigManagement #F8BBD9
        activity "日志管理" as LogManagement #F8BBD9
        activity "系统健康检查" as HealthCheck #F8BBD9
        activity "容量规划" as CapacityPlanning #F8BBD9
    }
}

' 数据处理流程
package "**数据处理流程**" as DataProcessFlow {
    rectangle "**数据存储流程**" as DataStorage #F5F5F5 {
        activity "ES索引管理" as ESIndexing #E0E0E0
        activity "向量数据管理" as VectorManagement #E0E0E0
        activity "图谱数据管理" as GraphManagement #E0E0E0
        activity "缓存数据管理" as CacheManagement #E0E0E0
    }
}

' 主要业务流程连接
User --> ReceiveQuery : 发起查询请求
ReceiveQuery --> Authentication : 身份验证
Authentication --> QueryPreprocess : 验证通过
QueryPreprocess --> EntityExtraction : 预处理完成
EntityExtraction --> StrategySelection : 实体提取完成

' 智能处理分支
QueryPreprocess --> QueryRewrite : 多轮对话
QueryRewrite --> DialogManagement : 查询重写
DialogManagement --> IntentAnalysis : 对话管理
IntentAnalysis --> ContextManagement : 意图分析
ContextManagement --> LanguageDetection : 上下文处理

' 检索流程分支
StrategySelection --> ESRetrieval : 策略1:文本检索
StrategySelection --> MilvusRetrieval : 策略2:向量检索
StrategySelection --> KGRetrieval : 策略3:图谱检索

ESRetrieval --> ResultMerging : ES结果
MilvusRetrieval --> ResultMerging : 向量结果
KGRetrieval --> ResultMerging : 图谱结果
ResultMerging --> Deduplication : 合并完成

' AI模型处理
QueryPreprocess --> TextEmbedding : 向量化
TextEmbedding --> MilvusRetrieval : 向量查询
Deduplication --> SimilarityCalculation : 相似度计算
SimilarityCalculation --> ResultReranking : 重排序
ResultReranking --> ThresholdJudgment : 阈值过滤
ThresholdJudgment --> AnswerGeneration : LLM生成

' 结果处理
AnswerGeneration --> QualityAssessment : 质量评估
QualityAssessment --> StreamProcessing : 流式处理
StreamProcessing --> ResponseFormatting : 格式化
ResponseFormatting --> MonitoringRecord : 监控记录
MonitoringRecord --> FinalResponse : 最终响应
FinalResponse --> User : 返回结果

' 监控流程
MonitoringRecord --> RecallMonitoring : 监控数据
RecallMonitoring --> PerformanceStats : 性能统计
PerformanceStats --> AnomalyDetection : 异常检测
AnomalyDetection --> DataAnalysis : 数据分析

' 管理流程
Admin --> ConfigManagement : 配置管理
Admin --> LogManagement : 日志管理
Admin --> HealthCheck : 健康检查
Admin --> CapacityPlanning : 容量规划

' 数据管理
ESRetrieval --> ESIndexing : ES数据
MilvusRetrieval --> VectorManagement : 向量数据
KGRetrieval --> GraphManagement : 图谱数据
MonitoringRecord --> CacheManagement : 缓存数据

' 决策点和条件分支
note right of EntityExtraction
    **实体识别结果分支**
    - 有产品实体: 产品相关检索
    - 有系列实体: 系列相关检索  
    - 无实体: 通用知识检索
end note

note right of StrategySelection
    **检索策略选择**
    - 精确匹配: ES精确检索
    - 语义相似: Milvus向量检索
    - 关系推理: KG图谱检索
    - 混合策略: 多路并行检索
end note

note right of ThresholdJudgment
    **阈值判断逻辑**
    - 高相关性(>0.8): 直接返回
    - 中等相关性(0.5-0.8): LLM生成
    - 低相关性(<0.5): 无相关内容
end note

note bottom of MonitoringProcess
    **监控指标**
    - 召回率、准确率
    - 响应时间、吞吐量
    - 错误率、可用性
    - 资源使用率
end note

@enduml
