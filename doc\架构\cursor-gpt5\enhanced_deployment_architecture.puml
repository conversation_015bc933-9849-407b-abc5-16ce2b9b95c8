@startuml ZTE 产品检索系统增强版部署架构图
!theme plain

title ZTE 产品检索系统 - 增强版部署架构图

skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 11
skinparam nodeStyle rectangle

cloud "用户接入层" as Access {
  actor "业务用户" as BizUser
  actor "系统管理员" as Admin
  component "Web浏览器" as Browser
  component "API客户端" as APIClient
}

node "边缘与入口" as Edge #E3F2FD {
  component "CDN/静态加速" as CDN #BBDEFB
  component "WAF/防火墙" as WAF #BBDEFB
  component "Nginx 负载均衡" as Nginx #BBDEFB
  component "API网关 (限流/监控/鉴权前置)" as APIGateway #BBDEFB
}

node "应用服务集群" as AppCluster #FFF3E0 {
  node "K8s集群 (生产)" as K8sProd #FFE0B2 {
    node "命名空间: zte-qa" as NSQA {
      component "Flask微服务 (Deployment) x3" as FlaskSvcQ {
        [main.py / controller / service]
        [SSE / Streaming]
        [探针: /info]
        [端口: 10023]
      }
    }
    node "命名空间: zte-admin" as NSAdmin {
      component "监控&管理微服务" as AdminSvc {
        [监控接口]
        [任务调度]
        [周期分析]
      }
    }
  }
}

node "数据与AI服务集群" as DataAI #E8F5E8 {
  node "ElasticSearch 集群" as ESCluster #C8E6C9 {
    database "ES Master x3" as ESM {
      [集群协调]
      [索引管理]
      [元数据]
    }
    database "ES Data xN" as ESD {
      [文档存储]
      [索引分片]
      [查询执行]
    }
  }
  node "Milvus 向量集群" as MilvusCluster #C8E6C9 {
    database "Milvus Coordinator" as MilvusC {
      [元数据/调度]
      [负载均衡]
    }
    database "Milvus Data xN" as MilvusD {
      [向量存储]
      [索引构建]
      [相似检索]
    }
  }
  node "Nebula 图数据库集群" as NebulaCluster #C8E6C9 {
    database "Nebula Meta x3" as NebulaMeta {
      [Schema]
      [元数据]
    }
    database "Nebula Graph xN" as NebulaGraph {
      [查询引擎]
      [执行计划]
    }
    database "Nebula Storage xN" as NebulaStorage {
      [图存储]
      [分片/副本]
    }
  }
}

cloud "外部企业服务" as External #F3E5F5 {
  component "UAC 认证中心" as UAC
  component "Apollo 配置中心" as Apollo
  component "NebulaBiz LLM" as NebulaBiz
  component "BGE-M3 模型服务" as BGEService
}

node "可观测与运维" as Observability #FCE4EC {
  component "Prometheus + Grafana" as Monitor
  component "ELK Stack" as ELK
  component "Jaeger 链路追踪" as Jaeger
}

' 网络与调用
Browser --> CDN : HTTPS 静态资源
APIClient --> APIGateway : HTTPS API
CDN --> Nginx : 反向代理
APIGateway --> Nginx : API路由
Nginx --> FlaskSvcQ : 负载分发 /health

FlaskSvcQ --> ESCluster : 文本检索
FlaskSvcQ --> MilvusCluster : 向量检索
FlaskSvcQ --> NebulaCluster : 图谱查询

FlaskSvcQ --> UAC : Token 验证
FlaskSvcQ --> Apollo : 配置同步
FlaskSvcQ --> NebulaBiz : LLM 生成
FlaskSvcQ --> BGEService : 向量化/重排

AppCluster --> Monitor : 指标暴露
AppCluster --> ELK : 日志采集
DataAI --> Monitor : 组件监控
External --> Monitor : 依赖探测

rectangle "部署说明" as DeployNote #F5F5F5 {
  note as DNote
    建议配置：
    - 副本数: Flask 3+，跨可用区
    - 资源: CPU 4/内存 8G/实例；ES(内存 32G*3)；Milvus(64G*2)；Nebula(16G*3)
    - Ingress: TLS1.3，HSTS，HTTP/2
    - 安全: WAF+Bot防护、最小权限、密钥轮换
    - 发布: 蓝绿/金丝雀，自动回滚
    - 存储: ES冷热分层，Milvus分区策略，Nebula副本
  end note
}

legend right
  |= 层次 |= 组件 |
  | 边缘与入口 | CDN/WAF/Nginx/APIGW |
  | 应用服务集群 | Flask 微服务/监控管理 |
  | 数据与AI | ES/Milvus/Nebula/LLM/BGE |
  | 外部企业服务 | UAC/Apollo/AI服务 |
  | 可观测与运维 | Prometheus/ELK/Jaeger |
endlegend

@enduml


