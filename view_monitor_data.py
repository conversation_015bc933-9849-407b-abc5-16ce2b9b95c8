"""
查看和分析pkl监控数据文件的工具
"""

import pickle
import os
from datetime import datetime
from pathlib import Path
import json

def load_pkl_file(file_path):
    """加载pkl文件"""
    try:
        with open(file_path, 'rb') as f:
            data = pickle.load(f)
        return data
    except Exception as e:
        print(f"加载文件失败: {e}")
        return None

def display_monitor_data(data):
    """展示监控数据"""
    if not data:
        print("没有数据可显示")
        return
    
    print("=" * 80)
    print("                    监控数据文件内容")
    print("=" * 80)
    
    # 1. 基本信息
    print("\n[1] 基本信息")
    print("-" * 40)
    print(f"查询总数: {data.get('query_counter', 0)}")
    print(f"监控开始时间: {data.get('start_time', 'N/A')}")
    
    # 2. 全局统计
    print("\n[2] 全局统计数据")
    print("-" * 40)
    global_stats = data.get('global_stats', {})
    
    for source in ['es', 'milvus', 'kg']:
        if source in global_stats:
            stats = global_stats[source]
            print(f"\n{source.upper()}召回:")
            print(f"  - 召回次数: {stats.recall_count}")
            print(f"  - 命中次数: {stats.hit_count}")
            print(f"  - Top1命中: {stats.top1_count}")
            print(f"  - Top3命中: {stats.top3_count}")
            print(f"  - Top5命中: {stats.top5_count}")
            print(f"  - 总得分: {stats.total_score:.2f}")
            print(f"  - 贡献率: {stats.contribution_rate:.2f}%")
            print(f"  - 最后更新: {stats.last_update}")
    
    # 3. 排行榜
    print("\n[3] 召回源排行榜")
    print("-" * 40)
    leaderboard = data.get('leaderboard', [])
    
    if leaderboard:
        print(f"{'排名':<4} {'召回源':<8} {'贡献率':<10} {'总得分':<10} {'Top1':<6} {'命中':<8}")
        print("-" * 40)
        for idx, item in enumerate(leaderboard, 1):
            print(f"{idx:<4} {item['source']:<8} "
                  f"{item['contribution_rate']:<9.1f}% "
                  f"{item['total_score']:<10.2f} "
                  f"{item['top1_count']:<6} "
                  f"{item['hit_count']:<8}")
    else:
        print("暂无排行榜数据")
    
    # 4. 小时统计
    print("\n[4] 小时统计摘要")
    print("-" * 40)
    hourly_stats = data.get('hourly_stats', {})
    
    if hourly_stats:
        # 只显示最近5个小时的数据
        recent_hours = sorted(hourly_stats.keys())[-5:]
        for hour in recent_hours:
            stats = hourly_stats[hour]
            print(f"\n{hour}:")
            for source in ['es', 'milvus', 'kg']:
                if source in stats:
                    s = stats[source]
                    if s.hit_count > 0:
                        print(f"  {source.upper()}: 命中{s.hit_count}次, Top1={s.top1_count}, 得分={s.total_score:.2f}")
    else:
        print("暂无小时统计数据")

def export_to_json(data, output_file):
    """导出数据为JSON格式"""
    # 转换为可序列化的格式
    export_data = {
        'query_counter': data.get('query_counter', 0),
        'start_time': str(data.get('start_time', '')),
        'leaderboard': data.get('leaderboard', []),
        'global_stats': {}
    }
    
    # 转换全局统计
    global_stats = data.get('global_stats', {})
    for source in ['es', 'milvus', 'kg']:
        if source in global_stats:
            stats = global_stats[source]
            export_data['global_stats'][source] = {
                'recall_count': stats.recall_count,
                'hit_count': stats.hit_count,
                'top1_count': stats.top1_count,
                'top3_count': stats.top3_count,
                'top5_count': stats.top5_count,
                'total_score': stats.total_score,
                'contribution_rate': stats.contribution_rate,
                'last_update': stats.last_update
            }
    
    # 保存为JSON
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(export_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n数据已导出到: {output_file}")

def list_pkl_files(directory="monitor_data"):
    """列出所有pkl文件"""
    monitor_dir = Path(directory)
    if not monitor_dir.exists():
        print(f"目录 {directory} 不存在")
        return []
    
    pkl_files = list(monitor_dir.glob("*.pkl"))
    return sorted(pkl_files, key=lambda x: x.stat().st_mtime, reverse=True)

def main():
    print("\n监控数据查看工具")
    print("=" * 40)
    
    # 1. 列出所有pkl文件
    pkl_files = list_pkl_files()
    
    if not pkl_files:
        print("没有找到监控数据文件")
        print("提示：监控数据通常保存在 monitor_data/ 目录下")
        return
    
    print("\n找到以下监控数据文件：")
    for idx, file in enumerate(pkl_files, 1):
        file_size = file.stat().st_size / 1024  # KB
        mod_time = datetime.fromtimestamp(file.stat().st_mtime)
        print(f"{idx}. {file.name} ({file_size:.1f}KB) - 修改时间: {mod_time}")
    
    # 2. 选择要查看的文件
    if len(pkl_files) == 1:
        selected_file = pkl_files[0]
        print(f"\n自动选择唯一的文件: {selected_file.name}")
    else:
        try:
            choice = input(f"\n请选择要查看的文件 (1-{len(pkl_files)}, 默认1): ").strip()
            if not choice:
                choice = 1
            else:
                choice = int(choice)
            
            if 1 <= choice <= len(pkl_files):
                selected_file = pkl_files[choice - 1]
            else:
                print("无效选择，使用最新文件")
                selected_file = pkl_files[0]
        except:
            print("输入错误，使用最新文件")
            selected_file = pkl_files[0]
    
    print(f"\n正在加载: {selected_file}")
    
    # 3. 加载并显示数据
    data = load_pkl_file(selected_file)
    if data:
        display_monitor_data(data)
        
        # 4. 询问是否导出为JSON
        export = input("\n是否导出为JSON格式？(y/n, 默认n): ").strip().lower()
        if export == 'y':
            json_file = selected_file.with_suffix('.json')
            export_to_json(data, json_file)
    
    print("\n" + "=" * 80)
    print("查看完成")

def quick_view():
    """快速查看最新的监控数据"""
    pkl_files = list_pkl_files()
    if not pkl_files:
        print("没有找到监控数据文件")
        return
    
    latest_file = pkl_files[0]
    print(f"加载最新文件: {latest_file.name}")
    
    data = load_pkl_file(latest_file)
    if data:
        # 只显示核心信息
        print("\n=== 快速摘要 ===")
        print(f"查询总数: {data.get('query_counter', 0)}")
        print(f"监控开始: {data.get('start_time', 'N/A')}")
        
        print("\n排行榜:")
        leaderboard = data.get('leaderboard', [])
        for idx, item in enumerate(leaderboard[:3], 1):
            print(f"  {idx}. {item['source']}: {item['contribution_rate']:.1f}% (得分: {item['total_score']:.2f})")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == '--quick':
        # 快速查看模式
        quick_view()
    else:
        # 完整查看模式
        main()