def relate_prompt(user_content, question):
    _prompt = {
                "sys_prompt": '''判断当前用户输入内容与上一轮对话的关联性，仅用小数回答关联度，得分范围0.0至1.0。如果当前用户输入中出现指代词，如它、他、这个等词，或者谓语宾语不全，则得分不得小于0.1。''',
                "user_prompt": f'''上下文:
            ####
            {user_content}
            ####
            用户问题：
            @@@
            {question}
            @@@
            这两次输入是否关联'''}
    return _prompt
