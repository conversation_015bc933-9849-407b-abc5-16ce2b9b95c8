@startuml ZTE 产品检索系统增强版数据流图
!theme plain

title ZTE 产品检索系统 - 增强版数据流架构图

skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 11
skinparam componentStyle rectangle
skinparam ArrowColor #2E86C1
skinparam ArrowThickness 2

package "**数据源层**" as DataSourceLayer #E8F5E8 {
  component "用户输入" as UserInput #C8E6C9 {
    [查询文本]
    [历史对话]
    [会话UUID]
    [用户工号]
  }
  component "配置数据源" as ConfigSource #C8E6C9 {
    [Apollo配置]
    [本地配置]
    [环境变量]
    [密钥/证书]
  }
  component "外部服务数据" as ExternalData #C8E6C9 {
    [UAC认证响应]
    [LLM响应]
    [嵌入向量]
    [重排序结果]
  }
}

package "**数据处理层**" as ProcessLayer #FFF3E0 {
  component "输入处理" as InputProcessing #FFE0B2 {
    [参数验证]
    [数据清洗]
    [格式标准化]
    [编码转换]
  }
  component "文本处理" as TextProcessing #FFE0B2 {
    [语言检测]
    [分词/停用词]
    [实体识别]
    [查询重写]
  }
  component "向量化处理" as Vectorization #FFE0B2 {
    [文本向量化]
    [归一化]
    [维度映射]
    [相似度计算]
  }
  component "召回融合" as RecallFusion #FFE0B2 {
    [ES/Milvus/KG并行]
    [RRF融合]
    [结果去重]
    [TopN截断]
  }
  component "质量控制" as QualityGate #FFE0B2 {
    [阈值判断]
    [置信度评估]
    [黑白名单过滤]
    [异常检测]
  }
}

package "**数据存储层**" as StorageLayer #E3F2FD {
  database "ElasticSearch" as ES #BBDEFB {
    [倒排索引]
    [多字段检索]
    [聚合数据]
  }
  database "Milvus向量库" as Milvus #BBDEFB {
    [文档向量]
    [向量索引]
    [分区/副本]
  }
  database "Nebula图数据库" as Nebula #BBDEFB {
    [实体节点]
    [关系边]
    [属性KV]
  }
  storage "缓存(Redis/本地)" as Cache #BBDEFB {
    [查询缓存]
    [结果缓存]
    [配置缓存]
    [会话缓存]
  }
}

package "**数据输出层**" as OutputLayer #F3E5F5 {
  component "答案组装" as AnswerAssembly #E1BEE7 {
    [引用/来源]
    [元数据封装]
    [格式化JSON]
    [SSE流式]
  }
  component "监控指标" as Metrics #E1BEE7 {
    [召回统计]
    [时延分布]
    [质量指标]
    [错误计数]
  }
  component "日志/审计" as Logging #E1BEE7 {
    [访问日志]
    [业务日志]
    [错误日志]
    [审计事件]
  }
}

rectangle "**数据质量与安全**" as DataQuality #FFF8E1 {
  component "数据验证" as DataValidation #FFECB3 {
    [Schema校验]
    [类型检查]
    [业务规则]
    [边界值]
  }
  component "加密与访问控制" as Security #FFECB3 {
    [HTTPS/证书]
    [数据脱敏]
    [Token校验]
    [最小权限]
  }
}

' 主数据流
UserInput --> InputProcessing : 原始输入
ConfigSource --> InputProcessing : 配置加载
ExternalData --> InputProcessing : 外部上下文

InputProcessing --> TextProcessing : 规范化请求
TextProcessing --> Vectorization : 语义表示
TextProcessing --> ES : 关键词检索
TextProcessing --> Nebula : 图谱查询
Vectorization --> Milvus : 向量检索

ES --> RecallFusion : 文本结果
Milvus --> RecallFusion : 向量结果
Nebula --> RecallFusion : 图谱结果

RecallFusion --> QualityGate : 去重/TopN
QualityGate --> AnswerAssembly : 候选文档
AnswerAssembly --> Metrics : 统计与指标
AnswerAssembly --> Logging : 输出日志

' 缓存流
InputProcessing --> Cache : 查询缓存写入
RecallFusion --> Cache : 结果缓存写入
Cache --> RecallFusion : 命中返回

' 质量与安全
InputProcessing --> DataValidation : 输入校验
DataValidation --> Security : 校验通过
Security --> TextProcessing : 安全通过

legend right
  |= 分层 |= 核心职责 |
  | 数据源层 | 输入/配置/外部数据 |
  | 数据处理层 | 清洗/语义化/融合/质量 |
  | 数据存储层 | ES/Milvus/Nebula/缓存 |
  | 数据输出层 | 答案组装/监控/日志 |
  | 数据质量与安全 | 校验/加密/访问控制 |
endlegend

@enduml


