# 召回监控系统使用说明

## 监控系统状态

✅ **监控系统已经正常工作！**

已成功记录了34次查询，数据分析结果：
- **ES贡献**: 91.7% (316次命中/487次召回)
- **KG贡献**: 8.3% (11次命中/11次召回)  
- **Milvus贡献**: 0% (代码被注释，未启用)

## 查看监控数据的方法

### 方法1: 查看文件
监控数据自动保存在 `monitor_data/` 目录下：
- `monitor_report_YYYYMMDD.md` - Markdown格式报告
- `monitor_data_YYYYMMDD.json` - JSON格式数据
- `monitor_leaderboard_YYYYMMDD.csv` - CSV格式排行榜
- `monitor_data_YYYYMMDD.txt` - 文本格式报告

### 方法2: 使用查看脚本
```bash
python view_monitor.py
```

### 方法3: 使用API端点（需要重启服务）
```bash
# 重启服务
python main.py

# 查看统计数据
curl http://127.0.0.1:10023/zte-ibo-acm-productretrieve/monitor/stats

# 查看排行榜
curl http://127.0.0.1:10023/zte-ibo-acm-productretrieve/monitor/leaderboard

# 重置监控数据
curl -X POST http://127.0.0.1:10023/zte-ibo-acm-productretrieve/monitor/reset
```

## 监控原理

1. **装饰器自动监控**: `@monitor_faq_call` 装饰器自动记录每次FAQ调用
2. **数据记录**: service层的各个deal_*方法记录实际召回数据
3. **数据持久化**: 每10次查询自动保存到文件
4. **多格式输出**: 支持MD、JSON、CSV、TXT等多种格式

## 数据分析结论

基于34次查询的监控数据：

1. **ES是主力召回源** (91.7%)
   - 几乎所有查询都依赖ES
   - 建议优化ES的索引和查询策略

2. **KG作为补充** (8.3%)
   - 在特定场景下提供精准结果
   - 虽然占比小但质量高（100%命中率）

3. **Milvus未启用** (0%)
   - 代码已被注释，建议移除或修复
   - 如需向量召回功能，应修复Milvus连接问题

## 优化建议

1. **移除Milvus代码**: 既然已经注释且不使用，建议完全移除
2. **优化ES查询**: 作为主力召回源，应重点优化
3. **增强KG覆盖**: 虽然占比小但精度高，可以扩展知识图谱

## 问题排查

如果监控数据没有更新：
1. 确认服务已重启加载最新代码
2. 检查 `logs/` 目录下的日志文件
3. 确认 `monitor/` 模块正确安装
4. 查看 `monitor_data/` 目录是否有新文件生成