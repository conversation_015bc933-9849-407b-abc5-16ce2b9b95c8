import hmac
import json
import time
from hashlib import sha256
from urllib.parse import quote
from embedding.get_embedding import Get<PERSON>mbe<PERSON>
from domain.config.zxtech_config import config
import numpy as np
import requests
from utils.logger.logger_util import logger

getembedding=GetEmbedding(config)
def create_signature(environment, access_key, access_secret):
    t = time.time()
    time_stamp = int(round(t * 1000))
    key = access_secret.encode('utf-8')
    message = access_key + environment + str(time_stamp)
    message = message.encode('utf-8')
    sign = hmac.new(key, message, digestmod=sha256).hexdigest()
    str_sign = access_key + ":" + str(time_stamp) + ":" + sign.lower()
    result = quote(str_sign)
    return result



def cosine_similarity(a, b):
    dot_product = np.dot(a, b)
    norm_a = np.linalg.norm(a)
    norm_b = np.linalg.norm(b)
    return dot_product / (norm_a * norm_b)


def sort_dict(all_dict,question):
    keys = all_dict.keys()
    query_and_res = [question]
    data = []
    for j in keys:
        query_and_res.append(j)
        data.append(j)
    embedding_Res = getembedding.post_url_m3(query_and_res)
    similarities = [cosine_similarity(embedding_Res[0], b) for b in embedding_Res[1:]]
    em_key_dict = {}
    for i in range(len(data)):
        em_key_dict[similarities[i]] = data[i]
    result_arr = []
    sort_similarities = sorted(similarities, reverse=True)
    res_similarities = sort_similarities[0:2]
    for sim in res_similarities:
        key = em_key_dict.get(sim)
        value = all_dict.get(key)
        result_arr.append(value)
    return result_arr


