import os

# Milvus Configuration
VECTOR_DIMENSION = os.getenv("VECTOR_DIMENSION", 1024)
SCORE_LIMIT = os.getenv("SCORE_LIMIT", 800)
INDEX_FILE_SIZE = os.getenv("INDEX_FILE_SIZE", 1024)
METRIC_TYPE = os.getenv("METRIC_TYPE", "COSINE")
TOP_K = os.getenv("TOP_K", 10)



# # 每日上传图片保存的根目录，每天都根据日期保存当天上传的图片
# UPLOAD_PATH = os.getenv("UPLOAD_PATH", "tmp/search-images")

# # Model Configuration
# NET_XML_PATH = os.getenv("NET_XML_PATH", "/app/src/inference_model/inference.xml")
# NET_BIN_PATH = os.getenv("NET_BIN_PATH", "/app/src/inference_model/inference.bin")

# Number of log files
LOGS_NUM = os.getenv("LOGS_NUM", 0)

# # 服务多活时，所有服务器上的milvus都需要插入数据，多个ip用逗号分隔,本机使用localhost标识
# SERVERS = os.getenv("SERVERS", "localhost,**************")

# # 批量插入每批数量
# BATCH_INSERT_SIZE = os.getenv("BATCH_INSERT_SIZE", 100000)

# MILVUS_COLLECTION_LIST = ['first_market', 'second_market', 'third_market', 'fifth_market']

# MILVUS_ADMIN = ['00242196']
