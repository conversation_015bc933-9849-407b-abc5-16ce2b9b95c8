import sys
from utils.encryption_util import decrypt
from utils.load_config import load_config_from_net, load_config
# def deal_config(config):
#     key_flag=config['key-flag']
#     llm_secret_en=config['LLM']['llm_headers']['Authorization']
#     es_secret_en=config['Knowledge']['access_secret']
#     es_access_key=config['Knowledge']['access_key']
#     config['LLM']['llm_headers']['Authorization']=decrypt(llm_secret_en,key_flag)
#     config['Knowledge']['access_secret']=decrypt(es_secret_en,key_flag)
#     config['Knowledge']['access_key'] = decrypt(es_access_key, key_flag)
#     return config
#
# config = deal_config(load_config_from_net())


config = {'MicroserviceName': '/zte-ibo-acm-productretrieve', 'BaseUrl': 'https://llm.test.zte.com.cn', 'APP_NAME': 'XboIAdkoPGr6dhU6j0vFyr1E+2sAO5WDSsqofqMjHpnncQoGh2s7EtueLkNQFcA5MAP+xEYKWkJ5/LqNCT8+e5xwMFwIfd2+1uTSJmVYdPCJTWhCE4D2TtMwxKWAEgLmKhbpccuKICd3Mlzwd42P6+L7ATSoykF417GAMkXz7whRKhpzj+OY9fOb6Lh1eEJn0Lhuw3PSFcvHuOh7ys4bd/swF0OihCQd2dduUHaKytYHyBNpiDulsdznOI1vHgNEauZ4LHPuEVzQXE8M36rpTmrIlcq8P1GrkTVlDx9D9wQv/5620U3eSDs2QsDdt0IsdLObcbItTdRRpc8HGVuM8tykauSOUHPQ', 'USER_ID': 'rT13EAE/KqhyYCXaT7M1UIGvRozZw44kixI4yeL52evg6i19Tc37M8unKlRLShUjM5HRZUeJfEfW6pasVoSzb7XBTOJ50b/qFCYkhMpu8W+jZWPH4PENDrmF/m9ahTbtC2wLpJ9FBytZAcenEnxR9n1kd/D6dBLz9wwVjW6ss7xJ1ocOBXCLnXXIBumfgMrL/8oSGOZZGq+nvDAuJS9BX0dbKd/tEgwg52TYDgH8lnjWPa5uwmXNA47EssGHMOJhHHOWQ3ebKj3XGgf73BffG3Gw3hPQDHbUd7NDaPPG4O5WlIjolnDtJU/yKutzgnvNfEdZs3TMJmyqZ9DVhJdiGWxnDZhN+Vs2fIymUw==', 'APP_SECRET': 'icOZTPLQ8XRV6v50Dv/X/pTxvgkDzpOg9ZLZgzl2Fgl6WT7CZwewWyxVh11f42za2G9jeRZXBlN6jc4qq5sX3YUrcjosb1RMABJh72jAeo8sYJmwO9plsylDqLr9wXReYA9ULMlbi97nQtNK6xUQa4hA4geOhJhXExGde951j9KsCkC75WUYn6xzGmFMkzGCuk/QIXRkp75LeQTqntuB4+yMJ7otbmFt5rxJvdohbOopcHTvloNXpjkNWx9e6QBaqo8Ds1N9VjRfSfPsjdoY6p9tbqRCSqS3PhKNaOTDuv9AZr+vOAtX59UcD9H5P/bE46USn6Br7cC2ATTCl2+CC7/oxuv4OlZfFDEGPfyfBLpaGoKv+nZ23fkcxWNq2QHt/eoUng==', 'kms_domain': 'https://kms-msp.test.zte.com.cn', 'kms_uri': '/kms-ext/v1/application/decrypt_datakey', 'secretKeyType': 'apoll:test:product_key', 'secretKeyId': '1+TmwXFhWBu60xSJe0UzdkIjdRo+87S+n4mgqa+TFdsjc2n5KF0qF1+uuV9sK+gm7tVBCovPdIvUODpejQsSZvPKzvuf0M+fT7pfjXmPcksSzL4gVHg90tH3mLditXDzM8b9SQK+mFiElnJSXreSkAAzCZwPuj7hq9Rl+JfhxppFzOJgSJriZ1XQLu/DtMnWShYPfY9gefvUiSSgBycdKg2Jt4VBCmdqHOABI2jEfSly1Kj6H9ANv8LY5AXIvoOCeDY3hEA1znPkuP2HEthvPJV2oyqm77HelcH4Rpxutn0T2A1nKDudFnCNkEHBnHPRGQZBJXfi6dfmMNCVsOlCPlMJ2cekac2JKsjYiiLg26LS6al+ccuEG6zbWICBYVCcui5bUw==', 'dataKeyCipher': 'YzQyODY3MWViNWNiNGU1ZWQxZDdjZjM5N2FkOGZlMTA2NmNhOTNmMWZlZDRmYmYxMjJhNWU2NzRmZTk0ODE2ZTRmNzY5NTIwYzQ2MjBiNjM0YmRhZGY2NzkxZGNjOTBi$$ODJiYzA5YjNjYzkyNmY5MjQyMzQyOTBmMGJmOGYxMjE=', 'key-flag': 'b1HA9ddPIIo8W2v8YlwsynvZZijYrOdSUjy0Mn0GjyE=', 'Embedding': {'url': 'https://llm.dev.zte.com.cn/zte-igpt-ellm-vector/vector', 'name': 'Bge_M3'}, 'Milvus': {'collection_name': 'productQA_1118', 'MILVUS_HOST': '**************', 'MILVUS_PORT': 19530}, 'rerank': {'url': 'https://llm.dev.zte.com.cn/zte-igpt-ellm-reranker/rerank', 'Reranker_tfidf': {'threshold_origin': 0.7, 'threshold_tfidf': 0.23, 'threshold_tfidf_binary': 0.23, 'threshold_origin_max': 0.98, 'threshold_origin_min': 0.1, 'threshold_tfidf_max': 0.65, 'threshold_tfidf_binary_max': 0.65, 'threshold_origin_advantage': 0.997}}, 'LLM': {'url': 'https://studio.zte.com.cn/zte-studio-ai-platform/openapi/v1/chat', 'name': 'NebulaBiz', 'temperature': '0', 'top_p': 0.85, 'top_k': 5, 'llm_headers': {'Content-Type': 'application/json', 'Authorization': 'Bearer 75a51e9169d0484aac31271a515c6e0a-e256ed047d71457b82a376996634ff17', 'X-Emp-No': '10345316'}, 'rewrite_model_name': 'intention-recognition', 'rewrite_model_type': 'openai', 'rewrite_model_url': 'https://llm.dev.zte.com.cn/zte-igpt-ellm-llmrewrite/v1/chat/completions'}, 'Relate': {'simwords_url': 'https://llm.dev.zte.com.cn/zte-dmt-dmp-judgechat/multy'}, 'catch_body': {'entity_es_index': 'kg_product_dn20240327'}, 'kg': {'space': 'product_multiversion', 'kg_url': 'https://kger.zte.com.cn/zte-dmt-dmp-kgbff/zte-dmt-dmp-userprofile-bff-new/nebula/executeNql'}, 'Knowledge': {'es_url': 'https://kger.zte.com.cn/zte-dmt-dmp-kgbff/zte-dmt-dmp-userprofile-bff-new/es/commonSearch', 'index': 'product_1206', 'index_xbk': 'product_1206', 'ner_index': 'product_infogo_entitylink', 'doc_file_info_index': 'product_doc_info_0912', 'user': 10265786, 'type1': ['content'], 'es_headers': {'Content-Type': 'application/json', 'User': '10265786', 'systemName': 'kgnlp'}, 'access_environment': 'prod', 'access_key': '5b33d571ae464910822a2bbf167e7f6e', 'access_secret': '36ba38e1d3f58f8b3c2d25d48a00b19b96f95e65dbe7f9b3f4ef921543578200'}, 'Parameters': {'address': '0.0.0.0', 'port': 10023}, 'Enviroment': 'dev', 'log': {'level': 'INFO', 'path': './resource/logs/mylog.log', 'dir': './resource/logs'}, 'kafka': {'broker': ['************:9092', '************:9092', '************:9092'], 'topic': 'acm-bk-app-nj'}, 'identify': {'intercept': ['/zte-ibo-acm-productretrieve/**'], 'enable': True, 'exclude': ['/zte-ibo-acm-productretrieve/info'], 'uac': {'secret': 'ER5LKw3L8q65zuLI0xcaHPtDMMTfDPALpatdMwvg4d4/tb5kGtEvokxS1+opacpXdg5s/G8mE9lBWKWK48iNeGogA6lU8XWUBfZ2C8sJ1s/wn0gNxi7cBUegyGfw0NmOBjyz5q/CocMycpof0IutH9mbZH9JRULUf5tGpkhD9uSkzim7+s0vLJm3y7wxnBpGBiD1sOXFB9ByAYXtWMshsvqXoOT4zYrwUNyIcaxjTfX0UoEfm/QjTW1NsFEJiNfZcuy5H0L8ETKs67GhL23VJ/t753vLGBVqS35BeKQvwbZfKKhmU27dqRHtBDfAg0foLawNHueyDFdRq6RwxzbeXQeGCX7DFx+kL3zfIa9nbZw=', 'secret-key': 'fpCehD17t2oVTS254PTQPK2k++H5xkfFANRQjK5/dg4xklvj1G5P5unW01r7qFCXtcG5bbcFqxibDnbHP221AqWISwZJ4clXM0JpiPZzzZrSYukMw/9cP5ClW0r+/lJRLjHLBLzKHaSA8Bp363JKuG+isL6U5DG4X1BzDd0fVF2tupECMzl+0/RxftmbKYDZz/qe/ARqFBYVOBkhGoRdGmNO0NtwwFLe3Bi1EMN5OFMsUoR5bVv9AvrqYz8aDwdmuWP+dGcI5p5umUFG6G7pxBQDk5eaircwwu0+tRlEIHIAYngDs9C5wIi9jQAx5TqHZBL0Ns3bTEhJNTNesWCcscqg0pS7bu6Sk63se3puwNr6Ffi4SyPepgkPpJldfXzJZyVioAp4o+9Lzw2hyrFPFndWwpwXtrernshitjDj4gc=', 'app-id': '612791211526', 'access-key': 'Tl1zN2Rn9xb0bSUOlNdTiNzASWfHlnkG0Gz4UqlbtbZQMbXb0Ssntr5f1vY4us4n3WOijf/FNuhWiREKlHAzLwKfc9XGhX3/hGmJj5o+5ZOmgyqAO3+y+jYTpyu754O0KFMJ3/DebucSRxLh9j90YVnGC7eXP2u9PuNoPvu8TIVSDoUBhoFcQWOU51vIF0Eb/CXoCfT7vBXex/DuCW30g6iMSXhfzyUZI545N1slVI7aKfv+78DCkLBbNzLaT9EVbwRyzg2+hzcfvJwH88s7yPx6O2DkfzlrHlac3vmJaSR2afAGIcNg+UcpKeTtOnGTiHUdF9eiBiCx8y/Z99wnPzNnZ9NCDpHJnspiI2wz0YoLMne/PkG10KTVuTwNosdt', 'verify-url': 'https://uactest.zte.com.cn:5555/zte-sec-uac-iportalbff/external/auth/token/v2/verify.serv', 'itp-value': 'jFxDNBMtBVqGNVoOyh72vejSGsfClD2YsXF1OTvXcDZg9pX5fz+op3+Kl5qgOMsChwh44LsmRQTek+On1ytAG8KBGtNF/c/nwpJ8lJmeY1sP1jYqL8VBlgiTipOrfJezux1mQ81ufB2oYmS7tm5qHPAwqLsE1eGXKsMu4SuIcQEJqvizjk5xoq+wmB88qdfRv8jN/9WxJpSnPMIPKiuDohiFHIUwBPBzDWkkfSvmSAq0fRaG8na8dKZtWc7hMTlNgcqKRpZFCsW2f5Su8a4o2/RBy7FPHLHvyvcwZyTAUIXIhsrEPTBGy/2wjXZxVmhQ3EAr1JBZ5CS0cvTmRFlmO6HBKbPj4BvB31obPE5UzDyK/FiAPitUTLA1jARRNMiVabDI0oCoDHfERw==', 'encoding-utf8': 'utf-8', 'tenant-id': '10001', 'system-code': 'UAC', 'client-ip': '127.0.0.1'}}}
