import time
from hashlib import sha256
import hmac
from urllib.parse import quote


# 生成generateSignature的函数
def create_signature(environment, access_key, access_secret):
    t = time.time()
    time_stamp = int(round(t * 1000))
    key = access_secret.encode('utf-8')
    message = access_key + environment + str(time_stamp)
    message = message.encode('utf-8')
    sign = hmac.new(key, message, digestmod=sha256).hexdigest()
    str_sign = access_key + ":" + str(time_stamp) + ":" + sign.lower()
    result = quote(str_sign)
    return result


