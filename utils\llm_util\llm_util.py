import copy

import requests

from domain.constants.Enums import StringType_Response, StringType, Hints
from domain.constants.Enums import SOURCE
from prompt.prompt_instance.flag_prompt import zxtech_with_exclusive_self_prompt
from utils.stream_entity import SSEClient
import json
from utils.logger.logger_util import logger
from utils.logger.log_wrapper import wrap_log_content
from typing import Dict, Any
from rerank.bge_m3_reranker_v2 import check_similarity_tf_intersection
from retrieve.Recall_data import RecallR<PERSON>ult
from domain.config.zxtech_config import config
import re

es_recall = RecallResult(config)


def judge_query_lang(query_lang):
    if query_lang == StringType.CH.value:
        userp = StringType_Response.Chinese.value
    elif query_lang == StringType.EN.value:
        userp = StringType_Response.English.value
    else:
        userp = StringType_Response.Other.value
    return userp


# Started by AICoder, pid:h6807w5506qf1b5149f90b57d004c414fab1ead5


def replace_chinese_brackets(text):
    # 合并替换左括号和右括号的操作
    text = re.sub(r'（', '(', text)
    text = re.sub(r'）', ')', text)

    # 在特定情况下添加等号
    text = re.sub(r'∑', '=∑', text)

    return text

# Ended by AICoder, pid:h6807w5506qf1b5149f90b57d004c414fab1ead5


def getUrl_from_infoGo(kg_parts, super_url_list):
    for i in kg_parts:
        temp_kg = eval(i)
        if SOURCE.FROM.value not in list(temp_kg.keys()) and SOURCE.YXXBK_FROM.value not in list(temp_kg.keys()):
            continue
        elif temp_kg.get(SOURCE.PRODUCT_NUMBER.value):
            super_link = '[InfoGo：  ' + temp_kg[SOURCE.PRODUCT_NUMBER.value] + '](' + temp_kg[
                SOURCE.FROM.value].replace('\uFEFF', '') + ')'
        elif temp_kg.get(SOURCE.QUESTION.value):
            super_link = '[InfoGo：  ' + temp_kg[SOURCE.QUESTION.value] + '](' + temp_kg[
                SOURCE.YXXBK_FROM.value].replace('\uFEFF', '') + ')'
        else:
            if temp_kg.get(SOURCE.YXXBK_FROM.value):
                super_link = '[InfoGo：  ' + temp_kg[SOURCE.YXXBK_FROM.value].replace('\uFEFF', '') + '](' + temp_kg[
                    SOURCE.YXXBK_FROM.value].replace('\uFEFF', '') + ')'
            else:
                super_link = '[InfoGo：  ' + temp_kg[SOURCE.FROM.value].replace('\uFEFF', '') + '](' + temp_kg[
                    SOURCE.FROM.value].replace('\uFEFF', '') + ')'
        super_url_list.append(super_link)
    return super_url_list


# Started by AICoder, pid:zc82aea8cd07ade145470a42c070b71bd2735e69
def getId_from_list(ids_final):
    newid_final = []
    for i in ids_final:
        # 跳过'_infoGo_'
        if i == '_infoGo_':
            continue
        # 如果以'yyxbk'开头，去掉最后的'_数字'部分
        elif i.startswith('yyxbk'):
            newid_final.append(i.rsplit("_", 1)[0])
        # 否则去掉前两个'_'之间的部分
        else:
            newid_final.append(i.split("_", 2)[0])
    return newid_final


# Ended by AICoder, pid:zc82aea8cd07ade145470a42c070b71bd2735e69


def getkg_candidate_from_candidates(candidates):
    candidates_kg = ''
    for i in candidates:
        if str(i).startswith('{') and str(i).endswith('}'):
            candidates_kg = i
    return candidates_kg


# Started by AICoder, pid:vc70ahdbd34aea114ffb0a1860835c3436322ad9


# Configure logger


def deal_kg_res_length(kg_res):
    """
    处理知识图谱响应长度，确保内容不超过8000个字符。

    :param kg_res: 知识图谱响应列表
    :return: 处理后的知识图谱内容列表
    """
    if not kg_res:
        return []

    # 提取知识图谱的部分
    kg_parts = re.findall(r'(\{.*?\})(?=\{|\Z)', kg_res[0])

    while len(str(kg_parts)) > 8000 and len(kg_parts) > 1:
        kg_parts.pop()

    # 检查单个部分是否超过8000个字符
    if len(kg_parts) == 1 and len(str(kg_parts[0])) > 8000:
        logger.info("kg有召回内容，但是长度超过阈值8000")
        return []

    # 合并所有部分
    content = ''.join(map(str, kg_parts))

    return [content]

# Ended by AICoder, pid:vc70ahdbd34aea114ffb0a1860835c3436322ad9


def generate_similarity_report(logger_res: str, kg_dict: map, chunk_id_map: map, g: Any):
    """计算相似性报告，更新source_name_score。"""
    # 调用check_similarity函数计算相似度和分数
    all_source_score = []
    chunk_source_score = {}
    super_url_list = []

    if chunk_id_map:
        get_chunk_similarity(logger_res, chunk_id_map, g, chunk_source_score)

    if kg_dict:
        get_kg_similarity(logger_res, kg_dict, g, all_source_score)

    for key, value in chunk_source_score.items():
        all_source_score.append([key, value])

    sort_all_source(all_source_score, super_url_list)

    # 去重
    seen = set()
    unique_list = []
    for x in super_url_list:
        if x not in seen:
            seen.add(x)
            unique_list.append(x)
    urls_str = '\n\n'.join(unique_list)

    return urls_str


def get_chunk_similarity(logger_res: str, chunk_id_map: map, g: Any, chunk_source_score):
    chunk_similarities, chunk_scores = check_similarity_tf_intersection(logger_res, list(chunk_id_map), g=g,
                                                                        flag=True)
    logger.info(wrap_log_content(f'[similarity]{chunk_similarities} [scores]{chunk_scores}', g))
    chunk_value_list = list(chunk_id_map.values())
    for i, sim in enumerate(chunk_similarities):
        if sim:
            name = chunk_value_list[i]
            score = chunk_scores[i]
            # 如果source_name不在字典中或者分数大于当前分数，则更新字典
            if name not in chunk_source_score or score > chunk_source_score[name]:
                chunk_source_score[name] = score


def get_kg_similarity(logger_res: str, kg_dict: map, g: Any, all_source_score):
    list_candidates = []
    for key, value in kg_dict.items():
        if SOURCE.YXXBK_FROM.value in value and SOURCE.YXXBK_ANSWER.value in value:
            list_candidates.append(value[SOURCE.YXXBK_ANSWER.value])
        else:
            list_candidates.append(key)
    kg_similarities, kg_scores = check_similarity_tf_intersection(logger_res, list_candidates, g=g)
    kg_value_list = list(kg_dict.values())
    for i, sim in enumerate(kg_similarities):
        if sim:
            name = kg_value_list[i]
            score = kg_scores[i]
            all_source_score.append([name, score])


def sort_all_source(all_source_score, super_url_list):
    sorted_data = sorted(all_source_score, key=lambda x: x[1], reverse=True)
    for element in sorted_data:
        search_text = element[0]
        temp_list = []
        if isinstance(search_text, dict):
            temp_list.append(str(search_text))
            super_url_list = getUrl_from_infoGo(temp_list, super_url_list)
        else:
            temp_list.append(search_text)
            candidates_url_match = es_recall.query_must_match(temp_list)
            for i in candidates_url_match:
                super_link = '[TSM：  ' + i['file_name'] + '](' + i['url'] + ')'
                super_url_list.append(super_link)


def llm_flag_result(rerank_res_doc,doc_res,query_lang,kg_res, question):
    LLM_config = copy.deepcopy(config['LLM'])

    if doc_res != Hints.NOT_EXIST.value:
        doc_res = [i['documentID'] for i in next(iter(doc_res.values()))]
    # 候选文本
    candidates = [i['content'] for i in rerank_res_doc]
    res = [i['id'] for i in rerank_res_doc]
    # 候选文本对应的文档
    doc = [i['doc_name'] for i in rerank_res_doc]
    if len(candidates) == 0:
        return Hints.NO_CONTENT.value
    candidates = list(candidates)
    s_candidates = str(candidates)
    last_candidate=''
    while len(s_candidates) > 8000 and len(candidates) > 1:
        last_candidate=candidates.pop()
        s_candidates = str(candidates)
    if len(candidates) == 1:
        candidates = [candidates[0][:8000]]
    candidates.append(last_candidate[:8000])
    candidates = candidates[:6]
    doc_effective=doc[:len(candidates)]
    res_effective=res[:len(candidates)]
    kg_res = deal_kg_res_length(kg_res)
    if kg_res:
        doc_effective.insert(0, '_infoGo_')
        res_effective.insert(0, '_infoGo_')
    candidates = kg_res + candidates
    candidates.reverse()
    doc_effective.reverse()
    res_effective.reverse()
    user_content = ""
    candidates_log = ''
    candidates_log += '\n' + "****" + "\n"
    candidates2=[]
    for i, c in enumerate(candidates):
        c_new = re.sub(r'([*])', r' * ', c)
        user_content += "***"+"\n"+str(i + 1) + "." + c_new+"\n"+ "***"+"\n"
        candidates2.append(str(i + 1) + "." + c_new+"\n"+SOURCE.DOC_FROM.value+doc_effective[i]+ "\n"+ SOURCE.DOC_ID.value + res_effective[i]+"\n")
    candidates_log += user_content
    candidates_log += "****" + "\n"

    prompt_dict = zxtech_with_exclusive_self_prompt(user_content, question)
    sys_prompt = prompt_dict['sys_prompt']
    text = prompt_dict['user_prompt']
    bo = {
        "text": text,
        "chatUuid": "",
        "chatName": "",
        "model": config['LLM']['name'],
        "temperature": config['LLM']['temperature'],
        "top_k": config['LLM']['top_k'],
        "top_p": config['LLM']['top_p'],
        "stream": False,
        "keep": True,
        "messages": [
            {
                "role": "system",
                "content": sys_prompt
            }
        ]
    }
    headers = LLM_config['llm_headers']
    try:
        receive = requests.post(
            headers=headers,
            json=bo,
            url=config['LLM']['url'],
            timeout=60,
            verify=True
        )
        result = receive.json()['bo']['result']
        if Hints.FOU_CN.value in result:
            return False

        return True
    except Exception as e:
        logger.info(e)


def stream_generate(Stream_Generate_Params):
    kg_dict = Stream_Generate_Params.kg_dict
    chunk_id_map = Stream_Generate_Params.chunk_id_map
    receive = Stream_Generate_Params.receive
    body = Stream_Generate_Params.body
    doc_res = Stream_Generate_Params.doc_res
    es_entity = Stream_Generate_Params.es_entity
    es_res_doc = Stream_Generate_Params.es_res_doc
    milvus_res_doc = Stream_Generate_Params.milvus_res_doc
    kg_res = Stream_Generate_Params.kg_res
    rerank_res_doc = Stream_Generate_Params.rerank_res_doc
    candidates2 = Stream_Generate_Params.candidates2
    urls_str = Stream_Generate_Params.urls_str
    env = Stream_Generate_Params.env
    client = SSEClient(receive)
    events = client.events()
    llm_response = ''
    for i, event in enumerate(events):
        if i == 0:
            if env != 'prod':
                yield ('data:' + str(
                    [body, doc_res, es_entity, es_res_doc, milvus_res_doc, kg_res, rerank_res_doc,
                     candidates2]) + '\n\n')
            continue
        try:
            dict_res = json.loads(event.data.encode('utf-8'))
            if dict_res.get('finishReason'):
                new_url = generate_similarity_report(llm_response, kg_dict, chunk_id_map, None)
                document_info = ''
                document_info += '\n' + '####' + '\n' + new_url + '\n' + '####' + '\n'
                logger.info(f"[溯源文档：]{document_info}")
                dict_content = {
                    "finishReason": "stop",
                    "result": ""
                }
                content = json.dumps(dict_content, ensure_ascii=False)
                dict_content1 = {
                    # "chatUuid": chatUuid,
                    "finishReason": "",
                    "result": f"\n\n{SOURCE.REF_DOCUMENT.value}:\n\n{new_url}"
                    # "result":""
                }
                yield ('data: ' + json.dumps(dict_content1, ensure_ascii=False) + '\n\n')
                yield ('data: ' + content + '\n\n')

                break
        except:

            yield ('data: [DONE]')
            continue
        dict_content = dict_res
        content = json.dumps(dict_content, ensure_ascii=False)
        llm_response += dict_content['result']
        yield ('data: ' + content + '\n\n')
    logger.info(f"[大模型回答：]{llm_response}")
