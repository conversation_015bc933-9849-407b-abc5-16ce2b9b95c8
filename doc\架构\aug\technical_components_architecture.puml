@startuml ZTE Product Retrieval Technical Components Architecture
' 不使用 plain 主题，避免中文乱码
' !theme plain

!define FONTNAME "Microsoft YaHei"
!define FONTSIZE 12

skinparam defaultFontName FONTNAME
skinparam defaultFontSize FONTSIZE
skinparam monospace FONTNAME

skinparam backgroundColor #FFFFFF
skinparam componentStyle rectangle
skinparam packageStyle rectangle

title **ZTE Product Retrieval Q&A System 555 - Technical Components Architecture**\\n**ZTE产品检索问答系统 - 技术组件详细架构图**

' 定义样式
skinparam component {
    BackgroundColor<<framework>> #E3F2FD
    BackgroundColor<<ai_model>> #E8F5E8
    BackgroundColor<<database>> #FFF3E0
    BackgroundColor<<service>> #F3E5F5
    BackgroundColor<<config>> #F1F8E9
    BackgroundColor<<external>> #FFEBEE
}

' 应用框架层
package "应用框架层" as FrameworkLayer {
    [Flask 2.x] as Flask <<framework>>
    [Flask-CORS] as FlaskCORS <<framework>>
    [Blueprint路由] as Blueprint <<framework>>
    [Werkzeug WSGI] as Werkzeug <<framework>>
}

' 业务组件层
package "业务组件层" as BusinessLayer {
    component "控制器组件" as ControllerComponents {
        [zxtech_controller.py] as Controller
        [参数验证器] as ParamValidator
        [响应包装器] as ResponseWrapper
    }
    
    component "服务组件" as ServiceComponents {
        [_ZXTECH_核心服务] as CoreService
        [文本预处理服务] as TextProcessor
        [多轮对话服务] as DialogService
        [语言检测服务] as LanguageService
    }
}

' AI/ML模型层
package "AI/ML模型层" as AIModelLayer {
    component "向量化模型" as EmbeddingComponents <<ai_model>> {
        [BGE-M3 Embedding] as BGEM3Embedding
        [GetEmbedding服务] as EmbeddingService
        [1024维向量生成] as VectorGeneration
    }
    
    component "实体识别模型" as EntityComponents <<ai_model>> {
        [dp_entity实体提取] as EntityExtractor
        [产品型号识别] as ProductRecognition
        [系列识别] as SeriesRecognition
    }
    
    component "重排序模型" as RerankComponents <<ai_model>> {
        [BGE-M3 Reranker V2] as BGEM3Reranker
        [相关性评分] as RelevanceScoring
        [结果去重] as Deduplication
    }
    
    component "语言模型" as LLMComponents <<ai_model>> {
        [NebulaBiz LLM] as NebulaBizLLM
        [意图识别模型] as IntentionRecognition
        [流式生成] as StreamGeneration
    }
}

' 检索引擎层
package "检索引擎层" as RetrievalLayer {
    component "ES检索引擎" as ESComponents <<database>> {
        [RecallResult ES检索] as ESRecall
        [精确匹配] as ExactMatch
        [模糊匹配] as FuzzyMatch
        [全文检索] as FullTextSearch
    }
    
    component "向量检索引擎" as VectorComponents <<database>> {
        [RecallMilvus向量检索] as MilvusRecall
        [余弦相似度计算] as CosineSimilarity
        [Top-K检索] as TopKRetrieval
    }
    
    component "知识图谱引擎" as KGComponents <<database>> {
        [RecallResult_kg图谱检索] as KGRecall
        [产品关系查询] as ProductRelationQuery
        [文档关系查询] as DocumentRelationQuery
    }
}

' 数据存储层
package "数据存储层" as DataStorageLayer {
    database "ElasticSearch存储" as ESStorage <<database>> {
        [product_1206] as ProductIndex
        [kg_product_dn20240327] as EntityIndex
        [product_infogo_entitylink] as EntityLinkIndex
        [product_doc_info_0912] as DocInfoIndex
    }
    
    database "Milvus向量存储" as MilvusStorage <<database>> {
        [productQA_1118集合] as VectorCollection
        [向量索引] as VectorIndex
        [元数据存储] as MetadataStorage
    }
    
    database "知识图谱存储" as KGStorage <<database>> {
        [product_multiversion空间] as KGSpace
        [产品实体] as ProductEntities
        [关系图谱] as RelationGraph
    }
}

' 外部服务层
package "外部服务层" as ExternalServiceLayer {
    [LLM API服务\nstudio.zte.com.cn] as ExternalLLMAPI <<external>>
    [向量化API服务\nllm.dev.zte.com.cn] as ExternalEmbeddingAPI <<external>>
    [重排序API服务\nllm.dev.zte.com.cn] as ExternalRerankAPI <<external>>
    [知识图谱API服务\nkger.zte.com.cn] as ExternalKGAPI <<external>>
    [UAC认证服务\nuactest.zte.com.cn] as ExternalUACAPI <<external>>
}

' 配置管理层
package "配置管理层" as ConfigLayer {
    [zxtech_config.py] as MainConfig <<config>>
    [Apollo配置中心] as ApolloConfig <<config>>
    [环境配置] as EnvConfig <<config>>
    [加密配置] as EncryptionConfig <<config>>
}

' 基础设施层
package "基础设施层" as InfrastructureLayer {
    [日志系统] as LoggingSystem <<service>>
    [监控系统] as MonitoringSystem <<service>>
    [安全认证] as SecuritySystem <<service>>
    [流式响应] as StreamingSystem <<service>>
}

' 工具层
package "工具层" as UtilityLayer {
    [HTTP工具] as HTTPUtil
    [加密工具] as EncryptionUtil
    [字符串工具] as StringUtil
    [实体工具] as EntityUtil
    [日志工具] as LogUtil
}

' 连接关系定义
Flask --> Blueprint
Blueprint --> Controller
Controller --> CoreService

CoreService --> TextProcessor
CoreService --> DialogService
CoreService --> LanguageService

CoreService --> EmbeddingService
CoreService --> EntityExtractor
CoreService --> BGEM3Reranker
CoreService --> NebulaBizLLM

CoreService --> ESRecall
CoreService --> MilvusRecall
CoreService --> KGRecall

ESRecall --> ESStorage
MilvusRecall --> MilvusStorage
KGRecall --> KGStorage

EmbeddingService --> ExternalEmbeddingAPI
BGEM3Reranker --> ExternalRerankAPI
NebulaBizLLM --> ExternalLLMAPI
KGRecall --> ExternalKGAPI

CoreService --> MainConfig
MainConfig --> ApolloConfig

CoreService --> LoggingSystem
CoreService --> SecuritySystem
CoreService --> StreamingSystem

' 数据流注释
note top of CoreService
  核心业务逻辑:
  1. 请求预处理
  2. 实体提取与识别
  3. 多路检索召回
  4. 结果融合与重排序
  5. LLM生成与流式输出
end note

note top of ESStorage
  ES索引结构:
  - product_1206: 主要产品文档
  - kg_product_dn20240327: 产品实体
  - product_infogo_entitylink: 实体链接
  - product_doc_info_0912: 文档信息
end note

note top of MilvusStorage
  Milvus配置:
  - 集合: productQA_1118
  - 向量维度: 1024 (BGE-M3)
  - 相似度度量: 余弦相似度
  - 主机: 10.136.152.152:19530
end note

note top of KGStorage
  知识图谱结构222:
  - 空间: product_multiversion
  - 实体: 产品、系列、文档
  - 关系: 产品-文档、产品-系列
  - 版本: 多版本支持
end note

@enduml
