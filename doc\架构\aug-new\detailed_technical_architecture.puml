@startuml ZTE产品检索系统技术架构图
!theme plain
title ZTE产品检索系统 - 详细技术架构图

skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 11
skinparam componentStyle rectangle

' 表示层
package "**表示层 (Presentation Layer)**" #E3F2FD {
    component "Web接口" as WebInterface #BBDEFB {
        [HTTP API接口]
        [RESTful服务]
        [CORS跨域支持]
        [流式响应(SSE)]
    }
    
    component "路由管理" as RouteManagement #BBDEFB {
        [Flask Blueprint]
        [URL路由映射]
        [请求参数验证]
        [响应格式化]
    }
}

' 网关层
package "**网关层 (Gateway Layer)**" #FFF3E0 {
    component "认证网关" as AuthGateway #FFE0B2 {
        [UAC Token验证]
        [用户身份识别]
        [权限检查]
        [安全过滤]
    }
    
    component "请求处理" as RequestProcessor #FFE0B2 {
        [参数解析]
        [请求日志记录]
        [异常处理]
        [响应包装]
    }
}

' 业务逻辑层
package "**业务逻辑层 (Business Logic Layer)**" #E8F5E8 {
    component "核心服务编排" as ServiceOrchestration #C8E6C9 {
        [_ZXTECH_主服务]
        [业务流程控制]
        [策略路由选择]
        [结果组装]
    }
    
    component "智能处理服务" as IntelligentServices #C8E6C9 {
        [查询重写(rewrite_model)]
        [多轮对话管理]
        [意图理解]
        [上下文管理]
        [语言类型判断]
    }
    
    component "实体处理服务" as EntityServices #C8E6C9 {
        [实体识别(dp_entity)]
        [产品实体提取]
        [系列实体映射]
        [实体关系分析]
    }
}

' AI模型层
package "**AI模型层 (AI Model Layer)**" #F3E5F5 {
    component "嵌入模型服务" as EmbeddingServices #E1BEE7 {
        [BGE-M3 Embedder]
        [文本向量化]
        [多语言支持]
        [向量维度:1024]
    }
    
    component "重排序服务" as RerankingServices #E1BEE7 {
        [BGE-M3 Reranker]
        [相似度计算]
        [结果重排序]
        [阈值过滤]
    }
    
    component "生成模型服务" as GenerationServices #E1BEE7 {
        [NebulaBiz LLM]
        [答案生成]
        [流式输出]
        [提示词管理]
    }
}

' 检索层
package "**检索层 (Retrieval Layer)**" #FFF8E1 {
    component "多路召回引擎" as MultiRecallEngine #FFECB3 {
        [ES文本检索]
        [Milvus向量检索]
        [KG图谱检索]
        [召回策略选择]
    }
    
    component "结果融合处理" as ResultFusion #FFECB3 {
        [多源结果合并]
        [去重过滤]
        [相关性排序]
        [质量评估]
    }
    
    component "检索优化" as RetrievalOptimization #FFECB3 {
        [查询扩展]
        [精确+模糊匹配]
        [索引优化]
        [缓存策略]
    }
}

' 数据访问层
package "**数据访问层 (Data Access Layer)**" #E0F2F1 {
    component "数据连接器" as DataConnectors #B2DFDB {
        [ES连接器(es_util)]
        [Milvus连接器(milvus_util)]
        [HTTP客户端(http_util)]
        [连接池管理]
    }
    
    component "外部API客户端" as ExternalAPIClients #B2DFDB {
        [LLM API客户端]
        [Embedding API客户端]
        [Rerank API客户端]
        [KG API客户端]
    }
}

' 配置管理层
package "**配置管理层 (Configuration Layer)**" #FCE4EC {
    component "配置中心" as ConfigCenter #F8BBD9 {
        [Apollo配置中心]
        [配置热更新]
        [环境隔离]
        [配置加密]
    }
    
    component "本地配置" as LocalConfig #F8BBD9 {
        [zxtech_config.py]
        [环境变量管理]
        [配置文件加载]
        [默认配置]
    }
}

' 基础设施层
package "**基础设施层 (Infrastructure Layer)**" #F5F5F5 {
    component "日志系统" as LoggingSystem #E0E0E0 {
        [结构化日志]
        [日志分级]
        [日志轮转]
        [性能监控]
    }
    
    component "监控系统" as MonitoringSystem #E0E0E0 {
        [召回监控]
        [性能统计]
        [异常告警]
        [数据分析]
    }
    
    component "工具库" as UtilityLibrary #E0E0E0 {
        [加密解密工具]
        [文本处理工具]
        [相似度计算]
        [签名生成]
    }
}

' 数据存储层
package "**数据存储层 (Data Storage Layer)**" #E8EAF6 {
    database "ElasticSearch" as ESStorage #C5CAE9 {
        [产品文档索引]
        [全文检索]
        [多字段搜索]
        [聚合分析]
    }
    
    database "Milvus向量库" as MilvusStorage #C5CAE9 {
        [向量存储]
        [相似度检索]
        [索引管理]
        [分区策略]
    }
    
    database "Nebula图数据库" as NebulaStorage #C5CAE9 {
        [知识图谱]
        [实体关系]
        [图查询]
        [路径分析]
    }
    
    storage "本地存储" as LocalStorage #C5CAE9 {
        [日志文件]
        [配置缓存]
        [监控数据]
        [临时文件]
    }
}

' 外部服务
cloud "**外部服务**" as ExternalServices {
    [UAC认证服务\nuactest.zte.com.cn] as UACService
    [Apollo配置中心] as ApolloService
    [NebulaBiz LLM\nstudio.zte.com.cn] as LLMService
    [BGE-M3服务\nllm.dev.zte.com.cn] as BGEService
    [知识图谱API\nkger.zte.com.cn] as KGService
}

' 主要数据流连接
WebInterface --> RouteManagement : HTTP请求
RouteManagement --> AuthGateway : 路由转发
AuthGateway --> RequestProcessor : 认证通过
RequestProcessor --> ServiceOrchestration : 业务调用

ServiceOrchestration --> IntelligentServices : 智能处理
ServiceOrchestration --> EntityServices : 实体处理
ServiceOrchestration --> MultiRecallEngine : 检索调用

IntelligentServices --> EmbeddingServices : 向量化
IntelligentServices --> GenerationServices : LLM调用
MultiRecallEngine --> ResultFusion : 结果处理
ResultFusion --> RerankingServices : 重排序

MultiRecallEngine --> DataConnectors : 数据查询
EmbeddingServices --> ExternalAPIClients : 外部API
RerankingServices --> ExternalAPIClients : 外部API
GenerationServices --> ExternalAPIClients : 外部API

DataConnectors --> ESStorage : ES查询
DataConnectors --> MilvusStorage : 向量检索
DataConnectors --> NebulaStorage : 图谱查询

ServiceOrchestration --> ConfigCenter : 配置获取
ServiceOrchestration --> LoggingSystem : 日志记录
ServiceOrchestration --> MonitoringSystem : 监控数据

' 外部服务连接
AuthGateway --> UACService : 认证验证
ConfigCenter --> ApolloService : 配置同步
ExternalAPIClients --> LLMService : LLM调用
ExternalAPIClients --> BGEService : 模型调用
ExternalAPIClients --> KGService : 图谱查询

' 基础设施连接
LoggingSystem --> LocalStorage : 日志存储
MonitoringSystem --> LocalStorage : 监控数据
UtilityLibrary --> ServiceOrchestration : 工具支持

@enduml
