# !/usr/bin/env python
# -*- coding: UTF-8 -*-

del_faq_index = {
    "财务": [],
    "行政服务": [],
    "人力资源": [],
    "DT产品及服务": [],
    "合规": [{
        # 合规FAQEAR法条
        "id": "0974634bb45a4969ae11ff36559a7763",
        # 008合规
        "kbaseId": "490166c589bf41df8dda2751b7489f33"
    }],
    "党务": [],
    "档案": [{
        # 档案FAQ
        "id": "5e20510c02884fada5cb8639777c95a8",
        # 010档案
        "kbaseId": "6ac81fb90ab24488ac1ee009f4894f70"
    }],
    "FAQ补充信息": [],
    "中兴通识": [{
        # 中兴通识类FAQ
        "id": "79f4a30582ed41e99941334039a20c25",
        # 017中兴通识类
        "kbaseId": "12824751384f485d8ea5a3f6c25303fd"
    }]
}

# 测试环境-向量库生产环境
faq_index = {
    "财务": [{
        # 财务FAQ
        "id": "966dc545ed5c4234b7b299c37e45f3a9",
        # 001财务
        "kbaseId": "05160ce400b541c79e831aa598905c7e"
    }],
    "行政服务": [{
        # 行政FAQ
        "id": "71e731c36be745789d4f8d8ac6330adc",
        # 002行政服务
        "kbaseId": "1354db4d980a400ebd3ada25885d51cf"
    }],
    "人力资源": [{
        # 人力资源FAQ
        "id": "c7338ea6879b411aae5d565eb7c63381",
        # 003人力资源
        "kbaseId": "49494fe74a3a435892ecf9c265c07b20"
    }],
    "DT产品及服务": [
        {
            # 新员工入职
            "id": "10140a33b9484877acd55e332660bbeb",
            # 007DT产品及服务
            "kbaseId": "f601545cfdbb432aa93ff2fd28c71278"
        }, {
            # 测试目录
            "id": "188b6f5201164123b562b484baa1a647",
            # 007DT产品及服务
            "kbaseId": "f601545cfdbb432aa93ff2fd28c71278"
        }],
    "合规": [{
        # 合规FAQ
        "id": "0974634bb45a4969ae11ff36559a7763",
        # 008合规
        "kbaseId": "490166c589bf41df8dda2751b7489f33"
    }],
    "党务": [{
        # 党务FAQ
        "id": "92ba9d37e0a34809bf035ef16b27a98d",
        # 009党务
        "kbaseId": "367ddef184304c03bd3690690966aab7"
    }],
    "档案": [{
        # 档案FAQ
        "id": "5e20510c02884fada5cb8639777c95a8",
        # 010档案
        "kbaseId": "6ac81fb90ab24488ac1ee009f4894f70"
    }],
    "中兴通识": [{
        # 中兴通识类FAQ
        "id": "79f4a30582ed41e99941334039a20c25",
        # 017中兴通识类
        "kbaseId": "12824751384f485d8ea5a3f6c25303fd"
    }],
    "FAQ补充信息": [
    ]
}

# 测试环境-向量库生产环境
document_index = {
    "财务": [{
        # 财务FAQ
        "id": "9f4dcde2d4d9448e9eec954cb5706fcd",
        # 001财务
        "kbaseId": "05160ce400b541c79e831aa598905c7e"
    }],
    "行政服务": [{
        # 行政文档
        "id": "cefd38bf4194492ca9fd360dbac922fb",
        # 002行政服务
        "kbaseId": "1354db4d980a400ebd3ada25885d51cf"
    }],
    "人力资源": [{
        # 人力资源文档
        "id": "632691e609c049a58a01d6557f43dbad",
        # 003人力资源
        "kbaseId": "49494fe74a3a435892ecf9c265c07b20"
    }],
    "DT产品及服务": [],
    "合规": [{
        # 合规网页
        "id": "125f23f0b55a408fa609e467622c2d35",
        # 008合规
        "kbaseId": "490166c589bf41df8dda2751b7489f33"
    }, {
        # 合规文档
        "id": "5b9b2d4da7bb4d0a85d183792dd99a8f",
        # 008合规
        "kbaseId": "490166c589bf41df8dda2751b7489f33"
    }],
    "党务": [{
        # 党务文档
        "id": "421295f698ec4f239b839b9569604f1b",
        # 009党务
        "kbaseId": "367ddef184304c03bd3690690966aab7"
    }],
    "档案": [{
        # 档案文档
        "id": "bbfadf9efdb74283bbf5f4af39f43766",
        # 010档案
        "kbaseId": "6ac81fb90ab24488ac1ee009f4894f70"
    }],
    "中兴通识": [{
        # 中兴通识文档
        "id": "aa18c3ff229b47aa9f1965a7b738c37f",
        # 017中兴通识类
        "kbaseId": "12824751384f485d8ea5a3f6c25303fd"
    }, {
        # 公司产品
        "id": "8c5d6c70f7634d0bb1cadd15ed37e41c",
        # 017中兴通识类
        "kbaseId": "12824751384f485d8ea5a3f6c25303fd"
    }],
    "FAQ补充信息": [
        # 财务
        {
            "kbaseId": "05160ce400b541c79e831aa598905c7e",
            "id": "5e9df83c9e1246599baa9bf8e747807f"
        },
        # 人事
        {
            "kbaseId": "49494fe74a3a435892ecf9c265c07b20",
            "id": "1e4c80692f094581aa902b808e04e9d5"
        },
        # 行政
        {
            "id": "f10f02d95b0743118a37731580320ade",
            "kbaseId": "1354db4d980a400ebd3ada25885d51cf"
        },
        # 党务
        {
            "id": "49bafb3e942c49d2871f3cabef13995d",
            "kbaseId": "367ddef184304c03bd3690690966aab7"
        },
        # DT产品及服务
        {
            "id": "d17f1d458c894ae1977e1131655bdac8",
            "kbaseId": "f601545cfdbb432aa93ff2fd28c71278"
        },
        # 档案
        {
            "id": "4aa7ca6bbf924343a0b68ae7d79dfd98",
            "kbaseId": "6ac81fb90ab24488ac1ee009f4894f70"
        },
        # 中兴通识
        # {
        #     "id": "723fd359cfbd4f3e9ef69425ef66ab0f",
        #     "kbaseId": "12824751384f485d8ea5a3f6c25303fd"
        # },
        # 合规
        {
            "id": "74e5d4711ce14347b6d226032c71f643",
            "kbaseId": "490166c589bf41df8dda2751b7489f33"
        }
    ]

}

kbaseId2field = {
    "05160ce400b541c79e831aa598905c7e": "财务",
    "1354db4d980a400ebd3ada25885d51cf": "行政服务",
    "49494fe74a3a435892ecf9c265c07b20": "人力资源",
    "ecf828ed68cb4c4c8cfaa90bd1b7bf6b": "商旅",
    "287d188877f0473283bab3a3f307de90": "会议云",
    "d697ebd36f8447978c3c8e519f1259ca": "工会",
    "f601545cfdbb432aa93ff2fd28c71278": "DT产品及服务",
    "490166c589bf41df8dda2751b7489f33": "合规",
    "367ddef184304c03bd3690690966aab7": "党务",
    "6ac81fb90ab24488ac1ee009f4894f70": "档案",
    "0c82677a699a4e9e8178e8965956ab38": "供应链",
    "7cc2add980c849d38d4186f77a92a178": "工程服务",
    "18b83f8d86904e6083c9cffb2624ba77": "商务合同",
    "4b5cca51d3284e1fa28cfde2823c21ac": "信息安全",
    "3594c85628b74fe3b8ba982f869e1931": "外事",
    "90b81c4380864e0899b8b3b747146b0d": "业务连续性",
    "12824751384f485d8ea5a3f6c25303fd": "中兴通识",
    "8560f2277a4e45a7b5bb44943b4ea2e4": "内控"
}

# 测试环境
q2zxbk_index = [
    {
        "kbaseId": "9e6bdba9b83c411480a3a7fd61e9ec92"
    }
]


# 检查ID和KbasID
def check_duplicates(dict_to_check, key1='id', key2='kbaseId'):
    values1 = set()
    values2 = set()
    duplicates_key1 = set()
    duplicates_key2 = set()

    for category in dict_to_check.values():
        for item in category:
            if item[key1] in values1:
                duplicates_key1.add(item[key1])
            else:
                values1.add(item[key1])

            if item[key2] in values2:
                duplicates_key2.add(item[key2])
            else:
                values2.add(item[key2])

    return duplicates_key1, duplicates_key2


if __name__ == "__main__":
    duplicate_ids, duplicate_kbaseIds = check_duplicates(document_index)
