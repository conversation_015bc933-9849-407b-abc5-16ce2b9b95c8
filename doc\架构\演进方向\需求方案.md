# 一、产品技术问答背景

1. 核心痛点：在客户交流，方案制作，技术澄清，合同谈判，以及员工日常学习时，检索产品信息低效。
2. 解决方案价值：

   * 秒级响应：替代人工平均分钟级别的文档搜索
   * 专家资源释放：减少专家咨询量，腾出产品线专家时间。
   * 全球员工赋能：中英文混合问答支持本地员工
3. 关键能力：跨系统文档检索、精准答案溯源、多语言自适应。
4. 业务场景地图：

{"title":"","syntax":"@startuml\nskinparam defaultTextAlignment center\nskinparam roundCorner 15\nskinparam shadowing false\nskinparam ArrowColor #444444\nskinparam ActorBackgroundColor #f0f0f0\nskinparam ActorBorderColor #333333\nskinparam ActorFontStyle bold\n\nrectangle \"客户现场交流\" <<场景1>> as A #ffcc99\nrectangle \"投标方案制作\" <<场景2>> as B #ccffcc\nrectangle \"合同谈判\" <<场景3>> as C #ccccff\nrectangle \"员工自学\" <<场景4>> as D #ffccff\ndatabase \"产品技术问答\" <<系统>> as Q #99ccff\n\nA --> Q : 即时答复\\n产品参数\nB --> Q : 调取\\n技术指标\nC --> Q : 功能边界\\n澄清\nD --> Q : 随时提问\\n学习知识\n\nnote right of Q\n <b>核心能力：</b>\n 1. 实时响应业务咨询\n 2. 精准调取技术文档\n 3. 统一企业知识口径\n 4. 7x24小时自助学习\nend note\n\n@enduml","dropshadow":true,"align":"left","type":"plantuml"}

# 二、当前系统架构

{"title":"","syntax":"@startuml\n!theme plain\nskinparam nodesep 10\nskinparam ranksep 15\nskinparam defaultFontName Arial\nskinparam defaultFontSize 12\nskinparam rectangle {\n BackgroundColor White\n BorderColor Black\n}\n\ntitle <b>产品技术问答系统架构</b>\\n<font color=#555555>\n\nleft to right direction\n\npackage \"数据源\" #AliceBlue {\n actor IT运维人员 as user\n file \"TSM文档库\" as tsm\n database \"Info-Go系统\" as info\_go\n}\n\npackage \"数据处理\" #LightGreen {\n component \"手工处理\" as manual\_processing #FFCCCC\n component \"自动同步\" as kg\_sync\n}\n\npackage \"存储层\" #Moccasin {\n database \"Elasticsearch\" as es <<关键字索引>> #FFCCCC\n database \"向量数据库\" as vector\_db <<嵌入表示>> #FFCCCC\n database \"知识图谱\" as knowledge\_graph <<产品关系>>\n}\n\npackage \"检索层\" #Wheat {\n component \"多路召回\" as recall\n component \"关键字检索\" as keyword\n component \"图关系检索\" as graph\n component \"向量检索\" as vector\n}\n\npackage \"重排层\" #PeachPuff {\n component \"结果重排\" as rerank\n component \"BGE-M3\" as bge\n component \"TF-IDF\" as tfidf\n component \"融合打分\" as fusion\n}\n\npackage \"生成层\" #Thistle {\n component \"答案生成\" as answer\_gen\n}\n\n' 数据流向\nuser --> manual\_processing\ntsm --> manual\_processing\ninfo\_go --> kg\_sync\n\nmanual\_processing --> es : 结构化存储\nmanual\_processing --> vector\_db : 向量化存储\nmanual\_processing --> knowledge\_graph : 关系存储\nkg\_sync --> knowledge\_graph : 关系存储\n\nes --> keyword\nvector\_db --> vector\nknowledge\_graph --> graph\n\nkeyword --> recall\nvector --> recall\ngraph --> recall\n\nrecall --> rerank\nrerank --> fusion\nbge --> fusion\ntfidf --> fusion\nfusion --> answer\_gen\n\n' 添加问题注释\nnote top of manual\_processing\n <b>手工处理待优化点：</b>\n 1. 效率瓶颈：人工操作导致数据更新延迟\n 2. 可扩展性差：数据增长需线性增加人力\n 3. 错误风险：人工操作易引入错误\nend note\n\nnote as storage\_note\n <b>存储层维护待优化点：</b>\n 1. 维护复杂：需保证ES与向量库数据一致性\n 2. 冗余存储：相同内容存于两个独立系统\n 3. 资源浪费：双倍存储空间和计算资源\nend note\nstorage\_note .. es\nstorage\_note .. vector\_db\n\n@enduml","dropshadow":false,"align":"left","type":"plantuml"}

# 三、用户核心诉求——产品技术问答DOD

## 3.1 数据源管理：实现语料源自动化同步与多模态解析能力

自动化同步：解决手工同步流程长、效率低的问题，需支持语料源自动同步。

多模态支持：解析能力需覆盖 Word、PDF、Excel、PPT、图片等文档类型。

产品兼容性：支持 BN、OTN、FM 三类产品语料。

## 3.2 用户体验：提升系统易用性与拓展能力

路径简化：缩短系统可达路径，提升触点效率（易找到）。

联网拓展：支持大模型联网查询常识，补充问答内容的覆盖范围。

对话记忆：支持多轮对话，避免重复问题相关要素。

## 3.3 问答质量：保障响应速度、答案准确性与内容安全

响应速度：首字返回时间 ≤5 秒（响应快）。

答案准确性：

* 90% 答案需达 2~3 分质量（满分 3 分）；
* 覆盖问题类型：产品配置、方案介绍、功能命令、指标查询、销售策略、通识知识、生命周期、成功案例、市场/竞情信息等。

权限管控：对返回内容实施权限控制，确保数据安全（安全感）。

## 3.4 辅助功能：优化参考文档的交互与定位

格式标准化：按角标标注参考文档来源；

可达性优化：点击角标可跳往对应文档来源

# 四、我司当前AI应开发框架能力分析

| 功能<br> | 对应系统<br> | 能力分析<br> | 系统截图<br> | 与需求对比所缺失的能力<br> |
--- | --- | --- | --- | ---
| 采集<br> | AI数据服务-知识工程流水线<br> | 1、支持同步icenter、Pal、代码库等数据源的数据至数据集<br> | ![](https://icenterapi.zte.com.cn/group3/M05/11/AF/CimMFmiVa8WATEimAAIWn\_wOw1g321.png?h=462&w=1176)<br> | 1、缺乏TSM的数据同步<br>2、不支持采集TSM的元数据，如：id、版本、文件类型等数据<br>3、缺乏同步知识图谱的数据<br> |
| 预处理<br> | AI数据服务-数据管理<br> | 1、数据处理<br>2、数据评估<br>3、标签集管理<br> | ![](https://icenterapi.zte.com.cn/group3/M05/0F/FE/CimMFmiVW5qAUCrKAADa57gB0Og173.png?h=649&w=1305)<br> | <br> |
| 存储<br> | 知识库<br>知识库<br> | 1、文档上传。包括txt、xlsx、doc、md、pdf、dpcx文件，同时也支持结构化文件（如CSV格式的FAQ对）和iCenter空间的内容3。<br>2、文件切分。提供知识文件的切分功能，支持三种切分方法：默认切分方法（按固定字符数切分）、正则表达式切分（支持自定义分隔符）以及MarkdownHeader文本切分（按标题切分）。<br>3、知识库检索<br> | ![](https://icenterapi.zte.com.cn/group3/M05/0F/D4/CimMFmiVWbaAdXv2AADqopBJ0ME980.png?h=775&w=1305)<br> | 1、文档格式不支持ppt、图片<br>2、采集的数据集到知识库的过程不是自动同步，存在人工导入的功能，这一过程会导致数据失真<br><br> |
| | 知识图谱<br> | 1、多源数据整合能力：支持将各类数据关系纳入知识体系<br> | ![](https://icenterapi.zte.com.cn/group3/M05/11/BF/CimMFmiVbGaAXPv\_AAPqlhV0XOI052.png?h=767&w=1176)<br> | 1、与TSM没有打通，无法把数据关系（如版本）同步至知识图谱<br> |
| 检索<br> | 知识库<br> | 1、检索方式可选：语义检索、全文检索 、混合检索<br>2、结果重排<br>3、按知识相关性选取<br>4、可选匹配的切片数，最大20<br><br> | ![](https://icenterapi.zte.com.cn/group3/M05/0F/C2/CimMFmiVWOSAQo2OAAEsEUpl4Eg363.png?h=1067&w=594)<br> | 1、不支持权限管控<br>2、提供的拓展能力不够灵活<br> |
| | 知识图谱<br> | 1、支持SQL查询<br> | ![](https://icenterapi.zte.com.cn/group3/M05/0F/C8/CimMFmiVWReACj0TAADmjlnqMm0958.png?h=1088&w=611)<br> | <br> |




# 四、优化方案

* 问题的重写优化：基于上下文，通过语义识别，结合领域知识，增强用户提问
* 数据预处理：去除页眉、页脚等无用数据噪音；识别段落、句子等结构化信息；）
* 召回的完整性保证：如选取的切片因切分导致数据缺失，再次进行切片上下文的召回补充
* 事实性验证：使用另一个模型，专门对生成的回答和问题进行比对，判断答案与问题是否匹配
* 高质量的测试集准备，基于问题类型和提问难度，准备一套完整的测试集。

## 4.1、方案1

{"title":"","syntax":"@startuml\n!theme plain\nskinparam nodesep 10\nskinparam ranksep 15\nskinparam defaultFontName Arial\nskinparam defaultFontSize 12\nskinparam rectangle {\n BackgroundColor White\n BorderColor Black\n}\n\ntitle <b>产品技术问答系统架构（方案1）</b>\\n<font color=#555555>\n\nleft to right direction\n\npackage \"数据源\" #AliceBlue {\n file \"TSM文档库\" as tsm\n database \"Info-Go系统\" as info\_go\n}\n\npackage \"数据处理\" #LightGreen {\n component \"DN Studio流水线采集\" as studio\_sync #FFCCCC\n component \"自动同步\" as kg\_sync #FFCCCC\n}\n\npackage \"存储层\" #Moccasin {\n database \"DN Studio知识库\" as knowledge\_DB <<关键字+向量索引>> #FFCCCC\n database \"知识图谱\" as knowledge\_graph <<产品关系>>\n}\n\npackage \"检索层\" #Wheat {\n component \"多路召回\" as recall\n component \"混合检索\" as kwSerach\n component \"图关系检索\" as graph\n}\n\npackage \"重排层\" #PeachPuff {\n component \"结果重排\" as rerank\n component \"BGE-M3\" as bge\n component \"TF-IDF\" as tfidf\n component \"融合打分\" as fusion\n}\n\npackage \"生成层\" #Thistle {\n component \"答案生成\" as answer\_gen\n}\n\n' 数据流向\ntsm --> studio\_sync \ninfo\_go --> kg\_sync\n\nstudio\_sync --> knowledge\_DB : 结构化存储\nkg\_sync --> knowledge\_graph : 关系存储\n\nknowledge\_DB --> kwSerach\nknowledge\_graph --> graph\n\nkwSerach--> recall\ngraph --> recall\n\nrecall --> rerank\nrerank --> fusion\nbge --> fusion\ntfidf --> fusion\nfusion --> answer\_gen\n\n@enduml","dropshadow":false,"align":"left","type":"plantuml"}

1、数据源管理：TSM同步至DN Studio知识库，实现数据自动同步

2、检索：采用知识库和知识图谱的检索能力，在AI应用平台开发接口，获取检索到的切片，对检索结果进行加工

3、答案加工与优化：对检索到的结果进行重排、格式调优

## 4.2、方案2

{"title":"","syntax":"@startuml\n!theme plain\nskinparam nodesep 10\nskinparam ranksep 15\nskinparam defaultFontName Arial\nskinparam defaultFontSize 12\nskinparam rectangle {\n BackgroundColor White\n BorderColor Black\n}\n\ntitle <b>产品技术问答系统架构（方案2）</b>\\n<font color=#555555>\n\nleft to right direction\n\npackage \"数据源\" #AliceBlue {\n file \"TSM文档库\" as tsm\n database \"Info-Go系统\" as info\_go\n}\n\npackage \"数据处理\" #LightGreen {\n component \"DN Studio采集\" as manual\_processing #FFCCCC\n component \"自动同步\" as kg\_sync\n}\n\npackage \"存储层\" #Moccasin {\n database \"Elasticsearch\" as es <<关键字索引>> #FFCCCC\n database \"向量数据库\" as vector\_db <<嵌入表示>> #FFCCCC\n database \"知识图谱\" as knowledge\_graph <<产品关系>>\n}\n\npackage \"检索层\" #Wheat {\n component \"多路召回\" as recall\n component \"关键字检索\" as keyword\n component \"图关系检索\" as graph\n component \"向量检索\" as vector\n}\n\npackage \"重排层\" #PeachPuff {\n component \"结果重排\" as rerank\n component \"BGE-M3\" as bge\n component \"TF-IDF\" as tfidf\n component \"融合打分\" as fusion\n}\n\npackage \"生成层\" #Thistle {\n component \"答案生成\" as answer\_gen\n}\n\n' 数据流向\ntsm --> manual\_processing\ninfo\_go --> kg\_sync\n\nmanual\_processing --> es : 结构化存储\nmanual\_processing --> vector\_db : 向量化存储\nmanual\_processing --> knowledge\_graph : 关系存储\nkg\_sync --> knowledge\_graph : 关系存储\n\nes --> keyword\nvector\_db --> vector\nknowledge\_graph --> graph\n\nkeyword --> recall\nvector --> recall\ngraph --> recall\n\nrecall --> rerank\nrerank --> fusion\nbge --> fusion\ntfidf --> fusion\nfusion --> answer\_gen\n\n@enduml","dropshadow":false,"align":"left","type":"plantuml"}

1、数据源管理：TSM同步至DN Studio知识库，随后导入进自建的向量库、ES库以及知识图谱中，实现数据自动采集

2、检索：基于原有的检索流程，进行模型调优

3、答案加工与优化：对检索到的结果进行重排、格式调优

## 4.3 方案对比

| 方案<br> | 优势<br> | 劣势<br> |
--- | --- | ---
| 方案1<br> | 1、重复利用DN Studio通用能力，搭建流程快<br>2、系统可跟随平台演进增强问答能力<br>3、DN Studio的整体框架与原自建框架能力一致<br> | 1、检索需求的能力灵活性较差<br>2、受限制于DN Studio的模型调剂能力<br> |
| 方案2<br> | 1、系统检索能力更加灵活，适用于更定制化的特性<br>2、短时间内，检索能力上限更高<br> | 1、维护任务重，需要维护三个数据源<br>2、演进成本更高<br> |

# 五、外部依赖识别

外部依赖识别

# 

#