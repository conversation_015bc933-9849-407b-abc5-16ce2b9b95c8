"""
PKL文件查看器 - 用于查看监控数据
"""

import pickle
import json
from pathlib import Path
from datetime import datetime

def view_pkl_file(file_path="monitor_data/monitor_data_20250806.pkl"):
    """查看pkl文件内容"""
    
    print("\n" + "="*60)
    print("                PKL监控数据查看器")
    print("="*60)
    
    # 检查文件是否存在
    pkl_path = Path(file_path)
    if not pkl_path.exists():
        print(f"\n错误：文件 {file_path} 不存在")
        print("提示：请确保文件路径正确")
        return
    
    # 加载pkl文件
    print(f"\n正在加载文件: {pkl_path.name}")
    print(f"文件大小: {pkl_path.stat().st_size / 1024:.1f} KB")
    
    try:
        with open(pkl_path, 'rb') as f:
            data = pickle.load(f)
        print("文件加载成功！")
    except Exception as e:
        print(f"加载失败: {e}")
        return
    
    # 显示数据内容
    print("\n" + "="*60)
    print("                    数据内容")
    print("="*60)
    
    # 1. 基本信息
    print("\n【基本信息】")
    print(f"  查询总数: {data.get('query_counter', 0)} 次")
    print(f"  监控开始时间: {data.get('start_time', 'N/A')}")
    
    # 2. 召回源排行榜
    print("\n【召回源贡献度排行榜】")
    print("-"*50)
    
    leaderboard = data.get('leaderboard', [])
    if leaderboard:
        print(f"{'名次':<5} {'召回源':<10} {'贡献率':<12} {'总得分':<10} {'Top1命中':<10}")
        print("-"*50)
        
        medals = ["🥇", "🥈", "🥉"]
        for idx, item in enumerate(leaderboard, 1):
            medal = medals[idx-1] if idx <= 3 else f"{idx}."
            # 使用ASCII字符替代emoji
            if idx == 1:
                medal = "[金]"
            elif idx == 2:
                medal = "[银]"
            elif idx == 3:
                medal = "[铜]"
            else:
                medal = f" {idx}."
                
            print(f"{medal:<5} {item['source']:<10} {item['contribution_rate']:<11.1f}% "
                  f"{item['total_score']:<10.2f} {item['top1_count']:<10}")
    
    # 3. 详细统计
    print("\n【各召回源详细统计】")
    print("-"*50)
    
    global_stats = data.get('global_stats', {})
    for source in ['es', 'milvus', 'kg']:
        if source in global_stats:
            stats = global_stats[source]
            print(f"\n{source.upper()}召回统计:")
            print(f"  总召回次数: {stats.recall_count}")
            print(f"  命中次数: {stats.hit_count}")
            print(f"  Top1/3/5命中: {stats.top1_count}/{stats.top3_count}/{stats.top5_count}")
            print(f"  总得分: {stats.total_score:.2f}")
            print(f"  平均贡献率: {stats.contribution_rate:.1f}%")
    
    # 4. 数据导出选项
    print("\n" + "="*60)
    print("                    导出选项")
    print("="*60)
    
    # 导出为可读的文本格式
    output_file = pkl_path.with_suffix('.txt')
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("监控数据报告\n")
        f.write("="*60 + "\n\n")
        f.write(f"生成时间: {datetime.now()}\n")
        f.write(f"数据文件: {pkl_path.name}\n")
        f.write(f"查询总数: {data.get('query_counter', 0)}\n")
        f.write(f"监控开始: {data.get('start_time', 'N/A')}\n\n")
        
        f.write("召回源排行榜\n")
        f.write("-"*40 + "\n")
        for idx, item in enumerate(leaderboard, 1):
            f.write(f"{idx}. {item['source']}: 贡献率{item['contribution_rate']:.1f}%, "
                   f"得分{item['total_score']:.2f}\n")
    
    print(f"✅ 文本报告已保存到: {output_file}")
    
    # 导出为JSON格式
    json_file = pkl_path.with_suffix('.json')
    json_data = {
        'query_counter': data.get('query_counter', 0),
        'start_time': str(data.get('start_time', '')),
        'leaderboard': leaderboard,
        'summary': {
            'es_contribution': leaderboard[0]['contribution_rate'] if leaderboard else 0,
            'kg_contribution': leaderboard[1]['contribution_rate'] if len(leaderboard) > 1 else 0,
            'milvus_contribution': leaderboard[2]['contribution_rate'] if len(leaderboard) > 2 else 0
        }
    }
    
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ JSON数据已保存到: {json_file}")
    
    print("\n" + "="*60)
    print("                    使用说明")
    print("="*60)
    print("1. PKL文件：Python的二进制数据格式，需要用pickle库读取")
    print("2. TXT文件：人类可读的文本报告，可以直接打开查看")
    print("3. JSON文件：结构化数据，可以用任何JSON查看器打开")
    print("\n提示：您可以直接打开生成的TXT或JSON文件查看详细数据")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # 如果提供了文件路径参数
        view_pkl_file(sys.argv[1])
    else:
        # 使用默认路径
        view_pkl_file()