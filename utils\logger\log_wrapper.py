import copy

from utils.logger.logger_util import extra_value


def wrap_log_content(content, g=None):
    if g is None:
        g = {}
    return f"[msgId:{g.get('msgId')}][chatUuid:{g.get('chatUuid')}] [log]{content}"

def get_extra(extra: dict) -> dict:
    """
    这个函数接受一个字典参数extra，并返回一个更新后的字典。
    它首先创建一个副本extra_dict，然后将传入的extra字典合并到这个副本中。
    这样做是为了不修改原始的extra_value字典。
    """
    extra_dict = copy.deepcopy(extra_value)
    extra_dict.update(extra)
    return extra_dict