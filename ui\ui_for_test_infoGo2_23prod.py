import datetime
import json
import sys

sys.path.append("..")
import streamlit as st
import requests
from streamlit_extras.colored_header import colored_header

# from sql_util import MySQLTool

from stream_entity import SSEClient
from ast import literal_eval
import time

st.set_page_config(page_title="AI助手", page_icon='☺')

base_url = "http://10.55.33.23:31983"

# es的映射不同领域下对应的不同es索引
# ["财务","人事"]
es_index_dic = {
    "营销kg": "yxxbk",
}

kid_index_dic = {
    "营销kg": "",
}


# 不适用大模型
def generate_response_llm(text, model_name):
    bo = {
        "chatuuid": "12345",
        "text": text,
        "temperature": st.session_state.temperature,
        "top_k": st.session_state.top_k,
        "top_p": st.session_state.top_p,
        "model_name": model_name,
        "model_url": st.session_state.model_url_dict[model_name]['url'],
        "es_index": es_index_dic[fileds_name],
        "vector_kid": kid_index_dic[fileds_name]
    }
    receive = requests.post(
        json=bo,
        url=base_url + "/zte-dmt-dmp-zxtech/faq2"
    )

    result = str(receive.json()['bo']['result'])

    st.session_state.candidates = receive.json()['bo']['candidates']
    return result


def generate_response_llm_stream(text, model_name, fileds_name):
    bo = {
        "chatuuid": "1",
        "text": text,
        # "model": model_name
    }
    headers = {"X-Emp-No": "10344606"}
    receive = requests.post(
        headers=headers,
        stream=True,
        json=bo,
        url=base_url + "/zte-dmt-dmp-zxtech/faq"
    )

    # st.session_state.candidates = receive.json()['bo']['candidates']
    client = SSEClient(receive)
    events = client.events()

    return events


if __name__ == '__main__':
    model_url_dict = {
        'Qwen1.0-72B-Chat': {'name': 'Qwen1.0-72B-Chat',
                             'url': 'http://10.55.19.154:30800/api/nebulabiz-test/v1/v1/chat/completions',
                             'desc': 'Qwen2_72B测试', 'stream': 1}
    }
    if 'temperature' not in st.session_state:
        st.session_state.temperature = 0
    if 'top_k' not in st.session_state:
        st.session_state.top_k = 5
    if 'top_p' not in st.session_state:
        st.session_state.top_p = 0.85
    if 'model_url_dict' not in st.session_state:
        st.session_state.model_url_dict = model_url_dict

    if "fileds" not in st.session_state:
        st.session_state.fileds = list(es_index_dic.keys())
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "candidates" not in st.session_state:
        st.session_state.candidates = ""

    st.markdown('<style>div { word-wrap: break-word; table-layout: fixed; word-break: break-all; }</style>',
                unsafe_allow_html=True)
    st.title("DTLLM-AI领域助手")
    st.markdown('AI领域助手')
    st.markdown('*createdby:* **DT-AI**')
    colored_header(label='', description='')

    with st.sidebar:
        st.header(' 💬 产品技术问答测试环境')
        st.markdown('''
        ---
        ### 🔮 介绍

        DT-LLM-AI领域助手测试环境

        🏀🏓🏈🎳⚾🏒🥊⛳🤿🏏🎾🎿🏐⛸️
        ''')

        st.markdown('---')
        # 领域
        fileds_name = st.selectbox("领域", st.session_state.fileds)
        st.markdown('---')
        # st.markdown("🔴 ZTE SMART SERVICE")
        show_model_name = st.selectbox("模型", st.session_state.model_url_dict.keys())
        model_name = model_url_dict[show_model_name]['name']
        st.session_state.temperature = st.slider("temperature", min_value=0.0, max_value=2.0, value=0.0, step=0.05)
        st.session_state.top_p = st.slider("top_p", min_value=0.0, max_value=1.0, value=0.85, step=0.05)
        st.session_state.top_k = st.slider("top_k", min_value=0, max_value=30, value=5, step=1)
        st.markdown("---")
        st.markdown("**检索内容：**")
        st.write(st.session_state.candidates)

    if st.button("clear", type="primary"):
        st.session_state.messages = []

    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            if message["role"] == 'user':
                st.markdown(message["content"], unsafe_allow_html=True)
            else:
                st.markdown(f"{message['model']}\n\n" + message["content"], unsafe_allow_html=True)

    if prompt := st.chat_input("请输入您的问题！"):
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user"):
            st.markdown(prompt)

        with st.chat_message("assistant"):
            message_placeholder = st.empty()
            full_response = ''

            start_time = time.time()  # 获取方法执行前的时间戳

            # intent = search_classify_result(st.session_state.messages[-1]['content'])
            end_time = time.time()  # 获取方法执行后的时间戳
            execution_time = str(round((end_time - start_time), 3)) + 's'
            intent = 2
            if intent == 2:
                # 流式处理
                if 1 == st.session_state.model_url_dict[model_name]['stream']:

                    events = generate_response_llm_stream(st.session_state.messages[-1]['content'], model_name,
                                                          fileds_name)
                    full_response = ""
                    stop_flag = False
                    multilang_flag = True
                    for i, event in enumerate(events):
                        if i == 0:
                            res=eval(event.data)
                            try:
                                full_response = " **大模型回答**:\n\n"+f"{res['result']}"
                                continue
                            except:
                                st.session_state.candidates = event.data
                                continue
                        if stop_flag:
                            # full_response += f'\n\n意图识别耗时{execution_time}'
                            full_response += f"\n\n*{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*"
                            message_placeholder.markdown(f"{model_name}\n\n" + full_response, unsafe_allow_html=True)
                            break
                        dict_res = json.loads(event.data)
                        if dict_res['finishReason']:
                            stop_flag = True
                            continue
                        full_response += dict_res['result']
                        message_placeholder.markdown(f"Qwen2.0-72B-Chat\n\n" + full_response, unsafe_allow_html=True)
                # 非流式
                else:
                    full_response = generate_response_llm(st.session_state.messages[-1]['content'], model_name)
                    st.session_state.candidates = full_response
                    full_response += f"\n\n*{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*"
                    message_placeholder.markdown(f"{model_name}\n\n" + full_response, unsafe_allow_html=True)
                    model_name = "检索结果"

            st.session_state.messages.append({"role": "assistant", "content": full_response, "model": model_name})
            st.rerun()
