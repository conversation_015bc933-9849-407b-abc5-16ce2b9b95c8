@startuml ZTE 产品检索系统增强版系统架构图
!theme vibrant

skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 12
skinparam componentStyle rectangle
skinparam packageStyle rectangle
skinparam ArrowThickness 2

title **ZTE 产品检索系统 - 增强版系统架构图**\n<i>Enhanced System Architecture</i>\n
' 颜色与分层样式
skinparam component {
  BorderThickness 2
  RoundCorner 10
}

rectangle "**接入层**" as AccessLayer #E3F2FD {
  actor "业务用户" as User #BBDEFB
  component "API网关/Nginx\n(限流/黑白名单/WAF)" as APIGW #BBDEFB
}

rectangle "**应用接口层**" as InterfaceLayer #FFF3E0 {
  component "Flask Web服务\n(main.py)" as Flask #FFE0B2
  component "CORS/Request校验" as CORS #FFE0B2
  component "UAC Token验证\n(utils/tokenVerify.py)" as UACToken #FFE0B2
  component "RateLimiter/熔断器\n(令牌桶+重试+退避)" as Resilience #FFE0B2
}

rectangle "**控制器层**" as ControllerLayer #E8F5E9 {
  component "产品问答控制器\n(controller/zxtech_controller.py)" as ZXController #C8E6C9
  component "监控控制器\n(controller/monitor_controller.py)" as MonitorController #C8E6C9
}

rectangle "**业务编排与智能处理层**" as BizLayer #FFF8DC {
  component "核心服务编排\n(service/zxtech_service.py)" as CoreService #F0E68C
  component "查询预处理/实体抽取\n(utils/entity_util.py)" as Preprocess #F0E68C
  component "上下文/会话管理\n(utils/stream_entity.py)" as CtxMgr #F0E68C
  component "提示词中心\n(prompt/PromptLoader.py + prompt_instance)" as PromptCenter #F0E68C
  component "查询重写服务\n(rewrite/rewrite_model.py)" as Rewrite #F0E68C
  component "策略路由器\n(ES/Milvus/KG/HYBRID)" as StrategyRouter #F0E68C
}

rectangle "**检索与融合层**" as RetrieveLayer #E6FFE6 {
  component "ES 文本检索\n(retrieve/milvus_recall.py/ES part + utils/es_util)" as ESRetriever #B2DFDB
  component "Milvus 向量检索\n(retrieve/milvus_recall.py + utils/milvus_util)" as MilvusRetriever #B2DFDB
  component "KG 图谱检索\n(retrieve/kg_retrieve/*.py)" as KGRetriever #B2DFDB
  component "缓存加速(可选)\nRedis 查询/结果缓存" as Cache #B2DFDB
  component "结果融合/RRF\n(rerank/rrf.py)" as Fusion #B2DFDB
}

rectangle "**重排序与质量控制层**" as RerankLayer #EDE7F6 {
  component "BGE-M3 Reranker\n(rerank/bge_m3_reranker_v2.py)" as Reranker #D1C4E9
  component "去重/阈值判断\n(utils/recall_context.py)" as Quality #D1C4E9
}

rectangle "**答案生成与输出层**" as GenLayer #FFF8E1 {
  component "LLM 调用器\n(llm/LLMResult.py)" as LLMCaller #FFECB3
  component "流式输出/SSE\n(ui/stream_entity.py)" as Streamer #FFECB3
  component "引用/可追溯性拼装" as Citation #FFECB3
}

rectangle "**AI模型与嵌入层**" as ModelLayer #F3E5F5 {
  component "BGE-M3 嵌入\n(embedding/get_embedding.py)" as Embedder #E1BEE7
}

rectangle "**基础设施与支撑层**" as InfraLayer #F5F5F5 {
  component "配置中心\n(apollo/* + infrastructure/config/config_center.py)" as Apollo #E0E0E0
  component "系统配置\n(domain/config/zxtech_config.py)" as LocalConfig #E0E0E0
  component "日志/链路追踪\n(utils/logger, resource/full_link_log_setup.py)" as Observability #E0E0E0
  component "HTTP客户端\n(utils/http_util.py)" as HTTPClient #E0E0E0
  component "加密与签名\n(utils/encryption_util.py, utils/system_signature.py)" as Security #E0E0E0
  component "消息队列(可选)\n(Celery/RabbitMQ/Kafka)" as MQ #E0E0E0
  component "特性开关/AB实验(可选)\n(Config + env)" as FeatureFlags #E0E0E0
}

rectangle "**外部与数据存储**" as ExternalAndStorage #E8EAF6 {
  database "ElasticSearch 集群" as ES #C5CAE9
  database "Milvus 向量库" as Milvus #C5CAE9
  database "Nebula 图数据库" as Nebula #C5CAE9

  cloud "外部AI与基础服务" as ExternalServices {
    component "NebulaBiz LLM" as NebulaBiz #FFF59D
    component "BGE-M3 服务" as BGEService #FFF59D
    component "UAC 统一认证" as UAC #FFF59D
  }
}

' 主调用链
User --> APIGW : HTTPS 请求
APIGW --> Flask : 反向代理/转发
Flask --> CORS : 预检/跨域
Flask --> UACToken : Token 校验
Flask --> Resilience : 限流/熔断/重试
Flask --> ZXController : 路由到/faq
ZXController --> CoreService : 发起业务编排

CoreService --> Preprocess : 文本清洗/实体识别
CoreService --> CtxMgr : 读取上下文
CoreService --> PromptCenter : 选择提示词
CoreService --> Rewrite : 可选查询重写

CoreService --> StrategyRouter : 决策召回策略
StrategyRouter --> ESRetriever : 关键词检索
StrategyRouter --> MilvusRetriever : 向量检索
StrategyRouter --> KGRetriever : 图谱检索

ESRetriever --> Cache : 结果写入/命中
MilvusRetriever --> Cache : 结果写入/命中
KGRetriever --> Cache : 结果写入/命中

ESRetriever --> ES : 查询
MilvusRetriever --> Milvus : 查询
KGRetriever --> Nebula : 查询

ESRetriever --> Fusion : 候选集
MilvusRetriever --> Fusion : 候选集
KGRetriever --> Fusion : 候选集

Fusion --> Reranker : TopN 候选
Reranker --> Quality : 去重/阈值
Quality --> LLMCaller : 合格文档
LLMCaller --> NebulaBiz : 调用LLM
LLMCaller --> Streamer : 生成流式输出
Streamer --> Citation : 附带引用/来源
Citation --> ZXController : 结果回传
ZXController --> Flask : 统一响应包装
Flask --> APIGW : HTTP 响应
APIGW --> User : 输出答案

' 支撑连接
Apollo --> LocalConfig : 配置同步
LocalConfig --> CoreService : 配置注入
Security ..> UACToken : 加密验证
HTTPClient ..> NebulaBiz : API调用
Observability ..> CoreService : 结构化日志/指标/Trace
FeatureFlags ..> StrategyRouter : 策略切换/AB实验
MQ ..> CoreService : 异步任务/批处理(可选)

note right of StrategyRouter
  策略选择示例：
  - 识别到产品型号 → 优先ES+KG，辅以向量
  - 纯问答/概念问法 → 向量优先
  - 版本敏感 → 限定索引/实体版本
end note

note bottom of Resilience
  韧性策略：
  - 令牌桶限流/请求级速率
  - 指数退避重试
  - 熔断与降级（仅ES检索/仅向量检索）
  - 缓存兜底
end note

legend right
  |= 分层 |= 说明 |
  | 接入层 | 统一入口、WAF/限流 |
  | 应用接口层 | 鉴权/入站治理 |
  | 控制器层 | 路由与API编排 |
  | 业务编排与智能处理层 | 预处理、重写、策略决策 |
  | 检索与融合层 | ES/Milvus/KG 与 RRF融合 |
  | 重排序与质量控制层 | BGE-M3 Rerank、去重阈值 |
  | 答案生成与输出层 | LLM 调用与SSE输出 |
  | 基础设施与支撑层 | 配置、日志、加密、队列 |
  | 外部与数据存储 | ES/Milvus/Nebula 与外部服务 |
endlegend

@enduml


