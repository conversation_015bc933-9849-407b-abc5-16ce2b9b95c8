"""
测试改进后的监控系统
"""

import requests
import time
from monitor.recall_monitor import get_monitor

# API配置
BASE_URL = "http://127.0.0.1:10023/zte-ibo-acm-productretrieve"
HEADERS = {
    'X-Emp-No': '0668001470',
    'X-Auth-Value': '6a9f6a6db72bfb1d0dd8ddbc2053d32f',
    'Content-Type': 'application/json'
}

def test_faq_with_monitor():
    """测试FAQ接口并检查监控"""
    
    print("=" * 60)
    print("        测试改进后的监控系统")
    print("=" * 60)
    
    # 获取初始监控数据
    monitor = get_monitor()
    initial_count = monitor.query_counter
    print(f"\n初始查询数: {initial_count}")
    
    # 测试查询
    test_queries = [
        "M6000-8S Plus的设备尺寸是多少",
        "如何配置VLAN",
        "系统日志在哪里查看"
    ]
    
    print("\n发送测试查询...")
    print("-" * 40)
    
    for i, query_text in enumerate(test_queries, 1):
        print(f"\n[{i}/{len(test_queries)}] 查询: {query_text}")
        
        # 发送请求
        try:
            response = requests.post(
                f"{BASE_URL}/faq",
                headers=HEADERS,
                json={"text": query_text},
                timeout=10
            )
            
            if response.status_code == 200:
                print(f"  ✓ 请求成功")
                # 给监控系统一点时间处理
                time.sleep(0.5)
            else:
                print(f"  ✗ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"  ✗ 请求异常: {e}")
    
    # 等待监控数据更新
    print("\n等待监控数据更新...")
    time.sleep(2)
    
    # 检查监控数据
    print("\n" + "=" * 60)
    print("        监控数据检查")
    print("=" * 60)
    
    new_count = monitor.query_counter
    print(f"当前查询数: {new_count}")
    print(f"新增查询数: {new_count - initial_count}")
    
    if new_count > initial_count:
        print("\n✅ 监控系统正常工作！")
        
        # 显示排行榜
        print("\n当前排行榜：")
        print("-" * 40)
        for idx, item in enumerate(monitor.leaderboard[:3], 1):
            print(f"{idx}. {item['source']}: 贡献率{item['contribution_rate']:.1f}%, 得分{item['total_score']:.2f}")
        
        # 查看最新的监控文件
        from pathlib import Path
        monitor_dir = Path("monitor_data")
        md_files = list(monitor_dir.glob("monitor_report_*.md"))
        if md_files:
            latest_md = max(md_files, key=lambda x: x.stat().st_mtime)
            print(f"\n最新监控报告: {latest_md.name}")
            print("可以打开该文件查看详细报告")
            
    else:
        print("\n⚠️ 监控数据未更新！")
        print("\n可能的原因：")
        print("1. 服务未正确启动")
        print("2. 监控装饰器未生效")
        print("3. 请求未到达服务层")
        
        # 检查日志
        print("\n检查最新日志...")
        import subprocess
        try:
            result = subprocess.run(
                ['tail', '-n', '20', 'logs/milvus-2025-08-06.log'],
                capture_output=True,
                text=True
            )
            if "[监控]" in result.stdout:
                print("日志中有监控记录")
            else:
                print("日志中没有监控记录")
        except:
            pass

if __name__ == "__main__":
    print("\n请确保服务已启动: python main.py")
    print("-" * 40)
    
    # 先检查服务是否运行
    print("\n检查服务状态...")
    try:
        response = requests.get(f"{BASE_URL}/recall-monitor/leaderboard", timeout=2)
        if response.status_code == 200:
            print("✓ 监控API可访问")
        else:
            print(f"✗ 监控API响应: {response.status_code}")
    except Exception as e:
        print(f"✗ 无法连接到服务: {e}")
        print("\n请先启动服务: python main.py")
        exit(1)
    
    # 运行测试
    test_faq_with_monitor()