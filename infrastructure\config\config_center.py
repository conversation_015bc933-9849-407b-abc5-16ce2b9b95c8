import os
from typing import Any

from pydantic import BaseModel, Field, model_validator, ValidationError

# 定义环境变量名称常量
CONFIG_CENTER_APP_ID_NAME = "configcenter.appid"
CONFIG_CENTER_META_URL_NAME = "configcenter.url"
CONFIG_CENTER_NAMESPACE_NAME = "configcenter.namespace"
CONFIG_CENTER_CLUSTER_NAME = "configcenter.cluster"
CONFIG_CENTER_SECRET_NAME = "configcenter.accesskey.secret"
CONFIG_CENTER_TYPE_NAME = "configcenter.type"
CONFIG_KEY_NAME = "configcenter.key"


class ConfigCenter(BaseModel):
    app_id: str = Field(alias="appId", description="Application id")
    url: str = Field(description="Config center URL")
    namespace: str = Field(description="Config center namespace")
    cluster: str = Field(description="Config center cluster name")
    secret: str = Field(description="Access secret")
    type: str = Field(default="apollo", description="Config center type")
    key: str = Field(description="Access secret encrypt key")

    # 校验方法确保所有必填字段不为空

    @model_validator(mode="before")
    @classmethod
    def validate_fields(cls, data: Any):
        missing_fields = [key for key, value in data.items() if key != "type" and not value]
        if missing_fields:
            raise ValueError(f"Missing fields : {', '.join(missing_fields)}")
        return data


def load_config_from_env() -> ConfigCenter:
    """
    从环境变量加载配置中心配置并进行校验。

    Returns:
        ConfigCenter: 配置中心配置实例。

    Raises:
        ValueError: 如果必填字段缺失。
    """
    try:
        config_center = ConfigCenter(
            appId=os.getenv(CONFIG_CENTER_APP_ID_NAME),
            url=os.getenv(CONFIG_CENTER_META_URL_NAME),
            namespace=os.getenv(CONFIG_CENTER_NAMESPACE_NAME),
            cluster=os.getenv(CONFIG_CENTER_CLUSTER_NAME),
            secret=os.getenv(CONFIG_CENTER_SECRET_NAME),
            key=os.getenv(CONFIG_KEY_NAME),
            type=os.getenv(CONFIG_CENTER_TYPE_NAME, "apollo")
        )
        return config_center
    except ValidationError as e:
        raise ValueError(f"Load config center config failed : {e}")
