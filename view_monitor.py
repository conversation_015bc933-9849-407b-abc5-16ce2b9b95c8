#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
查看召回监控数据的工具脚本
"""

import os
import sys
import json
import pickle
from datetime import datetime
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def view_monitor_data():
    """查看最新的监控数据"""
    monitor_dir = Path("monitor_data")
    
    if not monitor_dir.exists():
        print("监控数据目录不存在")
        return
    
    # 获取今天的日期
    today = datetime.now().strftime("%Y%m%d")
    
    # 查看MD报告
    md_file = monitor_dir / f"monitor_report_{today}.md"
    if md_file.exists():
        print("\n" + "="*60)
        print("[监控报告] (Markdown)")
        print("="*60)
        with open(md_file, 'r', encoding='utf-8') as f:
            content = f.read()
            # 替换emoji字符为文本
            content = content.replace('📊', '[图表]')
            content = content.replace('📈', '[统计]')
            content = content.replace('📉', '[趋势]')
            content = content.replace('🥇', '[1]')
            content = content.replace('🥈', '[2]')
            content = content.replace('🥉', '[3]')
            print(content)
    
    # 查看JSON数据
    json_file = monitor_dir / f"monitor_data_{today}.json"
    if json_file.exists():
        print("\n" + "="*60)
        print("[监控统计] (JSON)")
        print("="*60)
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            # 处理不同的JSON结构
            if 'metadata' in data:
                print(f"总查询次数: {data['metadata']['query_count']}")
                print(f"监控开始时间: {data['metadata']['start_time']}")
            elif 'query_counter' in data:
                print(f"总查询次数: {data['query_counter']}")
                print(f"监控开始时间: {data.get('start_time', 'N/A')}")
            
            print("\n排行榜:")
            if 'leaderboard' in data:
                for i, item in enumerate(data['leaderboard'], 1):
                    print(f"{i}. {item['source']}: 贡献率 {item['contribution_rate']:.1f}%, 得分 {item['total_score']:.2f}")
    
    # 查看CSV数据
    csv_file = monitor_dir / f"monitor_leaderboard_{today}.csv"
    if csv_file.exists():
        print("\n" + "="*60)
        print("[排行榜CSV文件]")
        print("="*60)
        print(f"文件路径: {csv_file.absolute()}")
        print("可以用Excel打开查看详细数据")
    
    print("\n" + "="*60)
    print("[OK] 监控系统正常工作中！")
    print("="*60)

def test_monitor_api():
    """测试监控API"""
    import requests
    
    base_url = "http://127.0.0.1:10023/zte-ibo-acm-productretrieve"
    
    print("\n测试监控API...")
    
    # 测试统计API
    try:
        resp = requests.get(f"{base_url}/monitor/stats")
        if resp.status_code == 200:
            print("\n[OK] 统计API响应:")
            print(json.dumps(resp.json(), indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"[WARNING] 统计API调用失败: {e}")
    
    # 测试排行榜API
    try:
        resp = requests.get(f"{base_url}/monitor/leaderboard")
        if resp.status_code == 200:
            print("\n[OK] 排行榜API响应:")
            print(json.dumps(resp.json(), indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"[WARNING] 排行榜API调用失败: {e}")

if __name__ == "__main__":
    print("="*60)
    print("[召回监控数据查看工具]")
    print("="*60)
    
    view_monitor_data()
    
    # 询问是否测试API
    print("\n是否测试监控API? (y/n): ", end="")
    if input().lower() == 'y':
        test_monitor_api()