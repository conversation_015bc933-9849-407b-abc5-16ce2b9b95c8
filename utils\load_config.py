import os

import yaml
from apollo.apollo_client import ApolloClient
from infrastructure.config.config_center import CONFIG_CENTER_NAMESPACE_NAME, ConfigCenter, load_config_from_env, \
    CONFIG_KEY_NAME
from utils.apollo_util import _decrypt_data_key
from utils.encryption_util import decrypt


def load_config(file_path):
    with open(file_path, 'r') as file:
        config = yaml.safe_load(file)
    return config

def load_config_from_net():
    namespace = os.getenv(CONFIG_CENTER_NAMESPACE_NAME)
    data = load_config_from_config_center(load_config_from_env(), namespace)
    key_name = os.getenv(CONFIG_KEY_NAME)
    conf_dict = yaml.safe_load(data['configurations']['content'])

    APP_NAME = decrypt(conf_dict['APP_NAME'],key_name)
    USER_ID = decrypt(conf_dict['USER_ID'],key_name)
    APP_SECRET = decrypt(conf_dict['APP_SECRET'],key_name)
    kms_headers = {
        "APP_NAME": APP_NAME,
        "USER_ID": USER_ID,
        "APP_SECRET": APP_SECRET
    }
    kms_domain = conf_dict['kms_domain']
    kms_uri = conf_dict['kms_uri']
    secretKeyId = decrypt(conf_dict['secretKeyId'],key_name)
    dataKeyCipher = conf_dict['dataKeyCipher']
    kms_req = {
        "dataKeyCipher": dataKeyCipher,
        "secretKeyId": secretKeyId
    }
    apollo_key_flag = _decrypt_data_key(kms_domain, kms_uri, kms_req, kms_headers)
    #apollo_key_flag = 'KVxDoHarNCXEsDIaBZjmzBzaJjbIW70GOfrnc9jCqQM='
    conf_dict['key-flag'] = decrypt(conf_dict['key-flag'], apollo_key_flag)
    return conf_dict


def load_config_from_config_center(config_center: ConfigCenter,namespace :str):
    client = ApolloClient(
        app_id=config_center.app_id,
        cluster=config_center.cluster,
        config_url=config_center.url,
        secret=decrypt(config_center.secret,config_center.key),
    )
    return client.get_json_from_net(namespace)