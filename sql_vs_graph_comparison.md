# SQL vs 知识图谱查询对比

## 传统SQL方案

```sql
-- 查询ZXR10 M6000-8S Plus V2.1版本相关文档及兼容产品文档
WITH RECURSIVE product_docs AS (
    -- 主产品版本文档
    SELECT DISTINCT d.id, d.document_name, d.content, 'main_product' as doc_type
    FROM products p
    JOIN product_version pv ON p.id = pv.product_id
    JOIN version_document vd ON pv.version_id = vd.version_id  
    JOIN documents d ON vd.document_id = d.id
    WHERE p.product_name = 'ZXR10 M6000-8S Plus' 
    AND pv.version_number = 'V2.1'
    
    UNION ALL
    
    -- 产品直接关联文档
    SELECT DISTINCT d.id, d.document_name, d.content, 'product_direct' as doc_type
    FROM products p
    JOIN product_document pd ON p.id = pd.product_id
    JOIN documents d ON pd.document_id = d.id  
    WHERE p.product_name = 'ZXR10 M6000-8S Plus'
    
    UNION ALL
    
    -- 兼容产品的文档
    SELECT DISTINCT d.id, d.document_name, d.content, 'compatible_product' as doc_type
    FROM products p1
    JOIN product_compatibility pc ON p1.id = pc.product_id
    JOIN products p2 ON pc.compatible_product_id = p2.id
    JOIN product_version pv ON p2.id = pv.product_id
    JOIN version_document vd ON pv.version_id = vd.version_id
    JOIN documents d ON vd.document_id = d.id
    WHERE p1.product_name = 'ZXR10 M6000-8S Plus'
    AND pv.version_number <= 'V2.1'  -- 版本兼容性判断
    
    UNION ALL
    
    -- 系列相关文档
    SELECT DISTINCT d.id, d.document_name, d.content, 'series_related' as doc_type
    FROM products p
    JOIN product_series ps ON p.id = ps.product_id
    JOIN series s ON ps.series_id = s.id
    JOIN series_document sd ON s.id = sd.series_id
    JOIN documents d ON sd.document_id = d.id
    WHERE p.product_name = 'ZXR10 M6000-8S Plus'
    
    UNION ALL
    
    -- 产品族文档（可能需要递归查询）
    SELECT DISTINCT d.id, d.document_name, d.content, 'product_family' as doc_type
    FROM products p1
    JOIN product_hierarchy ph ON p1.id = ph.child_product_id
    JOIN products p2 ON ph.parent_product_id = p2.id
    JOIN product_document pd ON p2.id = pd.product_id  
    JOIN documents d ON pd.document_id = d.id
    WHERE p1.product_name = 'ZXR10 M6000-8S Plus'
)
SELECT id, document_name, content, doc_type
FROM product_docs
ORDER BY 
  CASE doc_type 
    WHEN 'main_product' THEN 1
    WHEN 'product_direct' THEN 2  
    WHEN 'compatible_product' THEN 3
    WHEN 'series_related' THEN 4
    WHEN 'product_family' THEN 5
  END,
  document_name;

-- 还需要额外的查询来处理：
-- 1. 版本兼容性复杂判断逻辑
-- 2. 产品替代关系
-- 3. 文档优先级排序
-- 4. 动态关系计算
```

## 知识图谱nGQL方案

```cypher
-- 同样的查询需求，使用Nebula Graph nGQL
MATCH path = (p:product {name: "ZXR10 M6000-8S Plus"})-[*1..3]-(d:document)
WHERE 
  -- 直接版本匹配
  (p)-[:product_version_relation]->(v:version {version: "V2.1"})-[:version_document_relation]->(d)
  OR
  -- 产品直接文档
  (p)-[:product_document_relation]->(d)
  OR  
  -- 兼容产品文档
  (p)-[:compatible_with]->(cp:product)-[:product_document_relation]->(d)
  OR
  -- 系列文档
  (p)<-[:series_product_relation]-(s:series)-[:series_document_relation]->(d)
  OR
  -- 产品族文档（自动递归）
  (p)-[:belongs_to*1..2]->(pf:product_family)-[:family_document_relation]->(d)

RETURN 
  d.id as document_id,
  d.document_name,
  d.content,
  labels(d) as doc_type,
  length(path) as relation_distance
ORDER BY relation_distance, d.priority DESC
LIMIT 50;

-- 扩展查询：找到相关产品的升级路径和替代方案
MATCH upgrade_path = (p:product {name: "ZXR10 M6000-8S Plus"})-[:upgrade_to*1..3]->(up:product)
RETURN upgrade_path;

-- 查找技术相关性文档
MATCH (p:product {name: "ZXR10 M6000-8S Plus"})-[:has_technology]->(t:technology)<-[:has_technology]-(rp:product)-[:product_document_relation]->(d:document)
RETURN d.document_name, d.content, rp.name as related_product;
```