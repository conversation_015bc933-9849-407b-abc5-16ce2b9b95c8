import re
# Started by AICoder, pid:8429ana5a56758c14b020802b03a5d215d206ef6
import re

def judge_string_type(s):
    # 定义正则表达式模式，匹配中文字符和英文字符
    chinese_pattern = re.compile(r'[\u4e00-\u9fa5]+')
    english_pattern = re.compile(r'[a-zA-Z]+')

    # 检查字符串中是否含有中文字符或英文字符
    has_chinese = bool(re.search(chinese_pattern, s))
    has_english = bool(re.search(english_pattern, s))

    # 根据检查结果返回字符串类型
    if has_chinese and has_english:
        return "Mixed"
    elif has_chinese:
        return "Chinese"
    elif has_english:
        return "English"
    else:
        return "Other"

# Ended by AICoder, pid:8429ana5a56758c14b020802b03a5d215d206ef6