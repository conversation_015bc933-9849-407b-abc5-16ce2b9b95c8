# 产品技术问答系统 开发设计（方案一 + 方案二）

版本：v1.0  
日期：2025-08-08  
适用范围：ZTE 产品技术问答（覆盖 BN / OTN / FM 多产品线）

---

### 1. 背景与目标

- 基于《需求方案》，解决在客户交流、方案制作、技术澄清、合同谈判与员工学习中“产品信息检索低效”的痛点。
- 达成 DOD：
  - 秒级响应：首字返回 ≤ 5s（SLA P95）。
  - 准确性：≥ 90% 答案达 2~3 分质量；覆盖配置、方案、命令、指标、策略、通识、生命周期、案例、竞情等问题类型。
  - 权限管控：对检索与返回内容做细粒度权限过滤。
  - 可达性：引用角标可直达文档来源；多语言（中英）与多轮对话；可选联网补充。

### 2. 范围与不在范围

- 在范围：TSM 文档库与 Info-Go 的采集/解析/索引/检索/重排/生成/权限/审计、应用服务接口、部署与运维、评测体系。
- 不在范围：TSM/Info-Go 源系统改造；组织流程调整；非产品域知识纳入（可后续规划）。

### 3. 能力映射（对需求方案）

- 数据源管理：
  - 方案一：TSM → DN Studio 知识库自动同步；Info-Go → 知识图谱同步。
  - 方案二：TSM → 自建采集流水线（ES/向量库/KG 同步）。
- 多模态解析：Word/PDF/Excel/PPT/图片，OCR（图片、扫描件）。
- 检索与重排：多路召回（关键字/语义/KG），BGE-M3 语义向量与交叉编码器重排，TF-IDF 作为弱特征；融合打分。
- 生成与事实性：RAG 生成，事实一致性校验器二次判定；引用角标可点击跳转。
- 多轮与联网：会话记忆，按需开启联网补充常识（遵循企业合规）。
- 安全与权限：单点登录、细粒度 ACL/ABAC、审计留痕、脱敏策略。

---

## 4. 公共技术方案（两种实施的共性能力）

### 4.1 解析与预处理

- 文档解析：Apache Tika + PDFBox；PPT/Excel/Word 专用解析；图片/扫描件采用 OCR（优先 PaddleOCR，本地部署）。
- 结构化抽取：标题层级、段落、表格、列表、页码；保留文档元数据（文档 id、版本、产品、文件类型、部门、级别）。
- 去噪与规范化：移除页眉页脚/页码水印；表格拉直；保留段内编号。
- 切分策略：
  - MarkdownHeader/标题分段优先；
  - 二级回退：按最大 token 长度（如 512-800 tokens）滑窗切分，重叠 10-15%；
  - 记录 chunk_id、doc_id、page_range、section_path；
  - 对于命令类/参数类内容引入结构化 schema（键值、枚举、默认值）。

### 4.2 嵌入与索引

- 向量模型：BGE-M3（多语种/多功能，统一语义空间），文本归一化（全角/半角、单位标准化）。
- 维度与存储：
  - 方案一：依赖 DN Studio 知识库的内置向量索引能力（按平台规范）。
  - 方案二：自建向量库（Milvus/pgvector/ES-KNN）；维度按 BGE-M3 配置（1024/768，以实际模型为准）。
- 关键字索引：ES 或平台内置；中文采用 IK/SmartCN/自定义词典，英文标准分词；同义词词典（产品术语）。
- 知识图谱：图谱存储包含实体与关系：产品→版本→特性→命令→指标→案例→配套；支持 Cypher/Gremlin 查询。

### 4.3 多路召回、重排与融合

- 召回通道：
  - 关键字 BM25/ES 查询（匹配标题、正文、元数据）。
  - 语义向量相似度（topK=50~200 可调）。
  - 图谱关系检索（实体/关系路径约束）。
- 重排：BGE-Reranker（跨编码器）对候选切片排序，保留 topK 用于生成。
- 融合打分：score = w_kw*BM25 + w_vec*sim + w_kg*kg + w_rr*rerank；权重通过验证集网格搜索确定，按产品域微调。
- 召回完整性：若切片边界截断，向上下文扩展合并；重复/冲突段落去重处理。

### 4.4 生成与事实性校验

- 生成：基于 RAG 模板，提供结构化答案（要点、表格、步骤、命令块），附带来源角标 [^n]。
- 事实性校验：独立判定器对 问题-答案-证据 进行一致性/覆盖率评分；低分触发保守输出或追问澄清。
- 引用与跳转：返回 cite 列表（doc_id、chunk_id、title、page、url、权限），前端渲染角标并可点击直达来源。

### 4.5 多轮对话与会话管理

- 会话内存：滑动窗口 + 语义摘要（长对话自动总结）；
- 上下文注入：仍以当轮召回证据为准，历史仅做 disambiguation 与 query rewrite；
- 话题切换识别：显著漂移时重置召回上下文。

### 4.6 权限与安全

- 身份：企业 SSO/OAuth2/OIDC；服务侧验签、短期 Token、最小权限原则。
- ACL/ABAC：
  - 文档级：组织、岗位、涉密级、产品线、国家/区域；
  - 切片级：继承文档 ACL，支持附加标签（如涉密字段脱敏）。
  - 检索前过滤（向量/关键字均携带过滤条件）；生成前二次过滤与脱敏（号码、邮箱、客户名称）。
- 审计：保留问答、证据、判定分、用户/会话、版本；满足合规可追溯。

### 4.7 性能与可用性

- 目标：P95 首字 ≤ 5s，P99 ≤ 8s；召回 ≤ 800ms，重排 ≤ 400ms，生成首字 ≤ 2s（流式）。
- 缓存：
  - Query 语义哈希 → 检索结果缓存；
  - 热点文档 chunk cache；
  - 分层缓存（本地 + Redis）。
- 并发：无共享状态服务化；向量库/ES 连接池；批量相似度计算。
- 可用性：多副本部署、就近路由、熔断/降级（仅关键字检索应急）。

---

## 5. 方案一：基于 DN Studio 知识库 + 知识图谱

### 5.1 架构图（PlantUML）

```plantuml
@startuml
!theme plain
left to right direction

package "数据源" #AliceBlue {
  file "TSM文档库" as TSM
  database "Info-Go" as INFO
}

package "平台能力" #LightGreen {
  component "DN Studio 知识库" as STU_DB <<向量+关键字>>
  component "知识图谱服务" as KG
}

package "应用服务" #Wheat {
  component "API Gateway" as GW
  component "Auth/ACL" as AUTH
  component "Retriever(Studio适配)" as RET
  component "Reranker(BGE)" as RR
  component "AnswerGen" as GEN
  component "Audit/Log" as AUD
}

TSM --> STU_DB : 自动同步
INFO --> KG : 自动同步

GW --> AUTH
AUTH --> RET
RET --> STU_DB : 混合检索
RET --> KG : 图查询
RET --> RR
RR --> GEN
GEN --> AUD

@enduml
```

### 5.2 关键组件

- Studio 适配器：封装检索 API（关键词/向量/混合），统一分页、超时与重试策略。
- 图谱查询器：实体抽取与关系路径限定（如 产品→版本→命令）。
- 检索融合器：多路结果归一化与去重、上下文补全。
- 生成器：RAG Prompt，结构化输出与角标标注。
- 权限模块：基于用户组织/岗位/涉密级做预过滤与结果脱敏。
- 审计模块：记录问答全链路与版本信息。

### 5.3 数据流

1) 同步：平台负责 TSM→Studio 知识库、Info-Go→KG 的自动同步。
2) 问答：用户→GW；鉴权→检索（Studio 混合 + KG）→重排→生成→返回引用。
3) 反馈：用户对答案打分/纠错，入库用于权重与策略优化。

### 5.4 接口设计（对外）

```yaml
openapi: 3.0.3
info: { title: 产品技术问答 API, version: 1.0.0 }
paths:
  /api/v1/ask:
    post:
      summary: RAG 问答
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                question: { type: string }
                top_k: { type: integer, default: 8 }
                conversation_id: { type: string }
                enable_web: { type: boolean, default: false }
                debug: { type: boolean, default: false }
      responses:
        '200':
          description: ok
          content:
            application/json:
              schema:
                type: object
                properties:
                  answer: { type: string }
                  references:
                    type: array
                    items:
                      type: object
                      properties:
                        doc_id: { type: string }
                        chunk_id: { type: string }
                        title: { type: string }
                        page: { type: string }
                        url: { type: string }
                  latency_ms: { type: integer }
  /api/v1/search:
    post:
      summary: 调试检索
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                query: { type: string }
                top_k: { type: integer, default: 20 }
      responses: { '200': { description: ok } }
```

### 5.5 权限与安全

- 统一接入 SSO（OIDC），JWT 携带组织/岗位/涉密级；
- Studio 检索请求附带过滤条件（可通过标签/空间/目录/自定义字段实现文档级过滤）；
- 生成阶段再次校验引用是否可见；
- 脱敏策略：客户名称、联系方式、序列号等敏感字段；
- 审计日志：问题、证据、判定分、用户、时间、版本号。

### 5.6 性能与容量

- 复用平台索引能力；应用侧加层缓存（语义哈希→检索结果 30-120s TTL）。
- 流式响应，首字目标 ≤ 2s；并发 500 RPS 以内可线性扩容。

### 5.7 部署与运维

- K8s 部署：GW/APP 多副本；BGE 模型服务（推理）独立部署；
- 配置：Studio/KG 访问凭据密管（Vault/Secret）；
- 监控：Prometheus 指标（qps、p95、命中率、错误码）、Trace、结构化日志。

### 5.8 风险与规避

- 平台能力约束：定制度受限 → 在应用层自研融合与重排以弥补；
- 同步延迟：与平台约定增量频率与回调；
- 权限字段差异：建立映射表与双重过滤。

---

## 6. 方案二：自建 ES + 向量库 + 知识图谱

### 6.1 架构图（PlantUML）

```plantuml
@startuml
!theme plain
left to right direction

package "数据源" #AliceBlue {
  file "TSM文档库" as TSM
  database "Info-Go" as INFO
}

package "采集与处理" #LightGreen {
  component "Ingestor" as ING
  component "Parser/OCR" as PAR
  component "Preprocessor" as PRE
  component "Indexer" as IDX
}

package "存储" #Moccasin {
  database "Elasticsearch" as ES
  database "向量库(Milvus/pgvector)" as VEC
  database "知识图谱(Neo4j/JanusGraph)" as KG
}

package "应用服务" #Wheat {
  component "API Gateway" as GW
  component "Auth/ACL" as AUTH
  component "Retriever(Hybrid)" as RET
  component "Reranker(BGE)" as RR
  component "AnswerGen" as GEN
  component "Audit/Monitor" as AUD
}

TSM --> ING
ING --> PAR
PAR --> PRE
PRE --> IDX
IDX --> ES
IDX --> VEC
INFO --> KG

GW --> AUTH
AUTH --> RET
RET --> ES
RET --> VEC
RET --> KG
RET --> RR
RR --> GEN
GEN --> AUD

@enduml
```

### 6.2 数据模型与索引

- 文档 Document：doc_id、title、version、product_line、file_type、owner_org、security_level、created_at、url。
- 切片 Chunk：chunk_id、doc_id、text、tokens、section_path、page_range、language、acl_tags[]、embeddings[]。
- ES 索引（示例）：

```json
{
  "settings": {
    "index": {
      "analysis": {
        "analyzer": {
          "zh_smart": { "type": "custom", "tokenizer": "ik_max_word" }
        }
      }
    }
  },
  "mappings": {
    "properties": {
      "doc_id": { "type": "keyword" },
      "title": { "type": "text", "analyzer": "zh_smart" },
      "text": { "type": "text", "analyzer": "zh_smart" },
      "product_line": { "type": "keyword" },
      "security_level": { "type": "keyword" },
      "acl_tags": { "type": "keyword" },
      "section_path": { "type": "keyword" },
      "page_range": { "type": "keyword" },
      "url": { "type": "keyword" }
    }
  }
}
```

- 向量库：collection 名称 `chunks`, 主键 chunk_id，字段 embedding(FloatVector)、doc_id、acl_tags、language；支持基于 acl_tags 的标量过滤检索。

### 6.3 检索与融合

- 步骤：
  1) ACL 预过滤条件构造（roles/org/security）。
  2) ES 关键字召回 topK_kw，VEC 语义召回 topK_vec，KG 实体/路径召回 topK_kg。
  3) 归一化、去重、上下文补全，拼装候选 N 条。
  4) BGE-Reranker 重排，选前 K 输入生成。
  5) 事实性校验不通过 → 触发追问或仅返回证据与澄清提示。

### 6.4 接口与事件

- 采集：
  - POST /internal/ingest/tsm 触发增量；
  - POST /internal/reindex 文档级重建索引；
  - CDC/Webhook 接入（可选）。
- 问答：沿用 5.4 的 API；增加 /api/v1/feedback 保存用户打分与纠错。

### 6.5 权限模型

- 用户态：SSO → JWT（sub、org、role、clearance、region）。
- 数据态：文档/切片携带 acl_tags 与 security_level；
- 检索：ES 与向量库均带过滤表达式；
- 结果：生成前再次校验来源可见性，超出权限则替换为“不可见来源”提示。

### 6.6 性能、容量与成本

- 初始规模：100k-500k 文档，2-10M 切片；
- ES：3-6 个 data nodes（热/温分层）；
- 向量库：Milvus 集群（2-4 query nodes，1-2 index nodes），IVF/IVF_PQ 索引；
- 解析/OCR：异步并行，队列限流；
- 成本：较方案一更高，但可控与可扩展性强。

### 6.7 部署与运维

- K8s Helm/ArgoCD 部署；滚动升级；
- 备份：ES 快照、向量库备份、KG dump；
- 监控：节点健康、检索延迟、top queries、命中率、错误码；
- 灰度与回滚：按流量权重灰度新模型/新权重。

### 6.8 风险与规避

- 多存储一致性：以 doc_id 为主键，写入幂等与对账任务；
- OCR 质量：对低质页采用二次识别与人工抽检；
- 中英文混检：统一嵌入空间 + 语言检测路由；
- 资源峰值：弹性扩容与队列削峰。

---

## 7. 公共附加能力（实现要点）

- 问题重写：基于语义与上下文的 query rewrite（纠正产品名、补全型号与版本）。
- 常识联网：仅在召回不足且用户授权时调用；结果纳入“非权威来源”标签。
- 引用角标：统一样式 [^n]，点击直达 `url#chunk_id` 定位（前端支持高亮）。
- 评测集：覆盖九类问题类型，分难度层级；离线评测（召回/重排/生成）与在线 A/B（CSAT、点击率、纠错率）。

---

## 8. 实施计划与里程碑（推荐）

- T0-T2 周：PoC 与权重寻优（方案一优先验证），构建评测集与监控面板。
- T3-T6 周：方案一灰度上线（核心用户组），闭环反馈；并行搭建方案二底座。
- T7-T10 周：方案二试运行（部分产品线），对比稳定性与准确率；
- T11+：按业务定制选择单栈或双栈并行，逐步迁移。

---

## 9. 附录

### 9.1 问答时序（方案通用，PlantUML）

```plantuml
@startuml
actor User
participant GW as "API Gateway"
participant AUTH as "Auth/ACL"
participant RET as "Retriever"
participant RR as "Reranker"
participant GEN as "AnswerGen"

User -> GW: ask(question)
GW -> AUTH: verify(token)
AUTH --> GW: allow(acl)
GW -> RET: retrieve(q, acl)
RET --> RET: multi-recall + fusion
RET -> RR: candidates
RR --> RET: ranked
RET -> GEN: topK + context
GEN --> GW: stream(answer + cites)
GW --> User: stream...
@enduml
```

### 9.2 RAG Prompt（示例）

```text
你是资深产品技术专家。基于提供的证据，回答用户问题：
- 优先列出关键参数/命令/限制条件；
- 如证据不足，明确指出并提出澄清问题；
- 在答案末尾以 [^n] 标注引用；
- 不要编造不存在的产品或参数。
```

### 9.3 事实性校验准则（摘要）

- 一致性：答案要点是否被证据原文覆盖。
- 相关性：证据与问题语义相关性是否充足。
- 禁伪：若矛盾或不足，优先保守输出并提示澄清。

### 9.4 质量与SLA指标

- 质量：离线 F1@K、nDCG、Rerank MAP、事实性通过率；在线 CSAT≥4.5/5。
- 性能：P95 首字≤5s；可用性≥99.9%；错误率≤0.5%。

---

本文档为实施与评审依据：方案一主打“快集成、低运维”，方案二主打“高灵活、强可控”。可按业务优先级先采用方案一落地，同时分阶段建设方案二底座，逐步实现能力扩展与成本优化。


