' PlantUML Plain Theme with Chinese Support
' 自定义的 plain 主题，支持中文

!define FONTNAME "Microsoft YaHei"

' 全局字体设置
skinparam defaultFontName FONTNAME
skinparam defaultFontSize 12
skinparam defaultFontStyle plain
skinparam monospace FONTNAME

' Plain 主题的基本样式
skinparam backgroundColor white
skinparam shadowing false
skinparam roundcorner 0
skinparam borderColor black
skinparam arrowColor black

' 组件样式
skinparam component {
    BackgroundColor white
    BorderColor black
    FontName FONTNAME
}

' 包样式
skinparam package {
    BackgroundColor white
    BorderColor black
    FontName FONTNAME
}

' 类样式
skinparam class {
    BackgroundColor white
    BorderColor black
    FontName FONTNAME
}

' 注释样式
skinparam note {
    BackgroundColor #FFFFCC
    BorderColor black
    FontName FONTNAME
}

' 数据库样式
skinparam database {
    BackgroundColor white
    BorderColor black
    FontName FONTNAME
}

' 云样式
skinparam cloud {
    BackgroundColor white
    BorderColor black
    FontName FONTNAME
}

' Actor 样式
skinparam actor {
    BackgroundColor white
    BorderColor black
    FontName FONTNAME
}