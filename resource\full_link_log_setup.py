import logging
from logging.handlers import TimedRotatingFileHandler

def setup_logging():

    full_link_log = logging.getLogger('full_link_log')

    # 设置日志级别
    full_link_log.setLevel(logging.INFO)

    # 创建处理器
    file_handler = TimedRotatingFileHandler('logs/full_link.log', when='midnight', interval=1, encoding='utf-8')

    file_handler.suffix = "%Y-%m-%d"

    # 创建日志格式器
    formatter = logging.Formatter('%(message)s')

    # 将格式器添加到处理器
    file_handler.setFormatter(formatter)

    # 将处理器添加到日志记录器
    full_link_log.addHandler(file_handler)
