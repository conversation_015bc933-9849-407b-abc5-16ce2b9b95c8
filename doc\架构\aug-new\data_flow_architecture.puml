@startuml ZTE产品检索系统数据流架构图
!theme plain
title ZTE产品检索系统 - 数据流架构图

skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 11
skinparam componentStyle rectangle

' 数据源层
package "**数据源层**" as DataSourceLayer #E8F5E8 {
    component "用户输入数据" as UserInput #C8E6C9 {
        [查询文本]
        [历史对话]
        [用户工号]
        [会话UUID]
    }
    
    component "配置数据源" as ConfigDataSource #C8E6C9 {
        [Apollo配置]
        [本地配置文件]
        [环境变量]
        [加密密钥]
    }
    
    component "外部API数据" as ExternalAPIData #C8E6C9 {
        [UAC认证数据]
        [LLM响应数据]
        [向量化数据]
        [重排序数据]
    }
}

' 数据处理层
package "**数据处理层**" as DataProcessingLayer #FFF3E0 {
    component "输入数据处理" as InputProcessing #FFE0B2 {
        [参数验证]
        [数据清洗]
        [格式标准化]
        [编码转换]
    }
    
    component "文本处理引擎" as TextProcessing #FFE0B2 {
        [分词处理]
        [语言检测]
        [实体提取]
        [查询重写]
    }
    
    component "向量化处理" as VectorProcessing #FFE0B2 {
        [文本向量化]
        [向量标准化]
        [维度映射]
        [相似度计算]
    }
    
    component "结果处理引擎" as ResultProcessing #FFE0B2 {
        [结果合并]
        [去重过滤]
        [排序算法]
        [阈值判断]
    }
}

' 数据存储层
package "**数据存储层**" as DataStorageLayer #E3F2FD {
    database "ElasticSearch" as ESDatabase #BBDEFB {
        [产品文档索引]
        [倒排索引]
        [分词索引]
        [聚合数据]
    }
    
    database "Milvus向量库" as MilvusDatabase #BBDEFB {
        [文档向量]
        [向量索引]
        [相似度矩阵]
        [分区数据]
    }
    
    database "Nebula图数据库" as NebulaDatabase #BBDEFB {
        [实体节点]
        [关系边]
        [属性数据]
        [路径索引]
    }
    
    storage "缓存存储" as CacheStorage #BBDEFB {
        [查询缓存]
        [结果缓存]
        [配置缓存]
        [会话缓存]
    }
}

' 数据输出层
package "**数据输出层**" as DataOutputLayer #F3E5F5 {
    component "响应数据组装" as ResponseAssembly #E1BEE7 {
        [答案生成]
        [元数据添加]
        [格式化处理]
        [流式封装]
    }
    
    component "监控数据输出" as MonitoringOutput #E1BEE7 {
        [性能指标]
        [召回统计]
        [错误日志]
        [分析报告]
    }
    
    component "日志数据输出" as LoggingOutput #E1BEE7 {
        [访问日志]
        [业务日志]
        [错误日志]
        [审计日志]
    }
}

' 数据流向连接
UserInput --> InputProcessing : 原始输入数据
ConfigDataSource --> InputProcessing : 配置数据加载
ExternalAPIData --> InputProcessing : 外部数据获取

InputProcessing --> TextProcessing : 清洗后数据
TextProcessing --> VectorProcessing : 文本处理结果
VectorProcessing --> ResultProcessing : 向量化结果

' 数据库查询流
TextProcessing --> ESDatabase : 文本查询
VectorProcessing --> MilvusDatabase : 向量查询
TextProcessing --> NebulaDatabase : 图谱查询

ESDatabase --> ResultProcessing : ES检索结果
MilvusDatabase --> ResultProcessing : 向量检索结果
NebulaDatabase --> ResultProcessing : 图谱检索结果

' 缓存数据流
InputProcessing --> CacheStorage : 查询缓存
ResultProcessing --> CacheStorage : 结果缓存
CacheStorage --> ResultProcessing : 缓存命中

' 输出数据流
ResultProcessing --> ResponseAssembly : 处理结果
ResponseAssembly --> MonitoringOutput : 监控数据
ResponseAssembly --> LoggingOutput : 日志数据

' 详细数据流说明
rectangle "**数据流详细说明**" as DataFlowDetails #F5F5F5 {
    note as DataFlowNote
        **主要数据流路径**
        
        **1. 查询数据流**
        用户查询 → 参数验证 → 文本预处理 → 实体提取 
        → 向量化 → 多路检索 → 结果融合 → 答案生成
        
        **2. 检索数据流**
        - ES流: 查询文本 → 分词 → 倒排索引查询 → 文档结果
        - Milvus流: 查询向量 → 相似度计算 → 向量结果
        - KG流: 实体查询 → 图谱遍历 → 关系结果
        
        **3. 配置数据流**
        Apollo配置 → 配置解密 → 本地缓存 → 业务使用
        
        **4. 监控数据流**
        业务操作 → 指标收集 → 数据聚合 → 报告生成
        
        **5. 日志数据流**
        系统事件 → 日志格式化 → 文件写入 → 日志轮转
    end note
}

' 数据格式说明
rectangle "**数据格式规范**" as DataFormatSpecs #FCE4EC {
    note as FormatNote
        **主要数据格式**
        
        **输入数据格式**
        ```json
        {
            "text": "用户查询文本",
            "history": [{"role": "user", "content": "历史对话"}],
            "chatUuid": "会话唯一标识",
            "rewriteText": "重写查询文本"
        }
        ```
        
        **向量数据格式**
        - 维度: 1024维浮点数组
        - 范围: [-1.0, 1.0]
        - 相似度: 余弦相似度
        
        **检索结果格式**
        ```json
        {
            "content": "文档内容",
            "doc_name": "文档名称", 
            "score": 0.95,
            "source": "es|milvus|kg"
        }
        ```
        
        **输出数据格式**
        ```json
        {
            "code": "0000",
            "message": "success",
            "data": {
                "answer": "生成的答案",
                "sources": ["参考来源"]
            }
        }
        ```
    end note
}

' 数据质量保证
rectangle "**数据质量保证**" as DataQualityAssurance #E0F2F1 {
    component "数据验证" as DataValidation #B2DFDB {
        [输入参数验证]
        [数据类型检查]
        [格式规范验证]
        [业务规则验证]
    }
    
    component "数据清洗" as DataCleaning #B2DFDB {
        [特殊字符过滤]
        [编码标准化]
        [空值处理]
        [异常数据处理]
    }
    
    component "数据监控" as DataMonitoring #B2DFDB {
        [数据完整性检查]
        [数据一致性验证]
        [数据质量评估]
        [异常数据告警]
    }
}

' 数据安全
rectangle "**数据安全保护**" as DataSecurity #FFF8E1 {
    component "数据加密" as DataEncryption #FFECB3 {
        [传输加密(HTTPS)]
        [存储加密]
        [配置加密]
        [密钥管理]
    }
    
    component "访问控制" as AccessControl #FFECB3 {
        [身份认证]
        [权限验证]
        [数据脱敏]
        [审计日志]
    }
}

' 数据质量和安全连接
InputProcessing --> DataValidation : 数据验证
DataValidation --> DataCleaning : 验证通过
DataCleaning --> DataMonitoring : 清洗完成

InputProcessing --> DataEncryption : 加密处理
DataEncryption --> AccessControl : 安全验证

' 性能优化数据流
rectangle "**性能优化策略**" as PerformanceOptimization #E8EAF6 {
    component "缓存策略" as CachingStrategy #C5CAE9 {
        [查询结果缓存]
        [向量缓存]
        [配置缓存]
        [会话状态缓存]
    }

    component "并行处理" as ParallelProcessing #C5CAE9 {
        [多路检索并行]
        [批量向量化]
        [异步处理]
        [流水线处理]
    }

    component "数据预处理" as DataPreprocessing #C5CAE9 {
        [索引预构建]
        [向量预计算]
        [热点数据预加载]
        [查询预处理]
    }
}

' 性能优化连接
ResultProcessing --> CachingStrategy : 结果缓存
VectorProcessing --> ParallelProcessing : 并行向量化
ESDatabase --> DataPreprocessing : 索引优化

@enduml
