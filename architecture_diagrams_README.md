# ZTE AgenticRAG 系统架构图说明

## 概述
本目录包含了ZTE AgenticRAG产品技术问答系统的PlantUML架构图，从不同视角展示了系统的设计和实现。

## 架构图文件

### 1. system_architecture.puml - 详细系统架构图
**描述**：完整展示系统所有组件、模块和它们之间的关系
**特点**：
- 包含所有系统组件的详细信息
- 展示组件间的连接关系和数据流
- 包含外部服务和存储系统
- 提供颜色编码的图例说明

**适用场景**：
- 系统整体设计评审
- 技术文档编写
- 新成员培训
- 架构优化讨论

### 2. system_architecture_simplified.puml - 简化时序图
**描述**：以时序图形式展示系统的主要处理流程
**特点**：
- 清晰的请求处理流程
- 并行检索机制展示
- 条件分支处理逻辑
- 核心组件交互顺序

**适用场景**：
- 理解系统工作流程
- 性能优化分析
- 接口设计讨论
- 问题排查定位

### 3. system_architecture_layers.puml - 分层架构图
**描述**：展示系统的分层设计和各层职责
**特点**：
- 七层架构设计
- 每层的核心组件
- 层次间的依赖关系
- 清晰的职责划分

**适用场景**：
- 架构设计理解
- 模块职责划分
- 代码组织规划
- 系统扩展设计

## 如何查看架构图

### 方法1：使用PlantUML插件（推荐）
1. 在VS Code中安装PlantUML插件
2. 打开.puml文件
3. 按`Alt+D`预览图形

### 方法2：在线渲染
访问 [PlantUML在线编辑器](http://www.plantuml.com/plantuml/uml/)，将.puml文件内容复制粘贴即可查看

### 方法3：命令行生成
```bash
# 安装PlantUML
java -jar plantuml.jar system_architecture.puml
```

## 系统核心特性

### 技术栈
- **Web框架**: Flask + Flask-CORS
- **数据库**: ElasticSearch + Milvus + Nebula Graph
- **AI模型**: BGE-M3 (嵌入/重排序) + NebulaBiz LLM
- **配置管理**: Apollo配置中心
- **认证**: UAC统一认证中心

### 核心功能
1. **多路召回融合**：ES文本检索 + Milvus向量检索 + 知识图谱检索
2. **智能重写**：支持多轮对话的查询理解和重写
3. **产品感知**：识别产品型号，支持版本特定检索
4. **智能重排序**：多维度相似度计算和阈值控制
5. **流式生成**：基于LLM的答案生成和流式输出

### 数据流程
```
用户查询 → 认证 → 预处理 → 实体识别 → 检索策略选择 → 
多路召回 → 结果融合 → 重排序 → LLM生成 → 返回答案
```

## 系统优势
- **模块化设计**：组件解耦，易于维护和扩展
- **智能路由**：根据查询特征选择最优检索策略
- **容错机制**：产品→系列→全局的递进式检索
- **版本管理**：支持产品版本特定的文档检索
- **性能优化**：并行检索、结果缓存、流式输出

## 更新说明
- 创建日期：2025-08-06
- 基于代码版本：release分支
- 架构图版本：1.0

## 联系方式
如有问题或需要更新架构图，请联系系统架构师或开发团队。