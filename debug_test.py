import sys
import traceback

# 配置代理绕过
from proxy_config import configure_proxy_bypass
configure_proxy_bypass()

try:
    print("1. 测试配置加载...")
    from domain.config.zxtech_config import config
    print("OK 配置加载成功")

    print("2. 测试Embedding初始化...")
    from embedding.get_embedding import GetEmbedding
    getembedding = GetEmbedding(config)
    print("OK Embedding初始化成功")

    print("3. 测试Embedding调用...")
    test_text = "测试文本"
    result = getembedding.post_url_m3(test_text)
    print(f"OK Embedding调用成功，结果长度: {len(result) if result else 'None'}")

    print("4. 测试ES召回...")
    from retrieve.Recall_data import RecallResult
    es_recall = RecallResult(config)
    print("OK ES召回初始化成功")

    print("5. 测试实体提取...")
    from entity.catch_body import dp_entity
    entity = dp_entity(config)
    print("OK 实体提取初始化成功")

    print("所有组件测试通过！")

except Exception as e:
    print(f"ERROR 错误: {e}")
    print("\n完整错误信息:")
    traceback.print_exc()