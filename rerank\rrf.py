
import numpy as np

from domain.constants.Enums import SIM_THRESHOLD, RRF_K


# Started by AICoder, pid:pcf0ck921e3a5c0144440b27e036a12bc4c21205


def cosine_similarity(a, b):
    """
    计算两个向量的余弦相似度
    :param a: 向量a
    :param b: 向量b
    :return: 如果余弦相似度大于阈值，返回True，否则返回False
    """
    dot_product = np.dot(a, b)
    norm_a = np.linalg.norm(a)
    norm_b = np.linalg.norm(b)

    try:
        cos_sim  = dot_product / (norm_a * norm_b)
        cos_sim = np.around(cos_sim, decimals=5)
    except Exception as e:
        cos_sim = 0

    return cos_sim > SIM_THRESHOLD

# Ended by AICoder, pid:pcf0ck921e3a5c0144440b27e036a12bc4c21205


def rrf_python(es_results,vector_results,es_embedding_res,vec_embedding_res):
    rrf_scores = {}
    es_dic= {k:v for k,v in zip(es_results,es_embedding_res)}
    vec_dic = {k:v for k,v in zip(vector_results,vec_embedding_res)}
    # 计算ES结果的RRF得分
    for i, result1 in enumerate(es_results):
        rrf_scores[result1] = 1 / (RRF_K + i)
    rrf_scores_keys = list(rrf_scores.keys())
    # 计算向量库结果的RRF得分
    for i, result1 in enumerate(vector_results):
        # 检查result是否与rrf_scores中的任何键相似
        for key1 in rrf_scores_keys:
            if cosine_similarity(es_dic[key1], vec_dic[result1]):
                rrf_scores[key1] += 1 / (RRF_K + i)
                break
        else:
            rrf_scores[result1] = 1 / (RRF_K + i)

    # 根据RRF得分对结果进行排序
    sorted_results = sorted(rrf_scores.items(), key=lambda x: x[1], reverse=True)
    sorted_results2 = [item[0] for item in sorted_results]
    return sorted_results2
