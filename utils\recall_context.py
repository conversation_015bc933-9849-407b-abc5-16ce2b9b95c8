"""
召回上下文管理器
用于在service层和controller层之间传递召回数据
"""

import threading

# 使用线程本地存储，避免多线程问题
_thread_local = threading.local()

def set_recall_data(es_results=None, milvus_results=None, kg_results=None, rerank_results=None):
    """设置当前请求的召回数据"""
    if not hasattr(_thread_local, 'recall_data'):
        _thread_local.recall_data = {
            'es_results': [],
            'milvus_results': [],
            'kg_results': [],
            'rerank_results': []
        }
    
    if es_results is not None:
        _thread_local.recall_data['es_results'] = es_results
    if milvus_results is not None:
        _thread_local.recall_data['milvus_results'] = milvus_results
    if kg_results is not None:
        _thread_local.recall_data['kg_results'] = kg_results
    if rerank_results is not None:
        _thread_local.recall_data['rerank_results'] = rerank_results

def get_recall_data():
    """获取当前请求的召回数据"""
    if not hasattr(_thread_local, 'recall_data'):
        return {
            'es_results': [],
            'milvus_results': [],
            'kg_results': [],
            'rerank_results': []
        }
    return _thread_local.recall_data

def clear_recall_data():
    """清除当前请求的召回数据"""
    if hasattr(_thread_local, 'recall_data'):
        del _thread_local.recall_data