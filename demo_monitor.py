"""
监控功能演示脚本
通过调用FAQ接口生成监控数据，然后展示排行榜
"""

import requests
import json
import time
import random

# API配置
BASE_URL = "http://127.0.0.1:10023/zte-ibo-acm-productretrieve"
HEADERS = {
    'X-Emp-No': '0668001470',
    'X-Auth-Value': '6a9f6a6db72bfb1d0dd8ddbc2053d32f',
    'Content-Type': 'application/json'
}

# 测试查询列表
QUERIES = [
    "M6000-8S Plus的设备尺寸",
    "ZXR10 M6000如何配置VLAN？",
    "5G基站的功耗参数",
    "产品的最大传输速率是多少？",
    "M6000-8S Plus支持哪些网络协议？",
    "如何进行系统升级操作？",
    "告警信息如何查看和处理？",
    "设备的环境温度要求",
    "M6000系列的端口配置方法",
    "系统日志导出步骤"
]

def send_faq_query(text):
    """发送FAQ查询请求"""
    url = f"{BASE_URL}/faq"
    data = {"text": text}
    
    try:
        response = requests.post(url, headers=HEADERS, json=data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"[成功] 查询: {text[:30]}...")
            return True
        else:
            print(f"[失败] 查询失败，状态码: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"[错误] 请求异常: {e}")
        return False

def get_monitor_leaderboard(format_type='detailed'):
    """获取监控排行榜"""
    url = f"{BASE_URL}/recall-monitor/leaderboard"
    params = {'format': format_type}
    
    try:
        response = requests.get(url, params=params, timeout=5)
        if response.status_code == 200:
            result = response.json()
            if result['status'] == 'success':
                return result['data']
            else:
                print(f"[错误] 获取排行榜失败: {result}")
                return None
        else:
            print(f"[错误] 获取排行榜失败，状态码: {response.status_code}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"[错误] 请求异常: {e}")
        return None

def get_monitor_statistics():
    """获取监控统计报告"""
    url = f"{BASE_URL}/recall-monitor/statistics"
    
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            result = response.json()
            if result['status'] == 'success':
                return result['data']
            else:
                print(f"[错误] 获取统计报告失败: {result}")
                return None
        else:
            print(f"[错误] 获取统计报告失败，状态码: {response.status_code}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"[错误] 请求异常: {e}")
        return None

def get_recent_queries(limit=10):
    """获取最近的查询记录"""
    url = f"{BASE_URL}/recall-monitor/recent"
    params = {'limit': limit}
    
    try:
        response = requests.get(url, params=params, timeout=5)
        if response.status_code == 200:
            result = response.json()
            if result['status'] == 'success':
                return result['data']
            else:
                print(f"[错误] 获取最近查询失败: {result}")
                return None
        else:
            print(f"[错误] 获取最近查询失败，状态码: {response.status_code}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"[错误] 请求异常: {e}")
        return None

def run_demo():
    """运行演示"""
    print("=" * 80)
    print("                    召回监控功能演示")
    print("=" * 80)
    
    # 检查服务是否运行
    print("\n[1] 检查服务状态...")
    try:
        response = requests.get(f"{BASE_URL}/recall-monitor/leaderboard", params={'format': 'json'}, timeout=2)
        if response.status_code == 200:
            print("[OK] 监控服务正常运行")
        elif response.status_code == 503:
            print("[警告] 监控功能未启用，请确保服务已正确配置")
            return
        else:
            print(f"[警告] 监控服务响应异常: {response.status_code}")
    except:
        print("[错误] 无法连接到服务，请确保服务已启动在 http://127.0.0.1:10023")
        print("启动命令: python main.py")
        return
    
    # 发送多个查询请求
    print("\n[2] 发送测试查询...")
    print("-" * 40)
    
    success_count = 0
    for i, query in enumerate(QUERIES, 1):
        print(f"  [{i}/{len(QUERIES)}] ", end="")
        if send_faq_query(query):
            success_count += 1
        time.sleep(0.5)  # 避免请求过快
    
    print(f"\n成功发送 {success_count}/{len(QUERIES)} 个查询")
    
    # 等待一下让监控数据更新
    time.sleep(1)
    
    # 获取并显示排行榜
    print("\n[3] 获取召回源排行榜...")
    print("=" * 80)
    
    # 获取详细排行榜
    leaderboard = get_monitor_leaderboard('detailed')
    if leaderboard:
        print(leaderboard)
    
    # 获取JSON格式数据
    print("\n[4] 获取排行榜JSON数据...")
    print("-" * 40)
    
    leaderboard_json = get_monitor_leaderboard('json')
    if leaderboard_json:
        print(json.dumps(leaderboard_json, indent=2, ensure_ascii=False))
    
    # 获取最近查询记录
    print("\n[5] 最近5次查询的贡献度分析...")
    print("-" * 40)
    
    recent = get_recent_queries(5)
    if recent:
        for query in recent:
            print(f"\n查询ID: {query['query_id']}")
            print(f"查询文本: {query['query_text'][:50]}...")
            print(f"响应时间: {query['response_time']:.3f}秒")
            print(f"贡献度:")
            print(f"  - ES: {query['contributions']['es']['contribution_rate']:.1f}% "
                  f"(命中{query['contributions']['es']['hit_count']}次, "
                  f"Top1={query['contributions']['es']['top1_count']})")
            print(f"  - Milvus: {query['contributions']['milvus']['contribution_rate']:.1f}% "
                  f"(命中{query['contributions']['milvus']['hit_count']}次, "
                  f"Top1={query['contributions']['milvus']['top1_count']})")
            print(f"  - KG: {query['contributions']['kg']['contribution_rate']:.1f}% "
                  f"(命中{query['contributions']['kg']['hit_count']}次, "
                  f"Top1={query['contributions']['kg']['top1_count']})")
    
    # 获取统计报告
    print("\n[6] 获取详细统计报告...")
    print("=" * 80)
    
    stats = get_monitor_statistics()
    if stats:
        print(stats)
    
    print("\n" * 2)
    print("=" * 80)
    print("                    演示完成")
    print("=" * 80)
    print("\n提示：")
    print("1. 监控数据会自动保存，重启服务后仍可查看历史数据")
    print("2. 可以通过浏览器访问以下API查看数据：")
    print(f"   - {BASE_URL}/recall-monitor/leaderboard?format=detailed")
    print(f"   - {BASE_URL}/recall-monitor/statistics")
    print(f"   - {BASE_URL}/recall-monitor/recent?limit=10")
    print("3. 每次FAQ查询都会自动记录监控数据")

if __name__ == "__main__":
    print("\n召回监控演示程序")
    print("本程序将：")
    print("1. 发送多个FAQ查询请求")
    print("2. 展示各路召回的贡献度排行榜")
    print("3. 显示详细的统计分析")
    print("\n请确保服务已启动在 http://127.0.0.1:10023")
    print("-" * 40)
    print("\n开始演示...")
    
    run_demo()