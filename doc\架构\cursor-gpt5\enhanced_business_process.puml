@startuml ZTE 产品检索系统增强版业务流程图
!theme vibrant

title ZTE 产品检索系统 - 增强版业务流程时序图

skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 12
skinparam ArrowColor #2E86C1
skinparam ArrowThickness 2

actor 用户 as User
participant 网关 as Gateway
participant Flask as Flask
participant 控制器 as Controller
participant 核心编排 as Core
participant 重写服务 as Rewrite
participant 检索引擎 as Retriever
participant 融合 as Fusion
participant 重排 as Rerank
participant 质量门禁 as Quality
participant 生成器 as Generator
participant LLM as LLM

autonumber

User -> Gateway : 1. HTTPS 请求 /faq
Gateway -> Flask : 2. 反向代理/转发
Flask -> Controller : 3. 鉴权/参数校验
Controller -> Core : 4. 发起业务编排
Core -> Core : 5. 预处理/实体识别
alt 需要重写
  Core -> Rewrite : 6. 调用查询重写
  Rewrite -> LLM : 7. LLM重写
  LLM --> Rewrite : 8. 重写结果
  Rewrite --> Core : 9. 新查询返回
end
Core -> Retriever : 10. 策略路由&并行召回
par ES/Milvus/KG
  Retriever -> Retriever : 10.1 ES检索
  Retriever -> Retriever : 10.2 向量检索
  Retriever -> Retriever : 10.3 图谱检索
end
Retriever --> Fusion : 11. 收集候选
Fusion -> Rerank : 12. RRF融合后TopN
Rerank -> Quality : 13. 去重/阈值
Quality -> Generator : 14. 构造提示词/生成
Generator -> LLM : 15. 调用生成
LLM --> Generator : 16. 流式答案分片
Generator --> Controller : 17. 回传(流式SSE)
Controller --> Flask : 18. 统一包装
Flask --> User : 19. 返回响应

group 异常与降级
  note right
    - LLM超时: 启用较短答案模板
    - ES失败: 退化到向量+KG
    - 向量失败: 退化到 ES
    - 全部失败: 返回FAQ兜底提示
  end note
end

@enduml


