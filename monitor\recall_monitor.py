"""
召回监控模块 - 实时监控各路召回的贡献度和排名
"""

import json
import time
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from collections import defaultdict, deque
from dataclasses import dataclass, field
from pathlib import Path
import threading
import pickle

@dataclass
class RecallMetrics:
    """召回指标数据类"""
    recall_count: int = 0  # 召回数量
    hit_count: int = 0  # 命中数量
    top1_count: int = 0  # Top1命中次数
    top3_count: int = 0  # Top3命中次数
    top5_count: int = 0  # Top5命中次数
    total_score: float = 0.0  # 总得分
    avg_position: float = 0.0  # 平均排名位置
    contribution_rate: float = 0.0  # 贡献率
    last_update: str = ""  # 最后更新时间

@dataclass
class QueryRecord:
    """查询记录"""
    query_id: str
    query_text: str
    timestamp: str
    es_metrics: RecallMetrics = field(default_factory=RecallMetrics)
    milvus_metrics: RecallMetrics = field(default_factory=RecallMetrics)
    kg_metrics: RecallMetrics = field(default_factory=RecallMetrics)
    rerank_count: int = 0
    response_time: float = 0.0

class RecallMonitor:
    """召回监控器 - 实时监控和统计各路召回的贡献"""
    
    def __init__(self, save_path: str = "./monitor_data"):
        self.save_path = Path(save_path)
        self.save_path.mkdir(exist_ok=True)
        
        # 总体统计
        self.global_stats = {
            'es': RecallMetrics(),
            'milvus': RecallMetrics(),
            'kg': RecallMetrics()
        }
        
        # 实时查询记录（保留最近1000条）
        self.query_history = deque(maxlen=1000)
        
        # 时间窗口统计（按小时）
        self.hourly_stats = defaultdict(lambda: {
            'es': RecallMetrics(),
            'milvus': RecallMetrics(),
            'kg': RecallMetrics()
        })
        
        # 召回源排行榜
        self.leaderboard = []
        
        # 统计开始时间
        self.start_time = datetime.now()
        
        # 查询计数器
        self.query_counter = 0
        
        # 锁，用于线程安全
        self.lock = threading.Lock()
        
        # 加载历史数据
        self._load_history()
    
    def record_query(self, 
                    query: str,
                    es_results: List[Dict],
                    milvus_results: List[Dict],
                    kg_results: List,
                    rerank_results: List[Dict],
                    response_time: float = 0.0) -> str:
        """
        记录一次查询的召回情况
        
        Args:
            query: 查询文本
            es_results: ES召回结果
            milvus_results: Milvus召回结果
            kg_results: KG召回结果
            rerank_results: 重排序后的结果
            response_time: 响应时间
            
        Returns:
            query_id: 查询ID
        """
        with self.lock:
            # 生成查询ID
            self.query_counter += 1
            query_id = f"Q{self.query_counter:06d}"
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 创建查询记录
            record = QueryRecord(
                query_id=query_id,
                query_text=query[:100],  # 截取前100字符
                timestamp=timestamp,
                rerank_count=len(rerank_results),
                response_time=response_time
            )
            
            # 分析各路召回的贡献
            self._analyze_recall_contribution(record, es_results, milvus_results, 
                                             kg_results, rerank_results)
            
            # 更新全局统计
            self._update_global_stats(record)
            
            # 更新小时统计
            hour_key = datetime.now().strftime("%Y-%m-%d %H:00")
            self._update_hourly_stats(hour_key, record)
            
            # 添加到历史记录
            self.query_history.append(record)
            
            # 更新排行榜
            self._update_leaderboard()
            
            # 定期保存数据
            if self.query_counter % 10 == 0:
                self._save_history()
            
            return query_id
    
    def _analyze_recall_contribution(self, 
                                    record: QueryRecord,
                                    es_results: List[Dict],
                                    milvus_results: List[Dict],
                                    kg_results: List,
                                    rerank_results: List[Dict]):
        """分析各路召回的贡献"""
        
        # 创建内容到源的映射
        content_to_source = defaultdict(set)
        
        # ES结果映射
        for item in es_results:
            content = item.get('content', '')
            if content:
                content_to_source[content].add('es')
        
        # Milvus结果映射
        for item in milvus_results:
            content = item.get('content', '')
            if content:
                content_to_source[content].add('milvus')
        
        # KG结果映射（KG结果可能是字符串）
        if kg_results and len(kg_results) > 0:
            if isinstance(kg_results[0], str):
                content_to_source[kg_results[0]].add('kg')
        
        # 统计各源在重排序结果中的表现
        for idx, item in enumerate(rerank_results):
            content = item.get('content', '')
            if content in content_to_source:
                sources = content_to_source[content]
                position = idx + 1
                
                # 计算得分（位置越靠前得分越高）
                score = 1.0 / position if position > 0 else 0
                
                for source in sources:
                    metrics = getattr(record, f"{source}_metrics")
                    metrics.hit_count += 1
                    metrics.total_score += score
                    
                    if position == 1:
                        metrics.top1_count += 1
                    if position <= 3:
                        metrics.top3_count += 1
                    if position <= 5:
                        metrics.top5_count += 1
        
        # 更新召回数量和贡献率
        record.es_metrics.recall_count = len(es_results)
        record.milvus_metrics.recall_count = len(milvus_results)
        record.kg_metrics.recall_count = len(kg_results) if kg_results else 0
        
        # 计算贡献率
        total_hits = record.es_metrics.hit_count + record.milvus_metrics.hit_count + record.kg_metrics.hit_count
        if total_hits > 0:
            record.es_metrics.contribution_rate = (record.es_metrics.hit_count / total_hits) * 100
            record.milvus_metrics.contribution_rate = (record.milvus_metrics.hit_count / total_hits) * 100
            record.kg_metrics.contribution_rate = (record.kg_metrics.hit_count / total_hits) * 100
    
    def _update_global_stats(self, record: QueryRecord):
        """更新全局统计"""
        for source in ['es', 'milvus', 'kg']:
            record_metrics = getattr(record, f"{source}_metrics")
            global_metrics = self.global_stats[source]
            
            global_metrics.recall_count += record_metrics.recall_count
            global_metrics.hit_count += record_metrics.hit_count
            global_metrics.top1_count += record_metrics.top1_count
            global_metrics.top3_count += record_metrics.top3_count
            global_metrics.top5_count += record_metrics.top5_count
            global_metrics.total_score += record_metrics.total_score
            global_metrics.last_update = record.timestamp
            
            # 更新平均贡献率
            if self.query_counter > 0 and len(self.query_history) > 0:
                total_contribution = sum(
                    getattr(r, f"{source}_metrics").contribution_rate 
                    for r in self.query_history
                )
                global_metrics.contribution_rate = total_contribution / len(self.query_history)
    
    def _update_hourly_stats(self, hour_key: str, record: QueryRecord):
        """更新小时统计"""
        for source in ['es', 'milvus', 'kg']:
            record_metrics = getattr(record, f"{source}_metrics")
            hourly_metrics = self.hourly_stats[hour_key][source]
            
            hourly_metrics.recall_count += record_metrics.recall_count
            hourly_metrics.hit_count += record_metrics.hit_count
            hourly_metrics.top1_count += record_metrics.top1_count
            hourly_metrics.top3_count += record_metrics.top3_count
            hourly_metrics.top5_count += record_metrics.top5_count
            hourly_metrics.total_score += record_metrics.total_score
            hourly_metrics.last_update = record.timestamp
    
    def _update_leaderboard(self):
        """更新排行榜"""
        self.leaderboard = []
        
        for source, metrics in self.global_stats.items():
            self.leaderboard.append({
                'source': source.upper(),
                'total_score': metrics.total_score,
                'contribution_rate': metrics.contribution_rate,
                'top1_count': metrics.top1_count,
                'top3_count': metrics.top3_count,
                'top5_count': metrics.top5_count,
                'hit_count': metrics.hit_count,
                'recall_count': metrics.recall_count
            })
        
        # 按总得分排序
        self.leaderboard.sort(key=lambda x: x['total_score'], reverse=True)
    
    def get_leaderboard(self, format_type: str = 'simple') -> str:
        """
        获取排行榜
        
        Args:
            format_type: 格式类型 ('simple', 'detailed', 'json')
        """
        if format_type == 'json':
            return json.dumps(self.leaderboard, indent=2, ensure_ascii=False)
        
        # 构建文本格式排行榜
        board = []
        board.append("=" * 80)
        board.append("                    召回源贡献度排行榜")
        board.append("=" * 80)
        board.append(f"统计时间: {self.start_time.strftime('%Y-%m-%d %H:%M')} - {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        board.append(f"总查询次数: {self.query_counter}")
        board.append("-" * 80)
        
        if format_type == 'detailed':
            board.append(f"{'排名':<4} {'召回源':<8} {'总得分':<10} {'贡献率':<10} {'Top1':<6} {'Top3':<6} {'Top5':<6} {'命中':<8} {'召回':<8}")
            board.append("-" * 80)
            
            for idx, item in enumerate(self.leaderboard, 1):
                medal = "[1]" if idx == 1 else "[2]" if idx == 2 else "[3]" if idx == 3 else "   "
                board.append(
                    f"{medal}{idx:<2} {item['source']:<8} "
                    f"{item['total_score']:<10.2f} "
                    f"{item['contribution_rate']:<9.1f}% "
                    f"{item['top1_count']:<6} "
                    f"{item['top3_count']:<6} "
                    f"{item['top5_count']:<6} "
                    f"{item['hit_count']:<8} "
                    f"{item['recall_count']:<8}"
                )
        else:  # simple format
            for idx, item in enumerate(self.leaderboard, 1):
                medal = "[1]" if idx == 1 else "[2]" if idx == 2 else "[3]" if idx == 3 else "   "
                bar_length = int(item['contribution_rate'] / 2)  # 50个字符表示100%
                bar = '#' * bar_length + '-' * (50 - bar_length)
                
                board.append(f"\n{medal} 第{idx}名: {item['source']}")
                board.append(f"   贡献度: {bar} {item['contribution_rate']:.1f}%")
                board.append(f"   总得分: {item['total_score']:.2f} | Top1: {item['top1_count']} | Top3: {item['top3_count']} | Top5: {item['top5_count']}")
        
        board.append("=" * 80)
        
        # 添加实时趋势
        board.append("\n最近10次查询趋势:")
        recent_queries = list(self.query_history)[-10:]
        for q in recent_queries:
            board.append(f"  [{q.query_id}] {q.timestamp}")
            board.append(f"    ES: {q.es_metrics.contribution_rate:.1f}% | "
                        f"Milvus: {q.milvus_metrics.contribution_rate:.1f}% | "
                        f"KG: {q.kg_metrics.contribution_rate:.1f}%")
        
        return "\n".join(board)
    
    def get_statistics_report(self) -> str:
        """生成详细统计报告"""
        report = []
        report.append("=" * 80)
        report.append("                    📈 召回监控统计报告 📈")
        report.append("=" * 80)
        report.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"监控开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"总查询次数: {self.query_counter}")
        
        # 运行时长
        duration = datetime.now() - self.start_time
        hours = duration.total_seconds() / 3600
        report.append(f"运行时长: {hours:.2f} 小时")
        report.append(f"平均QPS: {self.query_counter / max(duration.total_seconds(), 1):.2f}")
        
        report.append("\n" + "=" * 80)
        report.append("                    各召回源详细统计")
        report.append("=" * 80)
        
        for source, metrics in self.global_stats.items():
            report.append(f"\n【{source.upper()}召回】")
            report.append(f"  总召回次数: {metrics.recall_count}")
            report.append(f"  命中次数: {metrics.hit_count}")
            report.append(f"  平均贡献率: {metrics.contribution_rate:.2f}%")
            report.append(f"  总得分: {metrics.total_score:.2f}")
            report.append(f"  Top1命中: {metrics.top1_count} 次")
            report.append(f"  Top3命中: {metrics.top3_count} 次")
            report.append(f"  Top5命中: {metrics.top5_count} 次")
            
            # 计算命中率
            if metrics.recall_count > 0:
                hit_rate = (metrics.hit_count / self.query_counter) * 100 if self.query_counter > 0 else 0
                report.append(f"  命中率: {hit_rate:.2f}%")
            
            report.append(f"  最后更新: {metrics.last_update}")
        
        # 添加小时统计摘要
        report.append("\n" + "=" * 80)
        report.append("                    小时统计摘要（最近24小时）")
        report.append("=" * 80)
        
        # 获取最近24小时的数据
        recent_hours = sorted(self.hourly_stats.keys())[-24:]
        for hour_key in recent_hours:
            stats = self.hourly_stats[hour_key]
            total_hits = sum(s.hit_count for s in stats.values())
            if total_hits > 0:
                report.append(f"\n{hour_key}:")
                for source, metrics in stats.items():
                    if metrics.hit_count > 0:
                        report.append(f"  {source.upper()}: 命中{metrics.hit_count}次, "
                                    f"Top1={metrics.top1_count}, "
                                    f"得分={metrics.total_score:.2f}")
        
        report.append("\n" + "=" * 80)
        return "\n".join(report)
    
    def _save_history(self):
        """保存历史数据（多格式）"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d')
            
            # 1. 保存为Markdown报告（人类可读）
            self._save_as_markdown(timestamp)
            
            # 2. 保存为JSON（方便程序处理）
            self._save_as_json(timestamp)
            
            # 3. 保存为CSV（方便Excel分析）
            self._save_as_csv(timestamp)
            
            # 4. 保存为PKL（保留完整Python对象，用于恢复）
            data = {
                'global_stats': self.global_stats,
                'query_counter': self.query_counter,
                'start_time': self.start_time,
                'hourly_stats': dict(self.hourly_stats),
                'leaderboard': self.leaderboard
            }
            
            save_file = self.save_path / f"monitor_data_{timestamp}.pkl"
            with open(save_file, 'wb') as f:
                pickle.dump(data, f)
                
        except Exception as e:
            print(f"保存监控数据失败: {e}")
    
    def _save_as_markdown(self, timestamp):
        """保存为Markdown格式报告"""
        md_file = self.save_path / f"monitor_report_{timestamp}.md"
        
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write("# 召回监控报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**监控周期**: {self.start_time.strftime('%Y-%m-%d %H:%M')} - {datetime.now().strftime('%Y-%m-%d %H:%M')}\n\n")
            f.write(f"**总查询次数**: {self.query_counter}\n\n")
            
            # 排行榜
            f.write("## 📊 召回源贡献度排行榜\n\n")
            f.write("| 排名 | 召回源 | 贡献率 | 总得分 | Top1 | Top3 | Top5 | 总命中 |\n")
            f.write("|------|--------|--------|--------|------|------|------|--------|\n")
            
            for idx, item in enumerate(self.leaderboard, 1):
                medal = "🥇" if idx == 1 else "🥈" if idx == 2 else "🥉" if idx == 3 else str(idx)
                f.write(f"| {medal} | {item['source']} | {item['contribution_rate']:.1f}% | "
                       f"{item['total_score']:.2f} | {item['top1_count']} | "
                       f"{item['top3_count']} | {item['top5_count']} | {item['hit_count']} |\n")
            
            # 详细统计
            f.write("\n## 📈 详细统计\n\n")
            for source, metrics in self.global_stats.items():
                f.write(f"### {source.upper()}召回\n")
                f.write(f"- **总召回次数**: {metrics.recall_count}\n")
                f.write(f"- **命中次数**: {metrics.hit_count}\n")
                f.write(f"- **平均贡献率**: {metrics.contribution_rate:.2f}%\n")
                f.write(f"- **总得分**: {metrics.total_score:.2f}\n")
                f.write(f"- **Top1/3/5命中**: {metrics.top1_count}/{metrics.top3_count}/{metrics.top5_count}\n\n")
            
            # 最近查询趋势
            f.write("## 📉 最近10次查询趋势\n\n")
            f.write("| 查询ID | 时间 | ES贡献 | Milvus贡献 | KG贡献 |\n")
            f.write("|--------|------|--------|------------|--------|\n")
            
            recent = list(self.query_history)[-10:]
            for q in recent:
                f.write(f"| {q.query_id} | {q.timestamp} | "
                       f"{q.es_metrics.contribution_rate:.1f}% | "
                       f"{q.milvus_metrics.contribution_rate:.1f}% | "
                       f"{q.kg_metrics.contribution_rate:.1f}% |\n")
    
    def _save_as_json(self, timestamp):
        """保存为JSON格式"""
        json_file = self.save_path / f"monitor_data_{timestamp}.json"
        
        json_data = {
            'metadata': {
                'generated_at': datetime.now().isoformat(),
                'start_time': self.start_time.isoformat(),
                'query_count': self.query_counter
            },
            'leaderboard': self.leaderboard,
            'global_stats': {
                source: {
                    'recall_count': metrics.recall_count,
                    'hit_count': metrics.hit_count,
                    'top1_count': metrics.top1_count,
                    'top3_count': metrics.top3_count,
                    'top5_count': metrics.top5_count,
                    'total_score': metrics.total_score,
                    'contribution_rate': metrics.contribution_rate
                }
                for source, metrics in self.global_stats.items()
            },
            'recent_queries': [
                {
                    'query_id': q.query_id,
                    'timestamp': q.timestamp,
                    'es_contribution': q.es_metrics.contribution_rate,
                    'milvus_contribution': q.milvus_metrics.contribution_rate,
                    'kg_contribution': q.kg_metrics.contribution_rate
                }
                for q in list(self.query_history)[-20:]
            ]
        }
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
    
    def _save_as_csv(self, timestamp):
        """保存为CSV格式（排行榜数据）"""
        csv_file = self.save_path / f"monitor_leaderboard_{timestamp}.csv"
        
        with open(csv_file, 'w', encoding='utf-8') as f:
            # 写入头部
            f.write("召回源,贡献率(%),总得分,Top1命中,Top3命中,Top5命中,总命中,总召回\n")
            
            # 写入数据
            for item in self.leaderboard:
                source = item['source']
                metrics = self.global_stats.get(source.lower(), self.global_stats.get(source, None))
                if metrics:
                    f.write(f"{source},{item['contribution_rate']:.1f},{item['total_score']:.2f},"
                           f"{item['top1_count']},{item['top3_count']},{item['top5_count']},"
                           f"{item['hit_count']},{metrics.recall_count}\n")
    
    def _load_history(self):
        """加载历史数据"""
        try:
            today_file = self.save_path / f"monitor_data_{datetime.now().strftime('%Y%m%d')}.pkl"
            if today_file.exists():
                with open(today_file, 'rb') as f:
                    data = pickle.load(f)
                    self.global_stats = data.get('global_stats', self.global_stats)
                    self.query_counter = data.get('query_counter', 0)
                    self.start_time = data.get('start_time', self.start_time)
                    self.hourly_stats = defaultdict(lambda: {
                        'es': RecallMetrics(),
                        'milvus': RecallMetrics(),
                        'kg': RecallMetrics()
                    }, data.get('hourly_stats', {}))
                    self.leaderboard = data.get('leaderboard', [])
        except Exception as e:
            print(f"加载监控数据失败: {e}")
    
    def reset_statistics(self):
        """重置统计数据"""
        with self.lock:
            self.global_stats = {
                'es': RecallMetrics(),
                'milvus': RecallMetrics(),
                'kg': RecallMetrics()
            }
            self.query_history.clear()
            self.hourly_stats.clear()
            self.leaderboard = []
            self.query_counter = 0
            self.start_time = datetime.now()

# 全局监控实例
_monitor_instance = None

def get_monitor() -> RecallMonitor:
    """获取全局监控实例"""
    global _monitor_instance
    if _monitor_instance is None:
        _monitor_instance = RecallMonitor()
    return _monitor_instance

def print_leaderboard():
    """打印排行榜（用于测试）"""
    monitor = get_monitor()
    print(monitor.get_leaderboard('detailed'))
    
def print_statistics():
    """打印统计报告（用于测试）"""
    monitor = get_monitor()
    print(monitor.get_statistics_report())