# 产品技术问答系统实施指南

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-08-08
- **文档类型**: 技术实施指南
- **适用范围**: 开发团队技术实施参考

## 目录
1. [实施概述](#1-实施概述)
2. [方案一实施细节](#2-方案一实施细节)
3. [方案二实施细节](#3-方案二实施细节)
4. [关键技术实现](#4-关键技术实现)
5. [接口设计规范](#5-接口设计规范)
6. [数据库设计](#6-数据库设计)
7. [部署配置](#7-部署配置)
8. [测试策略](#8-测试策略)

## 1. 实施概述

### 1.1 技术栈选择

#### 1.1.1 后端技术栈
- **开发语言**: Java 17+ / Python 3.9+
- **框架**: Spring Boot 3.x / FastAPI
- **数据库**: PostgreSQL 14+ / MySQL 8.0+
- **缓存**: Redis 7.0+
- **消息队列**: RabbitMQ / Apache Kafka
- **搜索引擎**: Elasticsearch 8.x
- **向量数据库**: Milvus / Pinecone / Weaviate

#### 1.1.2 前端技术栈
- **框架**: Vue.js 3.x / React 18+
- **UI组件**: Element Plus / Ant Design
- **状态管理**: Pinia / Redux Toolkit
- **构建工具**: Vite / Webpack 5

#### 1.1.3 基础设施
- **容器化**: Docker + Kubernetes
- **服务网格**: Istio (可选)
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack
- **CI/CD**: GitLab CI / Jenkins

### 1.2 开发环境准备

#### 1.2.1 本地开发环境
```bash
# 安装必要工具
docker --version
kubectl version --client
helm version

# 创建开发命名空间
kubectl create namespace qa-dev

# 安装开发依赖
helm install redis bitnami/redis -n qa-dev
helm install postgresql bitnami/postgresql -n qa-dev
helm install elasticsearch elastic/elasticsearch -n qa-dev
```

#### 1.2.2 开发配置模板
```yaml
# application-dev.yml
server:
  port: 8080

spring:
  datasource:
    url: ******************************************
    username: ${DB_USERNAME:qa_user}
    password: ${DB_PASSWORD:qa_pass}
  
  redis:
    host: localhost
    port: 6379
    password: ${REDIS_PASSWORD:}
  
  elasticsearch:
    uris: http://localhost:9200
    username: ${ES_USERNAME:elastic}
    password: ${ES_PASSWORD:}

# AI服务配置
ai:
  model:
    embedding: bge-m3
    rerank: bge-reranker
    generation: qwen-plus
  
  vector-db:
    type: milvus
    host: localhost
    port: 19530
```

## 2. 方案一实施细节

### 2.1 DN Studio集成架构

#### 2.1.1 核心组件设计
```java
@Service
public class DNStudioIntegrationService {
    
    @Autowired
    private DNStudioClient dnStudioClient;
    
    @Autowired
    private KnowledgeGraphService kgService;
    
    /**
     * 从DN Studio知识库检索
     */
    public SearchResult searchFromDNStudio(SearchRequest request) {
        // 1. 问题预处理
        String processedQuery = preprocessQuery(request.getQuery());
        
        // 2. 调用DN Studio检索API
        DNStudioSearchRequest dnRequest = DNStudioSearchRequest.builder()
            .query(processedQuery)
            .topK(request.getTopK())
            .searchType(SearchType.HYBRID)
            .build();
            
        DNStudioSearchResponse dnResponse = dnStudioClient.search(dnRequest);
        
        // 3. 结果后处理
        return postprocessResults(dnResponse, request);
    }
    
    /**
     * 知识图谱增强检索
     */
    public SearchResult enhanceWithKnowledgeGraph(SearchResult baseResult, 
                                                 SearchRequest request) {
        // 1. 提取实体
        List<Entity> entities = extractEntities(request.getQuery());
        
        // 2. 图谱查询
        List<GraphResult> graphResults = kgService.queryRelatedEntities(entities);
        
        // 3. 结果融合
        return mergeResults(baseResult, graphResults);
    }
}
```

#### 2.1.2 数据同步服务
```java
@Component
@Slf4j
public class TSMSyncService {
    
    @Autowired
    private TSMClient tsmClient;
    
    @Autowired
    private DNStudioClient dnStudioClient;
    
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void syncTSMDocuments() {
        try {
            // 1. 获取TSM更新列表
            List<TSMDocument> updatedDocs = tsmClient.getUpdatedDocuments();
            
            // 2. 批量处理文档
            for (TSMDocument doc : updatedDocs) {
                processDocument(doc);
            }
            
            log.info("TSM同步完成，处理文档数量: {}", updatedDocs.size());
        } catch (Exception e) {
            log.error("TSM同步失败", e);
            // 发送告警
            alertService.sendAlert("TSM同步失败", e.getMessage());
        }
    }
    
    private void processDocument(TSMDocument doc) {
        // 1. 文档解析
        ParsedDocument parsed = documentParser.parse(doc);
        
        // 2. 质量检查
        if (!qualityChecker.isValid(parsed)) {
            log.warn("文档质量不合格，跳过: {}", doc.getId());
            return;
        }
        
        // 3. 上传到DN Studio
        DNStudioDocument dnDoc = convertToDNStudioFormat(parsed);
        dnStudioClient.uploadDocument(dnDoc);
        
        // 4. 更新同步状态
        updateSyncStatus(doc.getId(), SyncStatus.SUCCESS);
    }
}
```

### 2.2 检索优化实现

#### 2.2.1 混合检索策略
```java
@Service
public class HybridSearchService {
    
    @Autowired
    private DNStudioService dnStudioService;
    
    @Autowired
    private KnowledgeGraphService kgService;
    
    @Autowired
    private RerankService rerankService;
    
    public SearchResult hybridSearch(SearchRequest request) {
        // 1. 并行多路召回
        CompletableFuture<List<SearchResult>> dnStudioFuture = 
            CompletableFuture.supplyAsync(() -> 
                dnStudioService.search(request));
                
        CompletableFuture<List<SearchResult>> kgFuture = 
            CompletableFuture.supplyAsync(() -> 
                kgService.search(request));
        
        // 2. 等待所有结果
        CompletableFuture.allOf(dnStudioFuture, kgFuture).join();
        
        // 3. 结果合并
        List<SearchResult> allResults = new ArrayList<>();
        allResults.addAll(dnStudioFuture.get());
        allResults.addAll(kgFuture.get());
        
        // 4. 重排序
        return rerankService.rerank(allResults, request);
    }
}
```

### 2.3 权限集成

#### 2.3.1 DN Studio权限适配
```java
@Service
public class DNStudioPermissionAdapter {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private DNStudioClient dnStudioClient;
    
    public SearchResult filterByPermission(SearchResult result, User user) {
        // 1. 获取用户权限
        List<Permission> permissions = userService.getUserPermissions(user);
        
        // 2. 过滤结果
        List<SearchItem> filteredItems = result.getItems().stream()
            .filter(item -> hasPermission(item, permissions))
            .collect(Collectors.toList());
        
        // 3. 构建过滤后的结果
        return SearchResult.builder()
            .items(filteredItems)
            .total(filteredItems.size())
            .query(result.getQuery())
            .build();
    }
    
    private boolean hasPermission(SearchItem item, List<Permission> permissions) {
        // 检查用户是否有访问该文档的权限
        return permissions.stream()
            .anyMatch(permission -> 
                permission.getResourceType().equals(item.getResourceType()) &&
                permission.getSecurityLevel() >= item.getSecurityLevel());
    }
}
```

## 3. 方案二实施细节

### 3.1 多存储架构实现

#### 3.1.1 存储服务抽象
```java
public interface StorageService {
    void store(Document document);
    List<SearchResult> search(SearchRequest request);
    void delete(String documentId);
    void update(Document document);
}

@Service
public class ElasticsearchStorageService implements StorageService {
    
    @Autowired
    private ElasticsearchClient esClient;
    
    @Override
    public void store(Document document) {
        IndexRequest request = IndexRequest.of(i -> i
            .index("qa_documents")
            .id(document.getId())
            .document(document));
            
        esClient.index(request);
    }
    
    @Override
    public List<SearchResult> search(SearchRequest request) {
        SearchRequest esRequest = SearchRequest.of(s -> s
            .index("qa_documents")
            .query(q -> q
                .multiMatch(m -> m
                    .query(request.getQuery())
                    .fields("title", "content"))));
                    
        SearchResponse<Document> response = esClient.search(esRequest, Document.class);
        
        return convertToSearchResults(response);
    }
}

@Service
public class VectorStorageService implements StorageService {
    
    @Autowired
    private MilvusClient milvusClient;
    
    @Autowired
    private EmbeddingService embeddingService;
    
    @Override
    public void store(Document document) {
        // 1. 生成向量
        float[] vector = embeddingService.embed(document.getContent());
        
        // 2. 存储到向量数据库
        InsertParam insertParam = InsertParam.newBuilder()
            .withCollectionName("qa_vectors")
            .withFields(Arrays.asList(
                new InsertParam.Field("id", Arrays.asList(document.getId())),
                new InsertParam.Field("vector", Arrays.asList(vector)),
                new InsertParam.Field("metadata", Arrays.asList(document.getMetadata()))
            ))
            .build();
            
        milvusClient.insert(insertParam);
    }
    
    @Override
    public List<SearchResult> search(SearchRequest request) {
        // 1. 查询向量化
        float[] queryVector = embeddingService.embed(request.getQuery());
        
        // 2. 向量检索
        SearchParam searchParam = SearchParam.newBuilder()
            .withCollectionName("qa_vectors")
            .withMetricType(MetricType.COSINE)
            .withTopK(request.getTopK())
            .withVectors(Arrays.asList(queryVector))
            .build();
            
        SearchResults results = milvusClient.search(searchParam);
        
        return convertToSearchResults(results);
    }
}
```

### 3.2 多路召回实现

#### 3.2.1 召回策略管理
```java
@Service
public class MultiRecallService {
    
    @Autowired
    private ElasticsearchStorageService esService;
    
    @Autowired
    private VectorStorageService vectorService;
    
    @Autowired
    private KnowledgeGraphService kgService;
    
    public SearchResult multiRecall(SearchRequest request) {
        // 1. 并行召回
        List<CompletableFuture<List<SearchResult>>> futures = Arrays.asList(
            CompletableFuture.supplyAsync(() -> esService.search(request)),
            CompletableFuture.supplyAsync(() -> vectorService.search(request)),
            CompletableFuture.supplyAsync(() -> kgService.search(request))
        );
        
        // 2. 等待所有结果
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        // 3. 收集结果
        List<SearchResult> allResults = futures.stream()
            .map(CompletableFuture::join)
            .flatMap(List::stream)
            .collect(Collectors.toList());
        
        // 4. 去重和融合
        return deduplicateAndMerge(allResults, request);
    }
    
    private SearchResult deduplicateAndMerge(List<SearchResult> results, 
                                           SearchRequest request) {
        // 基于文档ID去重
        Map<String, SearchResult> uniqueResults = new LinkedHashMap<>();
        
        for (SearchResult result : results) {
            String docId = result.getDocumentId();
            if (!uniqueResults.containsKey(docId) || 
                result.getScore() > uniqueResults.get(docId).getScore()) {
                uniqueResults.put(docId, result);
            }
        }
        
        // 按分数排序
        List<SearchResult> sortedResults = uniqueResults.values().stream()
            .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
            .limit(request.getTopK())
            .collect(Collectors.toList());
        
        return SearchResult.builder()
            .items(sortedResults)
            .total(sortedResults.size())
            .query(request.getQuery())
            .build();
    }
}
```

### 3.3 数据一致性保障

#### 3.3.1 分布式事务管理
```java
@Service
@Transactional
public class DocumentSyncService {
    
    @Autowired
    private ElasticsearchStorageService esService;
    
    @Autowired
    private VectorStorageService vectorService;
    
    @Autowired
    private KnowledgeGraphService kgService;
    
    @Autowired
    private TransactionTemplate transactionTemplate;
    
    public void syncDocument(Document document) {
        try {
            // 1. 开始分布式事务
            transactionTemplate.execute(status -> {
                try {
                    // 2. 存储到ES
                    esService.store(document);
                    
                    // 3. 存储到向量数据库
                    vectorService.store(document);
                    
                    // 4. 更新知识图谱
                    kgService.updateGraph(document);
                    
                    // 5. 记录同步日志
                    logSyncSuccess(document.getId());
                    
                    return null;
                } catch (Exception e) {
                    // 6. 回滚操作
                    status.setRollbackOnly();
                    throw new RuntimeException("文档同步失败", e);
                }
            });
        } catch (Exception e) {
            // 7. 补偿操作
            compensateFailedSync(document);
            throw e;
        }
    }
    
    private void compensateFailedSync(Document document) {
        // 清理可能的部分数据
        try {
            esService.delete(document.getId());
            vectorService.delete(document.getId());
            kgService.deleteRelations(document.getId());
        } catch (Exception e) {
            log.error("补偿操作失败", e);
        }
    }
}
```

## 4. 关键技术实现

### 4.1 问题重写与查询扩展

#### 4.1.1 问题重写服务
```java
@Service
public class QueryRewriteService {
    
    @Autowired
    private LLMService llmService;
    
    @Autowired
    private ContextService contextService;
    
    public String rewriteQuery(String originalQuery, String sessionId) {
        // 1. 获取对话上下文
        ConversationContext context = contextService.getContext(sessionId);
        
        // 2. 构建重写提示
        String prompt = buildRewritePrompt(originalQuery, context);
        
        // 3. 调用LLM重写
        String rewrittenQuery = llmService.generate(prompt);
        
        // 4. 验证重写质量
        if (isValidRewrite(originalQuery, rewrittenQuery)) {
            return rewrittenQuery;
        } else {
            return originalQuery; // 降级到原始查询
        }
    }
    
    private String buildRewritePrompt(String query, ConversationContext context) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("基于以下对话历史，重写用户的最新问题，使其更加完整和明确：\n\n");
        
        // 添加历史对话
        for (ConversationTurn turn : context.getHistory()) {
            prompt.append("用户: ").append(turn.getQuestion()).append("\n");
            prompt.append("助手: ").append(turn.getAnswer()).append("\n\n");
        }
        
        prompt.append("当前问题: ").append(query).append("\n");
        prompt.append("重写后的问题: ");
        
        return prompt.toString();
    }
}
```

### 4.2 重排序算法实现

#### 4.2.1 多特征重排序
```java
@Service
public class RerankService {
    
    @Autowired
    private BGEReranker bgeReranker;
    
    @Autowired
    private TFIDFCalculator tfidfCalculator;
    
    public List<SearchResult> rerank(List<SearchResult> candidates, 
                                   SearchRequest request) {
        // 1. 计算多种特征分数
        for (SearchResult result : candidates) {
            // BGE重排分数
            double bgeScore = bgeReranker.score(request.getQuery(), result.getContent());
            
            // TF-IDF分数
            double tfidfScore = tfidfCalculator.calculate(request.getQuery(), result.getContent());
            
            // 权威性分数
            double authorityScore = calculateAuthorityScore(result);
            
            // 时效性分数
            double freshnessScore = calculateFreshnessScore(result);
            
            // 融合分数
            double finalScore = fusionScore(bgeScore, tfidfScore, authorityScore, freshnessScore);
            
            result.setFinalScore(finalScore);
        }
        
        // 2. 按最终分数排序
        return candidates.stream()
            .sorted((a, b) -> Double.compare(b.getFinalScore(), a.getFinalScore()))
            .collect(Collectors.toList());
    }
    
    private double fusionScore(double bgeScore, double tfidfScore, 
                              double authorityScore, double freshnessScore) {
        // 加权融合策略
        return 0.5 * bgeScore + 
               0.3 * tfidfScore + 
               0.15 * authorityScore + 
               0.05 * freshnessScore;
    }
}
```

### 4.3 答案生成与优化

#### 4.3.1 答案生成服务
```java
@Service
public class AnswerGenerationService {
    
    @Autowired
    private LLMService llmService;
    
    @Autowired
    private FactCheckService factCheckService;
    
    public GeneratedAnswer generateAnswer(String question, 
                                        List<SearchResult> context) {
        // 1. 构建生成提示
        String prompt = buildGenerationPrompt(question, context);
        
        // 2. 生成答案
        String rawAnswer = llmService.generate(prompt);
        
        // 3. 事实性验证
        FactCheckResult factCheck = factCheckService.verify(question, rawAnswer, context);
        
        // 4. 答案后处理
        String processedAnswer = postprocessAnswer(rawAnswer, factCheck);
        
        // 5. 添加引用
        List<Citation> citations = extractCitations(context);
        
        return GeneratedAnswer.builder()
            .answer(processedAnswer)
            .citations(citations)
            .confidence(factCheck.getConfidence())
            .sources(context)
            .build();
    }
    
    private String buildGenerationPrompt(String question, List<SearchResult> context) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("基于以下参考资料回答问题，要求准确、完整、有条理：\n\n");
        
        // 添加参考资料
        for (int i = 0; i < context.size(); i++) {
            SearchResult result = context.get(i);
            prompt.append(String.format("[%d] %s\n", i + 1, result.getContent()));
        }
        
        prompt.append("\n问题: ").append(question).append("\n");
        prompt.append("答案: ");
        
        return prompt.toString();
    }
}

## 5. 接口设计规范

### 5.1 RESTful API设计

#### 5.1.1 问答接口
```java
@RestController
@RequestMapping("/api/v1/qa")
@Validated
public class QAController {

    @Autowired
    private QAService qaService;

    /**
     * 问答接口
     */
    @PostMapping("/ask")
    public ResponseEntity<QAResponse> ask(@Valid @RequestBody QARequest request,
                                         HttpServletRequest httpRequest) {
        // 1. 获取用户信息
        User user = getCurrentUser(httpRequest);

        // 2. 参数验证
        validateRequest(request);

        // 3. 执行问答
        QAResponse response = qaService.ask(request, user);

        // 4. 返回结果
        return ResponseEntity.ok(response);
    }

    /**
     * 多轮对话接口
     */
    @PostMapping("/chat")
    public ResponseEntity<ChatResponse> chat(@Valid @RequestBody ChatRequest request,
                                           HttpServletRequest httpRequest) {
        User user = getCurrentUser(httpRequest);
        ChatResponse response = qaService.chat(request, user);
        return ResponseEntity.ok(response);
    }

    /**
     * 获取对话历史
     */
    @GetMapping("/history/{sessionId}")
    public ResponseEntity<List<ConversationTurn>> getHistory(
            @PathVariable String sessionId,
            HttpServletRequest httpRequest) {
        User user = getCurrentUser(httpRequest);
        List<ConversationTurn> history = qaService.getHistory(sessionId, user);
        return ResponseEntity.ok(history);
    }
}
```

#### 5.1.2 数据传输对象
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QARequest {

    @NotBlank(message = "问题不能为空")
    @Size(max = 1000, message = "问题长度不能超过1000字符")
    private String question;

    @Size(max = 100, message = "会话ID长度不能超过100字符")
    private String sessionId;

    @Min(value = 1, message = "返回结果数量至少为1")
    @Max(value = 20, message = "返回结果数量不能超过20")
    private Integer topK = 5;

    private Map<String, Object> context;

    private List<String> productTypes;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QAResponse {

    private String answer;

    private List<Citation> citations;

    private Double confidence;

    private Long responseTime;

    private String sessionId;

    private List<String> relatedQuestions;

    private Map<String, Object> metadata;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Citation {

    private String documentId;

    private String title;

    private String snippet;

    private String url;

    private Double relevanceScore;

    private String sourceType;
}
```

### 5.2 GraphQL接口设计

#### 5.2.1 Schema定义
```graphql
type Query {
    # 问答查询
    ask(input: QAInput!): QAResult!

    # 搜索文档
    searchDocuments(input: SearchInput!): SearchResult!

    # 获取对话历史
    getConversation(sessionId: String!): Conversation

    # 获取用户权限
    getUserPermissions: [Permission!]!
}

type Mutation {
    # 开始新对话
    startConversation: Conversation!

    # 结束对话
    endConversation(sessionId: String!): Boolean!

    # 反馈答案质量
    feedbackAnswer(input: FeedbackInput!): Boolean!
}

input QAInput {
    question: String!
    sessionId: String
    topK: Int = 5
    productTypes: [String!]
    context: JSON
}

type QAResult {
    answer: String!
    citations: [Citation!]!
    confidence: Float!
    responseTime: Int!
    sessionId: String!
    relatedQuestions: [String!]!
}

type Citation {
    documentId: String!
    title: String!
    snippet: String!
    url: String
    relevanceScore: Float!
    sourceType: String!
}
```
```
