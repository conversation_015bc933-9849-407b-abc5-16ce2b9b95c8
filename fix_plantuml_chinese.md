# PlantUML4IDE 中文乱码解决方案

## 问题原因
plantuml4ide 插件在处理中文字符时可能出现乱码，主要原因是 Java 环境的字体配置问题。

## 解决方案

### 方案1：修改插件配置（推荐）
1. 在 IDE 中找到 plantuml4ide 插件设置
2. 添加 JVM 参数：
   ```
   -Dfile.encoding=UTF-8
   -Dsun.jnu.encoding=UTF-8
   ```
3. 设置 PlantUML 配置文件路径，创建 `plantuml.cfg` 文件：
   ```
   skinparam defaultFontName "Microsoft YaHei"
   ```

### 方案2：添加 PlantUML 环境变量
在系统环境变量中添加：
```
PLANTUML_ENCODING=UTF-8
JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8
```

### 方案3：在 PlantUML 文件头部添加编码声明
```plantuml
@startuml
!pragma useVerticalIf on
' encoding: UTF-8
skinparam defaultFontName "Microsoft YaHei"
```

### 方案4：使用命令行导出（最可靠）
如果插件仍有问题，可以使用命令行：
```bash
java -Dfile.encoding=UTF-8 -jar plantuml.jar -charset UTF-8 technical_components_architecture.puml
```

### 方案5：更换为支持更好的字体
尝试以下字体（按优先级）：
1. Noto Sans CJK SC
2. Source Han Sans CN  
3. Microsoft YaHei
4. SimSun
5. WenQuanYi Micro Hei

## IDE 特定配置

### IntelliJ IDEA
1. Settings → Editor → File Encodings → 全部设为 UTF-8
2. Settings → plantuml4ide → Additional JVM arguments：`-Dfile.encoding=UTF-8`

### Visual Studio Code
1. 安装 PlantUML 插件
2. 在 settings.json 中添加：
   ```json
   "plantuml.java": "-Dfile.encoding=UTF-8",
   "plantuml.commandArgs": ["-charset", "UTF-8"]
   ```

## 验证方法
1. 先用简单的中文测试：
   ```plantuml
   @startuml
   title 测试中文
   Alice -> Bob: 你好
   @enduml
   ```
2. 如果简单测试通过，再测试复杂图表

## 注意事项
- 确保系统已安装中文字体
- Java 版本建议使用 8 或以上
- PlantUML 版本建议使用最新版