from Crypto.Random import get_random_bytes
from Crypto.Cipher import AES
import base64
from domain.constants.ConfigEnum import BY<PERSON>_LEN


def get_key_from_string(key_str):
    # 将字符串解码为字节
    return base64.b64decode(key_str)


def key_to_string(key_bytes):
    # 将字节编码为字符串
    return base64.b64encode(key_bytes).decode('utf-8')


def encrypt(key:str, text:str) -> str:
    key = get_key_from_string(key)
    # Generate a random nonce with 8 bytes length
    nonce = get_random_bytes(BYTE_LEN)

    # Create AES cipher in CTR mode
    cipher = AES.new(key, AES.MODE_GCM, nonce=nonce)

    # Encrypt the plaintext
    ciphertext = cipher.encrypt(text.encode('utf-8'))

    # Return the nonce and ciphertext, both encoded in base64
    return base64.b64encode(nonce + ciphertext).decode('utf-8')

def decrypt(cipher_text:str, key:str) -> str:
    try:
        key = get_key_from_string(key)
        # Decode the base64 encoded message
        decoded_message = base64.b64decode(cipher_text)

        # Extract the nonce (first 8 bytes)
        nonce = decoded_message[:BYTE_LEN]

        # Extract the ciphertext (remaining bytes)
        ciphertext = decoded_message[BYTE_LEN:]

        # Create AES cipher in CTR mode with the same nonce
        cipher = AES.new(key, AES.MODE_GCM, nonce=nonce)

        # Decrypt the ciphertext
        plaintext = cipher.decrypt(ciphertext)

        # Return the plaintext as a string
        return plaintext.decode('utf-8') or cipher_text
    except:
        return cipher_text

# 生成密钥
def generate_random_key():
    # 生成指定长度的随机密钥
    key_bytes = get_random_bytes(32)
    return key_to_string(key_bytes)


