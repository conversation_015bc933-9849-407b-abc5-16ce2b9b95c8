import time
from hashlib import sha256
import hmac
from urllib.parse import quote


# 生成generateSignature的函数
def create_signature(environment, access_key, access_secret):
    t = time.time()
    # 毫秒级时间戳
    time_stamp = int(round(t * 1000))
    key = access_secret.encode('utf-8')
    message = access_key + environment + str(time_stamp)
    message = message.encode('utf-8')
    sign = hmac.new(key, message, digestmod=sha256).hexdigest()
    str_sign = access_key + ":" + str(time_stamp) + ":" + sign.lower()
    result = quote(str_sign)
    return result


if __name__ == '__main__':
    # 测试环境
    signature = create_signature("dev", "oissgvxhcbeatm7yx14u9g7l5rip2ye6",
                                 "b7a5a24f8f4a01443e36d4ae7846f0dcd3a4b6d4078de92705c1bac911188fc3")

    # 生产
    # signature_prod = create_signature("prod", "5b33d571ae464910822a2bbf167e7f6e",
    #                         "36ba38e1d3f58f8b3c2d25d48a00b19b96f95e65dbe7f9b3f4ef921543578200")
