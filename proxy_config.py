import os
import urllib.request
import requests
from urllib3.util import connection

def configure_proxy_bypass():
    """配置代理绕过特定域名"""
    # 设置不使用代理的域名列表
    no_proxy_domains = [
        "*.zte.com.cn",
        "*.test.zte.com.cn", 
        "kms-msp.test.zte.com.cn",
        "studio.zte.com.cn",
        "llm.dev.zte.com.cn",
        "kger.zte.com.cn",
        "127.0.0.1",
        "localhost",
        "10.*"
    ]
    
    # 设置环境变量
    no_proxy_str = ",".join(no_proxy_domains)
    os.environ['NO_PROXY'] = no_proxy_str
    os.environ['no_proxy'] = no_proxy_str
    os.environ['HTTP_PROXY'] = ''
    os.environ['HTTPS_PROXY'] = ''
    os.environ['http_proxy'] = ''
    os.environ['https_proxy'] = ''
    
    # 为urllib设置代理处理器
    proxy_handler = urllib.request.ProxyHandler({
        'http': None,
        'https': None
    })
    
    # 创建opener并安装
    opener = urllib.request.build_opener(proxy_handler)
    urllib.request.install_opener(opener)
    
    # 配置requests库的默认session
    session = requests.Session()
    session.proxies = {
        'http': None,
        'https': None
    }
    
    # 将配置应用到requests的默认适配器
    requests.adapters.DEFAULT_RETRIES = 3
    
    # 禁用所有代理
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    print(f"已配置代理绕过域名: {no_proxy_str}")

# 在应用启动时调用
if __name__ == "__main__":
    configure_proxy_bypass()