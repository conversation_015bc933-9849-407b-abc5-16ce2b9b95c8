@startuml ZTE产品检索系统部署架构图
!theme plain
title ZTE产品检索系统 - 部署架构图

skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 11
skinparam nodeStyle rectangle

' 用户接入层
cloud "**用户接入层**" as UserAccessLayer {
    actor "业务用户" as BusinessUser
    actor "系统管理员" as SystemAdmin
    component "Web浏览器" as WebBrowser
    component "API客户端" as APIClient
}

' 负载均衡层
node "**负载均衡层**" as LoadBalancerLayer #E3F2FD {
    component "Nginx负载均衡器" as NginxLB #BBDEFB {
        [负载均衡]
        [SSL终端]
        [反向代理]
        [静态资源服务]
    }
    
    component "API网关" as APIGateway #BBDEFB {
        [路由管理]
        [限流控制]
        [监控统计]
        [安全防护]
    }
}

' 应用服务层
node "**应用服务集群**" as ApplicationCluster #FFF3E0 {
    node "应用服务器1" as AppServer1 #FFE0B2 {
        component "Flask应用实例1" as FlaskApp1 {
            [主应用 main.py]
            [控制器层]
            [业务逻辑层]
            [端口: 10023]
        }
    }
    
    node "应用服务器2" as AppServer2 #FFE0B2 {
        component "Flask应用实例2" as FlaskApp2 {
            [主应用 main.py]
            [控制器层]
            [业务逻辑层]
            [端口: 10023]
        }
    }
    
    node "应用服务器N" as AppServerN #FFE0B2 {
        component "Flask应用实例N" as FlaskAppN {
            [主应用 main.py]
            [控制器层]
            [业务逻辑层]
            [端口: 10023]
        }
    }
}

' 数据存储集群
node "**数据存储集群**" as DataStorageCluster #E8F5E8 {
    node "ElasticSearch集群" as ESCluster #C8E6C9 {
        database "ES主节点1" as ESMaster1 {
            [索引管理]
            [集群协调]
            [元数据存储]
        }
        
        database "ES数据节点1" as ESData1 {
            [文档存储]
            [索引分片]
            [查询处理]
        }
        
        database "ES数据节点2" as ESData2 {
            [文档存储]
            [索引分片]
            [查询处理]
        }
    }
    
    node "Milvus向量集群" as MilvusCluster #C8E6C9 {
        database "Milvus主节点" as MilvusMaster {
            [元数据管理]
            [集群协调]
            [负载均衡]
        }
        
        database "Milvus数据节点1" as MilvusData1 {
            [向量存储]
            [索引构建]
            [相似度检索]
        }
        
        database "Milvus数据节点2" as MilvusData2 {
            [向量存储]
            [索引构建]
            [相似度检索]
        }
    }
    
    node "Nebula图数据库集群" as NebulaCluster #C8E6C9 {
        database "Nebula Meta服务" as NebulaMeta {
            [元数据管理]
            [Schema管理]
            [集群管理]
        }
        
        database "Nebula Graph服务" as NebulaGraph {
            [图查询处理]
            [计算引擎]
            [查询优化]
        }
        
        database "Nebula Storage服务" as NebulaStorage {
            [图数据存储]
            [分布式存储]
            [数据分片]
        }
    }
}

' 外部服务层
cloud "**外部服务层**" as ExternalServiceLayer #F3E5F5 {
    node "ZTE AI服务" as ZTEAIServices #E1BEE7 {
        component "NebulaBiz LLM" as NebulaBizLLM {
            [大语言模型]
            [文本生成]
            [studio.zte.com.cn]
        }
        
        component "BGE-M3服务" as BGEM3Service {
            [文本向量化]
            [重排序模型]
            [llm.dev.zte.com.cn]
        }
        
        component "知识图谱API" as KGAPIService {
            [图谱查询]
            [实体识别]
            [kger.zte.com.cn]
        }
    }
    
    node "ZTE基础服务" as ZTEBaseServices #E1BEE7 {
        component "UAC认证中心" as UACService {
            [身份认证]
            [权限管理]
            [uactest.zte.com.cn]
        }
        
        component "Apollo配置中心" as ApolloService {
            [配置管理]
            [配置分发]
            [热更新]
        }
    }
}

' 监控运维层
node "**监控运维层**" as MonitoringLayer #FCE4EC {
    component "日志收集" as LogCollection #F8BBD9 {
        [ELK Stack]
        [日志聚合]
        [日志分析]
        [告警通知]
    }
    
    component "监控系统" as MonitoringSystem #F8BBD9 {
        [Prometheus]
        [Grafana仪表板]
        [性能监控]
        [健康检查]
    }
    
    component "运维管理" as OpsManagement #F8BBD9 {
        [自动化部署]
        [配置管理]
        [故障恢复]
        [容量规划]
    }
}

' 网络连接
BusinessUser --> WebBrowser : 访问
SystemAdmin --> APIClient : 管理
WebBrowser --> NginxLB : HTTPS请求
APIClient --> APIGateway : API调用

NginxLB --> FlaskApp1 : 负载分发
NginxLB --> FlaskApp2 : 负载分发
NginxLB --> FlaskAppN : 负载分发

APIGateway --> FlaskApp1 : API路由
APIGateway --> FlaskApp2 : API路由
APIGateway --> FlaskAppN : API路由

' 应用到数据库连接
FlaskApp1 --> ESCluster : 文本检索
FlaskApp1 --> MilvusCluster : 向量检索
FlaskApp1 --> NebulaCluster : 图谱查询

FlaskApp2 --> ESCluster : 文本检索
FlaskApp2 --> MilvusCluster : 向量检索
FlaskApp2 --> NebulaCluster : 图谱查询

FlaskAppN --> ESCluster : 文本检索
FlaskAppN --> MilvusCluster : 向量检索
FlaskAppN --> NebulaCluster : 图谱查询

' 外部服务连接
FlaskApp1 --> ZTEAIServices : AI服务调用
FlaskApp1 --> ZTEBaseServices : 基础服务调用

FlaskApp2 --> ZTEAIServices : AI服务调用
FlaskApp2 --> ZTEBaseServices : 基础服务调用

FlaskAppN --> ZTEAIServices : AI服务调用
FlaskAppN --> ZTEBaseServices : 基础服务调用

' 监控连接
ApplicationCluster --> LogCollection : 日志收集
DataStorageCluster --> MonitoringSystem : 性能监控
ExternalServiceLayer --> MonitoringSystem : 服务监控

' 部署环境说明
rectangle "**部署环境配置**" as DeploymentConfig #F5F5F5 {
    note as DeploymentNote
        **生产环境配置**
        
        **应用服务器配置**
        - CPU: 8核心
        - 内存: 16GB
        - 存储: 100GB SSD
        - 操作系统: CentOS 7.x
        - Python: 3.8+
        - 部署方式: Docker容器
        
        **数据库服务器配置**
        - ElasticSearch: 3节点集群，每节点32GB内存
        - Milvus: 2节点集群，每节点64GB内存，GPU加速
        - Nebula: 3节点集群，每节点16GB内存
        
        **网络配置**
        - 内网带宽: 10Gbps
        - 外网带宽: 1Gbps
        - 负载均衡: Nginx 1.18+
        - SSL证书: TLS 1.3
        
        **容器化部署**
        - 容器编排: Kubernetes
        - 镜像仓库: Harbor
        - 服务发现: Consul
        - 配置管理: Apollo
    end note
}

' 高可用配置
rectangle "**高可用架构**" as HighAvailabilityConfig #E0F2F1 {
    note as HANote
        **高可用保障**
        
        **应用层高可用**
        - 多实例部署(至少3个实例)
        - 负载均衡自动故障转移
        - 健康检查和自动重启
        - 滚动更新零停机部署
        
        **数据层高可用**
        - ES集群主从复制
        - Milvus数据分片和副本
        - Nebula集群分布式存储
        - 定期数据备份和恢复
        
        **网络层高可用**
        - 多机房部署
        - 跨可用区负载均衡
        - 网络冗余和故障切换
        - CDN加速和缓存
        
        **监控和告警**
        - 7x24小时监控
        - 多级告警机制
        - 自动故障恢复
        - 运维值班响应
    end note
}

@enduml
