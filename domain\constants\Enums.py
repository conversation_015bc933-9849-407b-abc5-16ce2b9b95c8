from enum import Enum

RRF_K = 50
SIM_THRESHOLD = 0.97

class Hints(Enum):
    ECCN_CODE_ERROR = "ECCN编码规则有误，请重新输入"
    LLM_TIMEOUT = "获取大模型结果超时"
    X_EMP_NO_EMPTY = "HTTP Header中X-Emp-No为空"
    ES_IDX_EMPTY = "es_index为空"
    VECTOR_KID_EMPTY = "vector_kid为空"
    LLM_AUTHORIZATION_EMPTY = "llm_autorization为空"
    VECTOR_AUTHORIZATION_EMPTY = "vector_autorization为空"
    MODEL_NAME_EMPTY = "model_name为空"
    QUESTION_EMPTY = "用户输入为空"
    TRANSLATOR_ERROR = "翻译接口报错"
    RERANKER_ERROR = "reranker模型报错"
    NO_CONTENT="目前知识库中没有答案，请礼貌的回答没有知识"
    EMP_ERROR="请在headers中输入工号X-Emp-No"
    ES_TIMEOUT="获取es结果超时"
    PRODUCT_VERSION_LIST = ['V6000']
    NOT_EXIST="无"
    FOU_CN = "否"

class SOURCE(Enum):
    DOC_EMPTY="无"
    DOC_FROM="文档来源:"
    DOC_ID="文档id:"
    PRODUCT_NUMBER="产品号"
    FROM="来源"
    QUESTION="问题"
    YXXBK_FROM="小百科来源"
    WU_CN="无"
    REF_DOCUMENT="参考文档"
    YXXBK_ANSWER = "答案"


class CONST(Enum):
    EAR99 = 'EAR99'
    Timeout = 60
class Targets(Enum):
    FAQ='faq'
    ZXBK = 'zxbk'
    NOREFER='norefer'
    ZXBK18='zxbk18'
    ZXBK_EN = 'zxbk_en'
    ECCN = 'eccn'
    COO = 'coo'
    SIM = 'sim'
    REWRITE = 'rewrite'
    RELATE = 'relate'
    MULTI = 'multi'
    DIGIMAN = 'digman'
    DIGIMAN_EN = 'digman_en'
    ZXTECH='zxtech'
    ZXTECH_with_Exclusive='zetech_with_exclusive'
    ZXTECH_with_Exclusive_Self='zetech_with_exclusive_self'

class Thread_ID(Enum):
    ES = 'ES'
    VECTOR = '向量'

class ECCN_Flag(Enum):
    ZXBK = 0
    CODE_CORRECT = 1
    CODE_ERROR = 2

class ABBREVIATION(Enum):
    ECSS="ECSS"
    ECP="ECP"
    GDPR="GDPR"
    RPA="RPA"
    LCM="LCM"
    ECP8="ECP8"
    ITEM="ITEM"
    CCL="CCL"
    ECCN="ECCN"
    GTS="GTS"
    CCATS="CCATS"
    CTO="CTO"
    CEO="CEO"
    CFO="CFO"
    DT="DT"
    SCC="SCC"
    EAR="EAR"
    HPPD="HPPD"
    ZTE="ZTE"
    SDN="SDN"
    SRM="SRM"
    ERL="ERL"
    ECG="ECG"
    SPL="SPL"
    ECC="ECC"
    ZSPL="ZSPL"
    UVL="UVL"
    GCCL="GCCL"
    DM25="DM25"
    DM10="DM10"
    BOM="BOM"
    Enabled="ENABLE"
    CDC="CDC"
    ECPOC="ECPOC"

class StringType(Enum):
    MIX = "Mix"
    CH = "Chinese"
    EN = "English"
    OTHER = "Other"

class StringType_Response(Enum):
    Chinese = "请根据参考文本，用中文回答用户问题:"
    English = "请根据参考文本，用英文回答用户问题:"
    Other = "请根据参考文本，用中文回答用户问题:"

class RelatedResult(Enum):
    MULTI = "追问"
    SINGLE = "单轮"

class Rewrite(Enum):
    FILTER = "**参考文档**"

class PATTERNEnum(Enum):
    REWRITE_PATTERN = r'重写是：(.*)'
    GRAYLIST_PATTERN = r'灰名单'
    PUNCTUATION_PATTERN = r'[吗呢么嘛不啊|?？！!。]'
    COMPANY_PATTERN = r'中兴|我司|我们公司|我们|ZTE'
    COOPERATE_PATTERN = r'(合作|交流|合作伙伴|可以|违规|合规|违例)$'
    MEET_REQUIREMENTS_COOPERATE_PATTERN = r'(符合|满足|违反|)(.*)(规则|要求|条件|规定)$'
    NUMBER_ZH_PATTERN = r'[零一二三四五六七八九]+'