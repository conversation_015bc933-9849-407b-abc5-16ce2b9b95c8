"""
测试监控数据更新
直接调用service层的方法，绕过HTTP层
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from domain.config.zxtech_config import config
from service.zxtech_service import _ZXTECH_
from monitor.recall_monitor import get_monitor
import time

def test_monitor_update():
    """测试监控是否正常更新"""
    
    print("=" * 60)
    print("           测试监控数据更新")
    print("=" * 60)
    
    # 获取监控实例
    monitor = get_monitor()
    
    # 记录初始查询数
    initial_count = monitor.query_counter
    print(f"\n初始查询数: {initial_count}")
    
    try:
        # 创建服务实例
        print("\n正在初始化服务...")
        service = _ZXTECH_(config)
        print("服务初始化成功")
        
        # 测试查询
        test_queries = [
            "M6000-8S Plus的设备尺寸",
            "如何配置VLAN",
            "系统日志查看方法"
        ]
        
        print("\n开始测试查询...")
        for i, query in enumerate(test_queries, 1):
            print(f"\n[{i}/{len(test_queries)}] 查询: {query}")
            
            try:
                # 调用服务（模拟FAQ接口调用）
                # 注意：这里可能会因为缺少某些参数而失败
                # 我们主要是测试监控功能
                
                # 直接模拟监控记录
                es_results = [
                    {'content': f'ES结果{j}', 'doc_name': f'doc{j}', 'id': f'es_{j}'}
                    for j in range(10)
                ]
                milvus_results = []  # Milvus已禁用
                kg_results = []
                if i == 2:  # 第二个查询模拟有KG结果
                    kg_results = ["KG知识图谱结果"]
                
                rerank_results = es_results[:5]
                if kg_results:
                    rerank_results.insert(0, {
                        'content': kg_results[0],
                        'doc_name': 'kg_doc',
                        'id': 'kg_1'
                    })
                
                # 记录到监控
                query_id = monitor.record_query(
                    query=query,
                    es_results=es_results,
                    milvus_results=milvus_results,
                    kg_results=kg_results,
                    rerank_results=rerank_results,
                    response_time=0.2
                )
                
                print(f"  [OK] 监控记录成功，查询ID: {query_id}")
                
            except Exception as e:
                print(f"  [ERROR] 查询失败: {e}")
        
        # 检查监控更新
        print("\n" + "=" * 60)
        print("监控数据更新情况")
        print("=" * 60)
        
        new_count = monitor.query_counter
        print(f"当前查询数: {new_count}")
        print(f"新增查询数: {new_count - initial_count}")
        
        if new_count > initial_count:
            print("\n[SUCCESS] 监控数据更新成功！")
            
            # 显示排行榜
            print("\n当前排行榜：")
            print("-" * 40)
            for idx, item in enumerate(monitor.leaderboard[:3], 1):
                print(f"{idx}. {item['source']}: 贡献率{item['contribution_rate']:.1f}%, 得分{item['total_score']:.2f}")
            
            # 保存数据
            print("\n正在保存监控数据...")
            monitor._save_history()
            print("数据已保存到 monitor_data/ 目录")
            
        else:
            print("\n[FAILED] 监控数据未更新")
            
    except Exception as e:
        print(f"\n错误: {e}")
        import traceback
        traceback.print_exc()

def check_monitor_files():
    """检查监控文件"""
    from pathlib import Path
    
    print("\n" + "=" * 60)
    print("监控文件检查")
    print("=" * 60)
    
    monitor_dir = Path("monitor_data")
    if monitor_dir.exists():
        files = list(monitor_dir.glob("*"))
        print(f"\n找到 {len(files)} 个监控文件：")
        for f in sorted(files):
            size = f.stat().st_size / 1024
            print(f"  - {f.name} ({size:.1f}KB)")
    else:
        print("监控目录不存在")

if __name__ == "__main__":
    # 测试监控更新
    test_monitor_update()
    
    # 检查文件
    check_monitor_files()
    
    print("\n测试完成！")