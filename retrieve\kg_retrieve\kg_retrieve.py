import requests
from utils.http_util import post_no_proxy
from utils.utils import replace_keys
from utils.logger.logger_util import logger
from .generate_signature import create_signature
from domain.constants.infoGo_constants import translate_dict


class RecallResult_kg(object):
    def __init__(self, config: dict):
        self.kg_space = config['kg']['space']
        self.kg_url = config['kg']['kg_url']
        self.access_environment = config['Knowledge']['access_environment']
        self.access_key = config['Knowledge']['access_key']
        self.access_secret = config['Knowledge']['access_secret']
        self.systemName = config['Knowledge']['es_headers']['systemName']

    def body_relation_doc_pid(self, bodyids):

        bo = {
            "space": self.kg_space,
            "nql": f"GO FROM '{bodyids}' OVER product_document_relation YIELD dst(edge) as destination | fetch prop on * $-.destination YIELD vertex as vertices_;"
        }

        try:
            receive = post_no_proxy(
                json=bo,
                url=self.kg_url,
                verify=True,
                headers={"systemName": self.systemName,
                         'X-Signature': create_signature(self.access_environment, self.access_key, self.access_secret)
                         }
            )
            result = receive.json()['bo']
            if result == []:
                return None
            res = {}
            res[id] = []
            for i in result:
                label = i['vertices_']['vertexLabel']
                if label == "text" or label == "document" or label == "table":
                    res[id].append(
                        {"documentID": i['vertices_']['id'], "documentName": i['vertices_']['properties']['content']})
            return res
        except Exception as e:
            logger.info("实体关联文档型号出错")
            return None

    def body_relation_doc_sid(self, bodyids):

        bo = {
            "space": self.kg_space,
            "nql": f"GO FROM '{bodyids}' OVER series_document_relation YIELD dst(edge) as destination | fetch prop on * $-.destination YIELD vertex as vertices_;"
        }
        try:
            receive = post_no_proxy(
                json=bo,
                url=self.kg_url,
                verify=True,
                headers={"systemName": self.systemName,
                         'X-Signature': create_signature(self.access_environment, self.access_key, self.access_secret)
                         }
            )
            result = receive.json()['bo']
            if result == []:
                return None
            res = {}
            res[id] = []
            for i in result:
                label = i['vertices_']['vertexLabel']
                if label == "text" or label == "document" or label == "table":
                    res[id].append(
                        {"documentID": i['vertices_']['id'], "documentName": i['vertices_']['properties']['content']})
            return res
        except Exception as e:
            logger.info("实体关联文档系列出错")
            return None

    def body_relation_doc_version(self,bodyids,version,series_flag):
        # 获取系列下关联文档
        if series_flag:
            nql = f"MATCH (p:series)-[:series_version_relation]->(pv:product_version)-[:productversion_document_relation]->(pd:document) WHERE id(p) == '{bodyids}' and pv.product_version.version CONTAINS '{version}'  RETURN pd;"
        # 获取产品下关联文档
        else:
            nql = f"MATCH (p:product)-[:product_version_relation]->(pv:product_version)-[:productversion_document_relation]->(pd:document) WHERE id(p) == '{bodyids}' and pv.product_version.version CONTAINS '{version}'  RETURN pd;"
        bo = {
            "space": self.kg_space,
            "nql": nql
        }
        try:
            receive = post_no_proxy(
                json=bo,
                url=self.kg_url,
                verify=True,
                headers={"systemName": self.systemName,
                         'X-Signature': create_signature(self.access_environment, self.access_key, self.access_secret)
                         }
            )
            result = receive.json()['bo']
            if result == []:
                return None
            res = {}
            res[id] = []
            for i in result:
                label = i['pd']['vertexLabel']
                if label == "text" or label == "document" or label == "table":
                    res[id].append(
                        {"documentID": i['pd']['id'], "documentName": i['pd']['properties']['content']})
            return res
        except Exception as e:
            logger.info("实体关联系列版本文档出错")
            return None

    def query_data_kg(self, nql):

        bo = {
            "space": self.kg_space,
            "nql": nql
        }

        try:
            receive = post_no_proxy(
                json=bo,
                url=self.kg_url,
                verify=True,
                headers={"systemName": self.systemName,
                         'X-Signature': create_signature(self.access_environment, self.access_key, self.access_secret)
                         }
            )
            result = receive.json()['bo']
            if result == []:
                return None

            res = []
            for i in result:
                res.append(i['v']['properties'])
            res = [replace_keys(d, translate_dict()) for d in res]

            return res
        except Exception as e:
            return None

    def find_position(self, combo):
        # 原始元组
        all_res = []
        for i in combo:
            key = i.split('.')[0]
            shux = i.split('.')[1]
            value = combo[i]
            # 根据键值对和实体名，构造单实例查询的nql来查询知识图谱
            nql = f"MATCH (v:{key}) WHERE v.{i}=='{value}' RETURN v LIMIT 5"
            res_str = self.query_data_kg(nql)
            if res_str:
                all_res += res_str
        return {'key': combo,
                'result': all_res}
