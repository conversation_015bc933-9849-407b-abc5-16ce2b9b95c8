import requests
from utils.http_util import post_no_proxy
import sys
from domain.config.zxtech_config import config

sys.path.append("..")
from utils.generate_signature import create_signature
from utils.logger.logger_util import logger
from concurrent.futures import ThreadPoolExecutor
import json
from embedding.get_embedding import GetEmbedding
from retrieve.kg_retrieve.kg_retrieve import RecallResult_kg
from utils.infoGo_util import sort_dict
from utils.es_util.es_util import deal_results_from_kg
from domain.constants.Enums import Hints
getembedding = GetEmbedding(config)
kg_recall = RecallResult_kg(config)


class RecallResult(object):
    def __init__(self, config):
        self.real_environment=config['Enviroment']
        self.es_url = config['Knowledge']['es_url']
        self.index = config['Knowledge']['index']
        self.index_xbk=config['Knowledge']['index_xbk']
        self.user = config['Knowledge']['user']
        self.type = config['Knowledge']['type1']
        self.es_headers = config['Knowledge']['es_headers']
        self.ner_index = config['Knowledge']['ner_index']
        self.doc_file_info_index = config['Knowledge']['doc_file_info_index']
        # ES string constant
        self.environment = config['Knowledge']['access_environment']
        self.access_key = config['Knowledge']['access_key']
        self.access_secret = config['Knowledge']['access_secret']

        # kg
        self.kg_url = config['kg']['kg_url']
        self.space = config['kg']['space']

    def query_data_es(self, query):
        input_data = {
            "indexName": [self.index] if self.real_environment=='dev' else [self.index,self.index_xbk],
            "matchShouldQueryConditions": {query: self.type},
            "needQuery": True,
            "pageNo": 1,
            "pageSize": 20
        }
        self.es_headers['X-Signature'] = create_signature(self.environment, self.access_key, self.access_secret)
        try:
            req = post_no_proxy(url=self.es_url, data=json.dumps(input_data), headers=self.es_headers)
            req = req.content.decode('utf-8')
            list_es = json.loads(req)['mapResults']
            return list_es
        except:
            return []

    def entity_search_es(self, jsonObject):
        self.es_headers['X-Signature'] = create_signature(self.environment, self.access_key, self.access_secret)
        try:
            response = post_no_proxy(self.es_url, headers=self.es_headers, json=jsonObject)
            result = response.json()
            resultJsonArray = result['mapResults']
            return resultJsonArray
        except Exception as e:
            logger.error("实体提取出错！")

    def get_kg_result_with_full_es_link(self, query,isInFoGo=False):
        if not isInFoGo:
            logger.info(f'{query}中未包含infoGo中涉及的产品型号')
            return [], [], {}
        es_link_result, es_searchText, kg_dict = self.get_entity(query)
        if es_link_result:
            with ThreadPoolExecutor(max_workers=10) as executor:
                # 将每个元素作为任务提交到线程池
                results = list(executor.map(kg_recall.find_position, es_link_result))

            rs = []
            for item in results:
                # 将result字段添加到results列表
                rs.append(item['result'])

            map_dict = {}
            for key in kg_dict:
                value = kg_dict[key]
                filtered_dicts = [d for d in results if d.get('key') == value]
                if filtered_dicts[0]['result']:
                    new_value = filtered_dicts[0]['result'][0]
                    map_dict[key] = new_value

            unique_data=deal_results_from_kg(rs)
            results = unique_data
            result_str = ''
            for dict_temt in results:
                str = json.dumps(dict_temt, ensure_ascii=False)
                result_str += str

            if not results:
                logger.info("您好，知识图谱中无相关知识")
                return [], es_searchText, map_dict
            return [result_str], es_searchText, map_dict
        else:
            logger.info('KG查询无结果')
            return [], [], {}

    def get_entity(self, question):
        res = self.query_es_ner(question)
        # es_searchText = [f'{i + 1}' + '.' + j['searchText']+'\n' for i, j in enumerate(res)]
        having = set()
        data = []

        entity_type_dict = {}
        for i in res:
            search = i['searchText']
            belongs = i['belongs']
            entity_type = i['from']
            display_name = i['displayName']
            key = entity_type + '.' + belongs
            if belongs == "":
                key = entity_type
            value = key + display_name
            if value in having:
                continue
            else:
                entity_dict = {key: display_name}
                data.append(entity_dict)
                having.add(value)

            entity_list = {}
            if entity_type in entity_type_dict.keys():
                entity_list = entity_type_dict.get(entity_type)
            entity_list[search] = entity_dict
            entity_type_dict[entity_type] = entity_list

        result = []
        es_searchText = []
        # 字典用于溯源
        my_dict = {}
        for entity_key in entity_type_dict.keys():
            result_arr, es_top_searchText = sort_dict(entity_type_dict.get(entity_key), question)
            es_searchText+=es_top_searchText
            for j in result_arr:
                result.append(j)
                my_dict[es_top_searchText[0]] = j
        return result, es_searchText, my_dict

    def query_es_ner(self, query):
        query_body = {
            "indexName": [self.ner_index],
            "matchMustQueryConditions": {
                f'''{query}''': ["searchText", "displayName", "id", "combineId", "termSearch"]
            },
            "needQuery": True,
            "pageNo": 1,
            "pageSize": 50
        }
        self.es_headers['X-Signature'] = create_signature(self.environment, self.access_key, self.access_secret)
        try:
            receive = post_no_proxy(
                headers=self.es_headers,
                json=query_body,
                url=self.es_url,
                timeout=60,
                stream=False,
                verify=True
            )
            receive = receive.content.decode('utf-8')
            list_es = json.loads(receive)['mapResults']
            return list_es
        except requests.exceptions.Timeout:
            return Hints.ES_TIMEOUT.value

    def query_must_match(self, query):
        bo = {
            "indexName": [
                self.doc_file_info_index
            ],
            "termsMustQueryConditions": {
                "doc_code": query
            },
            "needQuery": True,
            "pageNo": 1,
            "pageSize": 10
        }
        self.es_headers['X-Signature'] = create_signature(self.environment, self.access_key, self.access_secret)
        try:
            req = post_no_proxy(url=self.es_url, data=json.dumps(bo), headers=self.es_headers)
            req = req.content.decode('utf-8')
            list_es = json.loads(req)['mapResults']
            return list_es
        except:
            return []
    def query_must_match_one(self, query):
        bo = {
            "indexName": [
                self.doc_file_info_index
            ],
            "termsMustQueryConditions": {
                "doc_code": [query]
            },
            "needQuery": True,
            "pageNo": 1,
            "pageSize": 10
        }
        self.es_headers['X-Signature'] = create_signature(self.environment, self.access_key, self.access_secret)
        try:
            req = post_no_proxy(url=self.es_url, data=json.dumps(bo), headers=self.es_headers)
            req = req.content.decode('utf-8')
            list_es = json.loads(req)['mapResults']
            return list_es[0]
        except:
            return []

    def query_data_fuzzyandprecise(self, doc_name_list, content):
        bo = {
            "indexName": [
                self.index
            ],
            "termsMustQueryConditions": {
                "doc_name": doc_name_list
            },
            "matchMustQueryConditions": {
                content: ["content"]
            },
            "needQuery": True,
            "pageNo": 1,
            "pageSize": 20
        }
        self.es_headers['X-Signature'] = create_signature(self.environment, self.access_key, self.access_secret)
        try:
            req = post_no_proxy(url=self.es_url, data=json.dumps(bo), headers=self.es_headers)
            req = req.content.decode('utf-8')
            list_es = json.loads(req)['mapResults']

            return list_es
        except Exception as e:
            logger.info("精确+模糊匹配出错")
            return []
