import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class NoProxyHTTPAdapter(HTTPAdapter):
    """不使用代理的HTTP适配器"""
    def send(self, request, **kwargs):
        # 强制不使用代理
        kwargs['proxies'] = {'http': None, 'https': None}
        return super().send(request, **kwargs)

def create_no_proxy_session():
    """创建一个不使用代理的requests session"""
    session = requests.Session()
    
    # 设置重试策略
    retry_strategy = Retry(
        total=3,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
    )
    
    # 使用不代理的适配器
    adapter = NoProxyHTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    # 确保不使用代理
    session.proxies = {'http': None, 'https': None}
    
    return session

# 创建全局session
no_proxy_session = create_no_proxy_session()

def post_no_proxy(url, **kwargs):
    """不使用代理的POST请求"""
    return no_proxy_session.post(url, **kwargs)

def get_no_proxy(url, **kwargs):
    """不使用代理的GET请求"""
    return no_proxy_session.get(url, **kwargs)

def put_no_proxy(url, **kwargs):
    """不使用代理的PUT请求"""
    return no_proxy_session.put(url, **kwargs)

def delete_no_proxy(url, **kwargs):
    """不使用代理的DELETE请求"""
    return no_proxy_session.delete(url, **kwargs)